{"name": "antd-pro-vue3-1.0", "version": "1.0.0", "private": true, "scripts": {"serve": "NODE_OPTIONS='--openssl-legacy-provider' vue-cli-service serve", "build": "NODE_OPTIONS='--openssl-legacy-provider' vue-cli-service build", "lint": "vue-cli-service lint", "deploy": "start ./deploy.sh", "clear": "rimraf node_modules && npm install --registry=https://registry.npm.taobao.org", "eslint": "eslint --fix --ext .js,.vue,.ts, src", "update": "ncu -u --target greatest && npm install --registry=https://registry.npm.taobao.org", "use:npm": "nrm use npm", "use:taobao": "nrm use taobao"}, "dependencies": {"@antv/g2plot": "^2.4.19", "@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.3.2", "@surely-vue/table": "^2.4.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ali-oss": "^6.18.0", "ant-design-vue": "^4.2.1", "axios": "^0.21.1", "babel-loader": "^7.1.5", "clipboard": "^2.0.8", "dayjs": "^1.10.6", "docx-preview": "^0.3.6", "dom7": "^4.0.6", "element-plus": "^2.11.3", "fjf-one": "^1.0.9", "html2canvas": "^1.4.1", "html2pdf.js": "^0.12.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.0-rc.3", "jsencrypt": "^3.3.2", "katex": "^0.16.11", "lint-staged": "^13.1.2", "mathjax": "^3.2.2", "mitt": "^3.0.1", "mockjs": "^1.1.0", "moment": "^2.29.3", "pdfjs-dist": "^5.4.149", "register-service-worker": "^1.7.1", "remixicon": "^2.5.0", "slate": "^0.103.0", "snabbdom": "^3.6.2", "vue": "^3.2.36", "vue-class-component": "^8.0.0-0", "vue-request": "^2.0.4", "vue-router": "^4.0.10", "vue3-count-to": "^1.1.2", "vuex": "^4.0.2", "xml-formatter": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.20.12", "@types/node": "^18.13.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "^4.5.9", "@vue/cli-plugin-eslint": "^4.5.9", "@vue/cli-plugin-typescript": "^4.5.15", "@vue/cli-service": "^4.5.9", "@vue/compiler-sfc": "^3.1.4", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-core": "^6.26.3", "babel-eslint": "^11.0.0-beta.2", "babel-plugin-transform-remove-console": "^6.9.4", "body-parser": "^1.19.0", "chalk": "^4.1.1", "chokidar": "^3.5.2", "compression-webpack-plugin": "6.1.1", "core-js": "^3.22.8", "eslint": "^7.30.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^8.4.0", "filemanager-webpack-plugin": "^6.1.4", "hls.js": "^1.5.11", "jquery": "^3.6.3", "less": "^4.1.1", "less-loader": "^7.3.0", "lodash-es": "^4.17.21", "optimize-css-assets-webpack-plugin": "^6.0.1", "path": "^0.12.7", "prettier": "^2.3.2", "qs": "^6.11.0", "sass": "^1.79.0", "sass-loader": "^8.0.2", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.4.0", "svg-sprite-loader": "^6.0.9", "terser-webpack-plugin": "4.2.3", "typescript": "^4.4.2", "uuid": "^9.0.1", "webpackbar": "^5.0.0-3", "xlsx": "^0.18.5"}, "lint-staged": {"*.{js,jsx,vue,ts}": ["vue-cli-service lint", "git add"]}}