<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.svg" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <meta content="<%= VUE_APP_AUTHOR %>" name="author" />
    <link href="<%= BASE_URL %>static/css/loading.css" rel="stylesheet" />
  </head>
  <body>
    <script src="<%= BASE_URL %>lib/es6-promise.min.js"></script>
    <script src="<%= BASE_URL %>lib/aliyun-oss-sdk-6.17.1.min.js"></script>
    <script src="<%= BASE_URL %>aliyun-upload-sdk-1.5.7.min.js"></script>
    <!-- 识别单行，行内，\( \)样式的公式 -->
    <!-- <script>
      window.MathJax = {
        tex: {
          inlineMath: [
            ['$', '$'],
            ['\\(', '\\)'],
          ], // 行内公式选择符
          displayMath: [
            ['$$', '$$'],
            ['\\[', '\\]'],
          ], // 段内公式选择符
        },
        startup: {
          ready() {
            MathJax.startup.defaultReady()
          },
        },
      }
    </script>
    <script
      id="MathJax-script"
      async
      src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-chtml.js"
    ></script> -->
    <noscript>
      <strong>
        We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to continue.
      </strong>
    </noscript>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <h1><%= VUE_APP_TITLE %></h1>
      </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
