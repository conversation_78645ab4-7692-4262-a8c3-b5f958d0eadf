import dayjs from 'dayjs'
import moment from 'moment'

import { dateFormat } from '@/config'

/**
 * 大于今天的日期
 * @param {*} current
 * @returns
 */
export function greaterThanToday(current: any) {
  return current && current > dayjs().endOf('day')
}

/**
 * 返回前s月的一号
 * @param {number} s 月份 负数
 * @returns
 */
export function startTime(s: any) {
  return dayjs(moment().add(s, 'months').startOf('month').format(dateFormat))
}

/**
 * 返回前s月的最后一天
 * @param {number} s 月份 负数
 * @returns
 */
export function endTime(s: any) {
  return dayjs(moment().add(s, 'months').endOf('month').format(dateFormat))
}

/**
 * 返回今天到前n天之内的日期
 * @param {*} current
 * @param {*} n 前n天
 * @returns
 */
export function todayToLastnDay(current: any, n: number) {
  return current && current <= moment().subtract(n, 'days')
}

/**
 * 当天时间
 */
const newTime = dayjs(moment().format(dateFormat))

const yesterday = dayjs().subtract(1, 'day')
/**
 * 获取时间预设范围
 */
const ranges = {
  本月: [startTime(0), dayjs(moment().format(dateFormat))],
  一个月: [startTime(-1), dayjs(moment().format(dateFormat))],
  二个月: [startTime(-2), dayjs(moment().format(dateFormat))],
  三个月: [startTime(-3), dayjs(moment().format(dateFormat))],
  六个月: [startTime(-6), dayjs(moment().format(dateFormat))],
  十二个月: [startTime(-12), dayjs(moment().format(dateFormat))],
}
export { newTime, ranges, yesterday }
