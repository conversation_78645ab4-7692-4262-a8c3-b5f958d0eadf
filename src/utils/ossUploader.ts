import OSS from 'ali-oss'

import { getOSS } from '@/api/user'
import { dateFormat } from '@/utils/index'
import { generateFileName } from '@/utils/uuid'

const fileTypeSuffix: { [key: string]: string } = {
  'audio/wav': 'wav',
  'audio/mpeg': 'mp3',
  'audio/ogg': 'ogg',
  'audio/flac': 'flac',
  'audio/aac': 'aac',

  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/bmp': 'bmp',
  'image/tiff': 'tiff',

  'video/mp4': 'mp4',
  'video/x-msvideo': 'avi',
  'video/x-matroska': 'mkv',
  'video/quicktime': 'mov',

  'application/pdf': 'pdf',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'docx',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/vnd.ms-powerpoint': 'ppt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    'pptx',

  'text/plain': 'txt',
  'text/html': 'html',
  'text/css': 'css',
  'text/javascript': 'js',
}

export const uploadToOSS = async (
  fileList: any,
  onSuccess?: any,
  onError?: any
) => {
  try {
    const { data } = await getOSS()
    const client = new OSS({
      bucket: 'edu-lewei-1',
      region: 'oss-cn-wuhan-lr',
      secure: true,
      accessKeyId: data.Credentials.AccessKeyId,
      accessKeySecret: data.Credentials.AccessKeySecret,
      stsToken: data.Credentials.SecurityToken,
      refreshSTSToken: async () => {
        const { data } = await getOSS()
        return {
          accessKeyId: data.Credentials.AccessKeyId,
          accessKeySecret: data.Credentials.AccessKeySecret,
          stsToken: data.Credentials.SecurityToken,
        }
      },
      refreshSTSTokenInterval: 60 * 1000 * 60,
    })

    const file = fileList[0].originFileObj
    const fileType = fileTypeSuffix[file.type] || 'unknown'
    const orderColumn = dateFormat(new Date())
    const fileName = generateFileName(orderColumn, fileType)
    const result = await client.put(fileName, file)

    if (result.res.status === 200) {
      if (onSuccess) onSuccess(result, file)
    } else {
      if (onError) onError(result)
    }
  } catch (error) {
    if (onError) onError(error)
  }
}
