import path from 'path'
import { LocationQueryRaw } from 'vue-router'

import { rolesControl } from '@/config'
import router from '@/router'
import { hasRole } from '@/utils/hasRole'
import { paramObj } from '@/utils/index'
import { isExternal } from '@/utils/validate'
import { VabRouteRecord } from '#/router'

// import {
//   useRouter,
//   // useRoute
// } from 'vue-router'
// import { getAccessToken } from '@/utils/accessToken'
//import state from '@/store/modules/user'

/**
 * <AUTHOR> @description all模式渲染后端返回路由
 * @param constantRoutes
 * @returns {*}
 */

export function convertRouter(constantRoutes: VabRouteRecord[]) {
  return constantRoutes.map((route: any) => {
    if (route.component) {
      if (route.component === 'Layout') {
        const path = 'layouts'
        route.component = (resolve: (...modules: any[]) => void) =>
          require([`@/${path}`], resolve)
      } else {
        let path = 'views/' + route.component
        if (
          new RegExp('^/views/.*$').test(route.component) ||
          new RegExp('^views/.*$').test(route.component)
        ) {
          path = route.component
        } else if (new RegExp('^/.*$').test(route.component)) {
          path = 'views' + route.component
        } else if (new RegExp('^@views/.*$').test(route.component)) {
          path = route.component.slice(1)
        } else {
          path = 'views/' + route.component
        }
        route.component = (resolve: (...modules: any[]) => void) =>
          require([`@/${path}`], resolve)
      }
    }
    if (route.children)
      route.children.length
        ? (route.children = convertRouter(route.children))
        : delete route.children

    return route
  })
}

/**
 * <AUTHOR> @description 根据roles数组拦截路由
 * @param routes
 * @param baseUrl
 * @returns {[]}
 */
export function filterRoutes(routes: VabRouteRecord[], baseUrl = '/') {
  return routes
    .filter((route: VabRouteRecord) => {
      //管理员账号
      // if (getAccessToken() === '15158305800') {
      //   return true
      // }
      // if (route?.meta?.roles) {
      //   return false
      // } else {
      //   return true
      // }
      if (route.meta && route.meta.roles)
        return !rolesControl || hasRole(route.meta.roles)
      return true
    })
    .map((route: any) => {
      if (route.path !== '*' && !isExternal(route.path))
        route.path = path.resolve(baseUrl, route.path)
      route.fullPath = route.path
      if (route.children && route.children.length > 0)
        route.children = filterRoutes(route.children, route.fullPath)
      return route
    })
}

/**
 * 根据当前页面firstMenu
 * @returns {string}
 */
export function handleFirstMenu() {
  // const firstMenu = router.currentRoute.matched[0].path
  // if (firstMenu === '') return '/'
  // return firstMenu
}

/**
 *
 * @param {*} name 路由名称
 * @param {*} record 传递参数
 */
export function pushWithQuery(name: any, record: LocationQueryRaw | undefined) {
  router.push({
    name,
    query: {
      ...record,
    },
  })
}

export function getUrlParam(
  param: { [s: string]: unknown } | ArrayLike<unknown>
) {
  let txt: any[] = []
  for (const [key, value] of Object.entries(param)) {
    if (value) txt = [...txt, `${key}=${value}`]
  }
  return txt.join('&')
}

export function routerReplace(
  pagination: { current: number; pageSize: number },
  params: any
) {
  router.replace(
    `${router.currentRoute.value.path}?${getUrlParam(
      Object.assign(paramObj(location.href), {
        current: pagination.current,
        pageSize: pagination.pageSize,
        ...params,
      })
    )}`
  )
}

export function routerReplaceNoPagi(params: any) {
  router.replace(
    `${router.currentRoute.value.path}?${getUrlParam(
      Object.assign(paramObj(location.href), {
        ...params,
      })
    )}`
  )
}

export function routerBack() {
  router.back()
}

/**
 * 清空地址URL
 */
export function routerReset() {
  router.replace(router.currentRoute.value.path)
}
