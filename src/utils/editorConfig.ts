import OSS from 'ali-oss'
import { message } from 'ant-design-vue'

import { getOSS } from '@/api/user'
import { dateFormat } from '@/utils'
import { generateFileName } from '@/utils/uuid'

// 可上传文件类型
const fileTypeSuffix: any = {
  'image/png': 'png',
  'image/jpeg': 'jpg',
}
const editorConfig = {
  MENU_CONF: {
    uploadImage: {
      // server: 'https://api.leweijg.com', // 自定义插入图片
      async customUpload(file: any, insertFn: any) {
        const { data } = await getOSS()
        const client = new OSS({
          // 填写Bucket名称。
          bucket: 'edu-lewei-que',
          // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
          region: 'oss-cn-wuhan-lr',
          secure: true,
          // endpoint: 'https',
          // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
          accessKeyId: data.Credentials.AccessKeyId,
          accessKeySecret: data.Credentials.AccessKeySecret,
          // 从STS服务获取的安全令牌（SecurityToken）。
          stsToken: data.Credentials.SecurityToken,
          refreshSTSToken: async () => {
            // 向您搭建的STS服务获取临时访问凭证。
            const { data } = await getOSS()
            return {
              accessKeyId: data.Credentials.accessKeyId,
              accessKeySecret: data.Credentials.accessKeySecret,
              stsToken: data.Credentials.stsToken,
            }
          },
          // 刷新临时访问凭证的时间间隔，单位为毫秒。
          refreshSTSTokenInterval: 60 * 1000 * 60,
        })
        const fileType = fileTypeSuffix[file.type]
        const orderColumn = dateFormat(new Date())
        const fileName = generateFileName(orderColumn, fileType)
        const result = await client.put(fileName, file)
        console.log(result)
        if (result.res.status == 200) {
          message.success('上传成功')
          insertFn(result.url)
        }
      },
    },
  },
  placeholder: '请输入内容...', // 选中公式时的悬浮菜单
}
export default editorConfig
