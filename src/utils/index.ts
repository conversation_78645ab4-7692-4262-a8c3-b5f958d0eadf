/**
 * <AUTHOR> @description 格式化时间
 * @param time
 * @param cFormat
 * @returns {string|null}
 */
export function parseTime(time: string | number | Date, cFormat: string) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj: any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  return format.replace(
    /{([ymdhisa])+}/g,
    (result: string | any[], key: string) => {
      let value = formatObj[key]
      if (key === 'a') {
        return ['日', '一', '二', '三', '四', '五', '六'][value]
      }
      if (result.length > 0 && value < 10) {
        value = '0' + value
      }
      return value || 0
    }
  )
}

/**
 * <AUTHOR> @description 格式化时间
 * @param time
 * @param option
 * @returns {string}
 */
export function formatTime(time: any | number | Date, option: any) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d: any = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  }
  return (
    d.getMonth() +
    1 +
    '月' +
    d.getDate() +
    '日' +
    d.getHours() +
    '时' +
    d.getMinutes() +
    '分'
  )
}

/**
 * <AUTHOR> @description 将url请求参数转为json格式
 * @param url
 * @returns {{}|any}
 */
export function paramObj(url: string) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
  )
}

/**
 * <AUTHOR> @description 将url请求参数转为json格式
 * @param url
 * @returns {{}|any}
 */
export function urlToParam(url: string) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  const obj: any = {}
  const tmp = search.split('&')
  tmp.map((item) => {
    const key = item.split('=')[0]
    const value = decodeURIComponent(item.split('=')[1])
    obj[key] = value
  })
  return obj
}

/**
 * <AUTHOR> @description 父子关系的数组转换成树形结构数据
 * @param data
 * @returns {*}
 */
export function translateDataToTree(data: any[]) {
  const parent = data.filter(
    (value) => value.parentId === 'undefined' || value.parentId == null
  )
  const children = data.filter(
    (value) => value.parentId !== 'undefined' && value.parentId != null
  )
  const translator = (parent: any[], children: any[]) => {
    parent.forEach((parent) => {
      children.forEach((current, index) => {
        if (current.parentId === parent.id) {
          const temp = JSON.parse(JSON.stringify(children))
          temp.splice(index, 1)
          translator([current], temp)
          typeof parent.children !== 'undefined'
            ? parent.children.push(current)
            : (parent.children = [current])
        }
      })
    })
  }
  translator(parent, children)
  return parent
}

/**
 * <AUTHOR> @description 树形结构数据转换成父子关系的数组
 * @param data
 * @returns {[]}
 */
export function translateTreeToData(data: any[]) {
  const result: { id: any; name: any; parentId: any }[] = []
  data.forEach((item) => {
    const loop = (data: {
      id: any
      name: any
      parentId: any
      children: any
    }) => {
      result.push({
        id: data.id,
        name: data.name,
        parentId: data.parentId,
      })
      const child = data.children
      if (child) {
        for (let i = 0; i < child.length; i++) {
          loop(child[i])
        }
      }
    }
    loop(item)
  })
  return result
}

/**
 * <AUTHOR> @description 10位时间戳转换
 * @param time
 * @returns {string}
 */
export function tenBitTimestamp(time: number) {
  const date = new Date(time * 1000)
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '' + d : d
  let h: any = date.getHours()
  h = h < 10 ? '0' + h : h
  let minute: any = date.getMinutes()
  let second: any = date.getSeconds()
  minute = minute < 10 ? '0' + minute : minute
  second = second < 10 ? '0' + second : second
  return y + '年' + m + '月' + d + '日 ' + h + ':' + minute + ':' + second //组合
}

/**
 * <AUTHOR> @description 13位时间戳转换
 * @param time
 * @returns {string}
 */
export function thirteenBitTimestamp(time: number) {
  const date = new Date(time / 1)
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '' + d : d
  let h: any = date.getHours()
  h = h < 10 ? '0' + h : h
  let minute: any = date.getMinutes()
  let second: any = date.getSeconds()
  minute = minute < 10 ? '0' + minute : minute
  second = second < 10 ? '0' + second : second
  return y + '年' + m + '月' + d + '日 ' + h + ':' + minute + ':' + second //组合
}

/**
 * <AUTHOR> @description 获取随机id
 * @param length
 * @returns {string}
 */
export function uuid(length = 32) {
  const num = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
  let str = ''
  for (let i = 0; i < length; i++) {
    str += num.charAt(Math.floor(Math.random() * num.length))
  }
  return str
}

/**
 * <AUTHOR> @description m到n的随机数
 * @param m
 * @param n
 * @returns {number}
 */
export function random(m: number, n: number) {
  return Math.floor(Math.random() * (m - n) + n)
}

/**
 * <AUTHOR> @description addEventListener
 * @type {function(...[*]=)}
 */
export const on = (function () {
  return function (
    element: {
      addEventListener: (arg0: any, arg1: any, arg2: boolean) => void
    },
    event: any,
    handler: any,
    useCapture = false
  ) {
    if (element && event && handler) {
      element.addEventListener(event, handler, useCapture)
    }
  }
})()

/**
 * <AUTHOR> @description removeEventListener
 * @type {function(...[*]=)}
 */
export const off = (function () {
  return function (
    element: {
      removeEventListener: (arg0: any, arg1: any, arg2: boolean) => void
    },
    event: any,
    handler: any,
    useCapture = false
  ) {
    if (element && event) {
      element.removeEventListener(event, handler, useCapture)
    }
  }
})()

/**
 * <AUTHOR> @description 将new Date转换为日期 格式 yyyy-MM-dd
 * @param dateObj
 * @returns {string}
 */
export function dateFormat(dateObj: Date) {
  const year = dateObj.getFullYear()
  const month = ('0' + (dateObj.getMonth() + 1)).slice(-2)
  const day = ('0' + dateObj.getDate()).slice(-2)
  return year + '-' + month + '-' + day
}

/**
 * <AUTHOR> @description 将new Date转换为日期时间 格式 yyyy-MM-dd hh:mm:ss
 * @param dateObj
 * @returns {string}
 */
export function timeFormat(dateObj: {
  getFullYear: () => any
  getMonth: () => number
  getDate: () => string
  getHours: () => { (): any; new (): any; toString: { (): any; new (): any } }
  getMinutes: () => { (): any; new (): any; toString: { (): any; new (): any } }
  getSeconds: () => { (): any; new (): any; toString: { (): any; new (): any } }
}) {
  const year = dateObj.getFullYear()
  const month = ('0' + (dateObj.getMonth() + 1)).slice(-2)
  const day = ('0' + dateObj.getDate()).slice(-2)
  const h = dateObj.getHours().toString()
  const m = dateObj.getMinutes().toString()
  const s = dateObj.getSeconds().toString()
  return year + '-' + month + '-' + day + ' ' + h + ':' + m + ':' + s
}

/**
 *
 * @param startTime 开始之间
 * @param endTime 结束时间
 * @returns 间隔时间
 */
export function setProgramMonthDiff(
  startTime: string | number | Date,
  endTime: string | number | Date
) {
  const flag = [1, 3, 5, 7, 8, 10, 12, 4, 6, 9, 11, 2]
  const start = new Date(startTime)
  const end = new Date(endTime)
  let year = end.getFullYear() - start.getFullYear()
  let month = end.getMonth() - start.getMonth()
  let day = end.getDate() - start.getDate()
  if (month < 0) {
    year--
    month = end.getMonth() + (12 - start.getMonth())
  }
  if (day < 0) {
    month--
    const index = flag.findIndex((temp) => {
      return temp === start.getMonth() + 1
    })
    let monthLength
    if (index <= 6) {
      monthLength = 31
    } else if (index > 6 && index <= 10) {
      monthLength = 30
    } else {
      monthLength = 28
    }
    day = end.getDate() + (monthLength - start.getDate())
  }

  return `${year}年${month}月${day}天`
}

export function bytesToMB(bytes: any) {
  const megabytes = bytes / (1024 * 1024)
  return megabytes.toFixed(2)
}

// 清空对象中所有数组
export function clearArrays(obj: any) {
  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      obj[key].length = 0
    }
  }
}

export function getSplitArray(data: []) {
  return data.map((item: any) => {
    return item['id']
  })
}

export function getProperty(data: [], name: string) {
  return data.map((item: any) => {
    return item[name]
  })
}

export function analyzeArray(data: [] | any) {
  return data.map((item: any) => {
    return {
      id: parseInt(item),
    }
  })
}

export function getSplitId(data: [] | any) {
  return data.map((item: any) => {
    return item.split('-')[0]
  })
}

/**
 * 秒数转换为分钟时长
 * @param seconds 秒
 * @returns
 */
export function secondsToMinutes(seconds: any) {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`
}

export function sliceTxt(text: string) {
  if (text.length >= 18) {
    return text.slice(0, 16) + '...'
  }
  return text
}

export function findIndex(data: [], val: any) {
  return data.findIndex((item) => item == val)
}

export function findTarget(data: [], val: any) {
  const flag = data.find((item) => item === val) === undefined
  return flag
}

export function sortFunc(a: any, b: any) {
  return b.split('-')[0] - a.split('-')[0]
}

export function getSplitTxt(txt: any, id: number) {
  const arr = txt?.split('-')
  if (!arr) return undefined
  return id === 0 ? arr?.[id] : arr.slice(id, arr.length).join('-')
}

/**
 * 计算交集，计算返回两个数组中独有的元素
 * @returns
 */
export function uniqueArrays(array: any[], array2: any[]) {
  const intersection = array.filter((value: any) => array2.includes(value))

  const uniqueToFirstArray = array.filter(
    (value: any) => !intersection.includes(value)
  )

  const uniqueToSecondArray = array2.filter(
    (value: any) => !intersection.includes(value)
  )

  return {
    uniqueToFirstArray,
    uniqueToSecondArray,
  }
}

/**
 * 将数组中所有空的 children 属性设置为 undefined。
 *
 * @param {Array<Object>} arr - 输入的对象数组，每个对象可以有一个 children 属性。
 * @returns {Array<Object>|undefined} - 返回处理后的数组，如果输入为空数组则返回 undefined。
 */
export function setEmptyChildrenToUndefined(
  arr: any[]
): Array<object> | undefined {
  if (arr.length === 0) return undefined // 处理空数组情况
  return arr.map((item) => {
    // 创建一个新对象以避免直接修改原对象
    const newItem = { ...item }
    if (newItem.children && Array.isArray(newItem.children)) {
      newItem.children =
        newItem.children.length === 0
          ? undefined
          : setEmptyChildrenToUndefined(newItem.children)
    }
    return newItem // 返回处理后的新对象
  })
}
