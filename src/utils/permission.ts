import store from '@/store'
/**
 * 是否可以访问目标权限元素
 * @param targetRoleOrPermission 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
export function hasPermission(targetRoleOrPermission: string[] | GuardType) {
  const admin = store.getters['acl/admin']
  const role = store.getters['acl/role']
  const ability = store.getters['acl/ability']
  if (admin) return true
  if (Array.isArray(targetRoleOrPermission)) {
    return can([...role, ...ability], {
      permission: targetRoleOrPermission,
      mode: 'oneOf',
    })
    // eslint-disable-next-line no-else-return
  } else {
    const {
      role = [],
      permission = [],
      mode = 'oneOf',
    } = targetRoleOrPermission
    return can([mode !== 'except'], {
      permission: [
        can(role, { permission: role, mode }),
        can(ability, { permission, mode }),
      ],
      mode,
    })
  }
}

/**
 * 检查是否满足权限
 * @param roleOrPermission 当前用户权限
 * @param target 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
function can(
  roleOrPermission: (string | boolean)[],
  target: { permission: string[]; mode: string }
): any {
  let hasRole = false
  const { permission = [], mode = 'oneOf' } = target
  if (mode === 'allOf')
    hasRole = permission.every((item: string) =>
      roleOrPermission.includes(item)
    )
  if (mode === 'oneOf')
    hasRole = permission.some((item: string) => roleOrPermission.includes(item))
  if (mode === 'except')
    hasRole = !permission.every((item: string) =>
      roleOrPermission.includes(item)
    )
  return hasRole
}
