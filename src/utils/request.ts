import { message } from 'ant-design-vue'
import axios from 'axios'
import qs from 'qs'

import { refresh } from '@/api/user'
import {
  Authorization,
  baseURL,
  contentType,
  requestTimeout,
  successCode,
} from '@/config'
import router from '@/router'
import store from '@/store'
import { isArray } from '@/utils/validate'

let loadingInstance: { close: () => void }

let refreshToking = false

let requests: (() => void)[] = []

/**
 * <AUTHOR> @description 处理code异常
 * @param {*} code
 * @param {*} msg
 */
const handleCode = async (
  config: any,
  code: any,
  msg: any,
  status: number | undefined,
  data: any
) => {
  switch (code) {
    case 200:
      return data
    case 401:
      if (msg == '登陆失败') return message.error(msg)
      return await tryRefreshToken(config)
    case 429:
    case 410:
      if (msg == '鉴权无效或过期' || msg == '缺少鉴权1')
        return await tryRefreshToken(config)
      message.error(msg)
      break
    default:
      message.error(msg || `后端接口${code}异常`)
      break
  }
}

/**
 * <AUTHOR> @description axios初始化
 */
const instance = axios.create({
  baseURL,
  timeout: requestTimeout,
  headers: {
    'Content-Type': contentType,
  },
})

/**
 * <AUTHOR> @description axios请求拦截器
 */

const requestConf: any = (config: any) => {
  if (store.getters['user/accessToken'])
    config.headers[Authorization] = store.getters['user/accessToken']
  if (
    config.data &&
    config.headers['Content-Type'] ===
      'application/x-www-form-urlencoded;charset=UTF-8'
  )
    config.data = qs.stringify(config.data)
  return config
}

/**
 * @description axios请求拦截器
 */
instance.interceptors.request.use(requestConf, (error) => {
  return Promise.reject(error)
})

/**
 * 刷新令牌
 * @param config 过期请求配置
 * @returns {any} 返回结果
 */
const tryRefreshToken = async (config: any) => {
  if (!refreshToking) {
    refreshToking = true
    try {
      const {
        data: {
          code,
          data: { access_token },
        },
      } = await refresh()
      switch (code) {
        case 200: {
          const accessToken = `bearer ${access_token}`
          store.dispatch('user/setAccessToken', accessToken)
          // 已经刷新了token，将所有队列中的请求进行重试
          requests.forEach((cb: any) => cb())
          requests = []
          return instance(requestConf(config))
        }
        default:
          store.dispatch('user/resetAll')
          break
      }
    } catch (error) {
      store.dispatch('user/resetAll')
    } finally {
      refreshToking = false
    }
  } else {
    return new Promise((resolve) => {
      // 将resolve放进队列，用一个函数形式来保存，等token刷新后直接执行
      requests.push(() => {
        resolve(instance(requestConf(config)))
      })
    })
  }
}

/**
 * <AUTHOR> @description axios响应拦截器
 */
instance.interceptors.response.use(
  (response) => {
    if (loadingInstance) loadingInstance.close()

    const { config, data, status } = response
    const { message: msg } = data
    // 操作正常Code数组
    const codeVerificationArray = isArray(successCode)
      ? [...successCode]
      : [...[successCode]]
    let code = data?.code ? data.code : status
    // 是否操作正常
    if (codeVerificationArray.includes(data.code)) {
      code = 200
    }
    return handleCode(config, code, msg, status, data)
  },
  (error) => {
    if (loadingInstance) loadingInstance.close()
    let { message: _message } = error
    if (_message === 'Network Error') {
      _message = '后端接口连接异常'
    }
    if (_message.includes('timeout')) {
      _message = '后端接口请求超时'
    }
    if (_message.includes('Request failed with status code')) {
      const code = _message.substr(_message.length - 3)
      _message = '后端接口' + code + '异常'
    }
    message.error(_message || `后端接口未知异常`)
    return Promise.reject(error)
  }
)

export default instance
