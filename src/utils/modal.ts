import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { createVNode } from 'vue'

export function modal(onOk: () => any) {
  Modal.confirm({
    title: '二次确认对话框!',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode(
      'div',
      {
        style: 'color:red;',
      },
      '请仔细确认表单内容无误后单击确认!'
    ),
    okText: '确认',
    cancelText: '取消',
    onOk() {
      return onOk && onOk()
    },
    onCancel() {
      message.success('取消')
    },
  })
}

