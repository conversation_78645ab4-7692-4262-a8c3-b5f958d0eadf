import store from '@/store'

export function hasRole(value: string | string[]) {
  if (store.getters['acl/admin']) return true
  if (value instanceof Array && value.length > 0)
    return can(store.getters['acl/role'], {
      role: value,
      mode: 'oneOf',
    })
  let mode = 'oneOf'
  if (Object.prototype.hasOwnProperty.call(value, 'mode'))
    mode = (value as any)['mode']
  let result = true
  if (Object.prototype.hasOwnProperty.call(value, 'role'))
    result =
      result &&
      can(store.getters['acl/role'], { role: (value as any)['role'], mode })
  if (result && Object.prototype.hasOwnProperty.call(value, 'ability'))
    result =
      result &&
      can(store.getters['acl/ability'], {
        role: (value as any)['ability'],
        mode,
      })
  return result
}

export function can(
  roleOrAbility: string | any[],
  value: { role: any; mode: any }
) {
  let hasRole = false
  if (
    value instanceof Object &&
    Object.prototype.hasOwnProperty.call(value, 'role') &&
    Object.prototype.hasOwnProperty.call(value, 'mode')
  ) {
    const { role, mode } = value
    if (mode === 'allOf') {
      hasRole = role.every((item: any) => {
        return roleOrAbility.includes(item)
      })
    }
    if (mode === 'oneOf') {
      hasRole = role.some((item: any) => {
        return roleOrAbility.includes(item)
      })
    }
    if (mode === 'except') {
      hasRole = !role.some((item: any) => {
        return roleOrAbility.includes(item)
      })
    }
  }
  return hasRole
}
