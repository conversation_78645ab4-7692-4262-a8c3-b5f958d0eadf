import { VabRouteRecord } from '#/router'

/**
 * <AUTHOR> @description tagsBar多标签页逻辑，前期借鉴了很多开源项目发现都有个共同的特点很繁琐并不符合框架设计的初衷，后来在github用户cyea的启发下完成了重构，请勿修改
 */

const state = () => ({
  visitedRoutes: [],
})
const getters = {
  visitedRoutes: (state: { visitedRoutes: Array<VabRouteRecord> }) =>
    state.visitedRoutes,
}
const mutations = {
  /**
   * <AUTHOR> @description 添加标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */

  addVisitedRoute(
    state: { visitedRoutes: Array<VabRouteRecord> },
    route: VabRouteRecord
  ) {
    const target = state.visitedRoutes.find(
      (item: VabRouteRecord) => item.path === route.path
    )
    if (target && route?.fullPath !== target?.fullPath)
      Object.assign(target, route)
    else if (!target) state.visitedRoutes.push(Object.assign({}, route))
  },
  /**
   * <AUTHOR> @description 删除当前标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */
  delVisitedRoute(
    state: { visitedRoutes: Array<VabRouteRecord> },
    route: VabRouteRecord
  ) {
    state.visitedRoutes.forEach((item: VabRouteRecord, index: number) => {
      if (item.path === route.path) state.visitedRoutes.splice(index, 1)
    })
  },
  /**
   * <AUTHOR> @description 删除当前标签页以外其它全部多标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */
  delOthersVisitedRoutes(
    state: { visitedRoutes: Array<VabRouteRecord> },
    route: VabRouteRecord
  ) {
    state.visitedRoutes = state.visitedRoutes.filter(
      (item: VabRouteRecord) => item.meta!.affix || item.path === route.path
    )
  },
  /**
   * <AUTHOR> @description 删除当前标签页左边全部多标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */
  delLeftVisitedRoutes(
    state: { visitedRoutes: Array<VabRouteRecord> },
    route: VabRouteRecord
  ) {
    let index = state.visitedRoutes.length
    state.visitedRoutes = state.visitedRoutes.filter((item: VabRouteRecord) => {
      if (item.name === route.name) index = state.visitedRoutes.indexOf(item)
      return item.meta!.affix || index <= state.visitedRoutes.indexOf(item)
    })
  },
  /**
   * <AUTHOR> @description 删除当前标签页右边全部多标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */
  delRightVisitedRoutes(
    state: { visitedRoutes: Array<VabRouteRecord> },
    route: VabRouteRecord
  ) {
    let index = state.visitedRoutes.length
    state.visitedRoutes = state.visitedRoutes.filter((item: VabRouteRecord) => {
      if (item.name === route.name) index = state.visitedRoutes.indexOf(item)
      return item.meta!.affix || index >= state.visitedRoutes.indexOf(item)
    })
  },
  /**
   * <AUTHOR> @description 删除全部多标签页
   * @param {*} state
   * @param {*} route
   * @returns
   */
  delAllVisitedRoutes(state: { visitedRoutes: Array<VabRouteRecord> }) {
    state.visitedRoutes = state.visitedRoutes.filter(
      (item: VabRouteRecord) => item.meta!.affix
    )
  },
}
const actions = {
  /**
   * <AUTHOR> @description 添加标签页
   * @param {*} { commit }
   * @param {*} route
   */
  addVisitedRoute({ commit }: any, route: VabRouteRecord) {
    commit('addVisitedRoute', route)
  },
  /**
   * <AUTHOR> @description 删除当前标签页
   * @param {*} { commit }
   * @param {*} route
   */
  delVisitedRoute({ commit }: any, route: VabRouteRecord) {
    commit('delVisitedRoute', route)
  },
  /**
   * <AUTHOR> @description 删除当前标签页以外其它全部多标签页
   * @param {*} { commit }
   * @param {*} route
   */
  delOthersVisitedRoutes({ commit }: any, route: VabRouteRecord) {
    commit('delOthersVisitedRoutes', route)
  },
  /**
   * <AUTHOR> @description 删除当前标签页左边全部多标签页
   * @param {*} { commit }
   * @param {*} route
   */
  delLeftVisitedRoutes({ commit }: any, route: VabRouteRecord) {
    commit('delLeftVisitedRoutes', route)
  },
  /**
   * <AUTHOR> @description 删除当前标签页右边全部多标签页
   * @param {*} { commit }
   * @param {*} route
   */
  delRightVisitedRoutes({ commit }: any, route: VabRouteRecord) {
    commit('delRightVisitedRoutes', route)
  },
  /**
   * <AUTHOR> @description 删除全部多标签页
   * @param {*} { commit }
   */
  delAllVisitedRoutes({ commit }: any) {
    commit('delAllVisitedRoutes')
  },
}
export default { state, getters, mutations, actions }
