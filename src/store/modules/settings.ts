/**
 * <AUTHOR> @description 所有全局配置的状态管理，如无必要请勿修改
 */
import defaultSettings from '@/config'
import { isJson } from '@/utils/validate'

const {
  logo,
  title,
  layout,
  themeName,
  i18n,
  showLanguage,
  showProgressBar,
  showRefresh,
  showSearch,
  showTheme,
  showNotice,
  showFullScreen,
} = defaultSettings

const getLocalStorage = (key: string) => {
  const value: string | null = localStorage.getItem(key)
  return value && isJson(value) ? JSON.parse(value) : false
}

const theme = getLocalStorage('vue-admin-beautiful-pro-theme')
const { collapse } = getLocalStorage('vue-admin-beautiful-pro-collapse')
const { language } = getLocalStorage('vue-admin-beautiful-pro-language')
const toggleBoolean = (key: any) => {
  return typeof theme[key] !== 'undefined' ? theme[key] : key
}

const state = () => ({
  logo,
  title,
  collapse,
  themeName: theme.themeName || themeName,
  layout: theme.layout || layout,
  header: theme.header,
  device: 'desktop',
  language: language || i18n,
  showLanguage: toggleBoolean(showLanguage),
  showProgressBar: toggleBoolean(showProgressBar),
  showRefresh: toggleBoolean(showRefresh),
  showSearch: toggleBoolean(showSearch),
  showTheme: toggleBoolean(showTheme),
  showNotice: toggleBoolean(showNotice),
  showFullScreen: toggleBoolean(showFullScreen),
})
const getters = {
  collapse: (state: { collapse: boolean }) => state.collapse,
  device: (state: { device: string }) => state.device,
  header: (state: { header: any }) => state.header,
  language: (state: { language: string }) => state.language,
  layout: (state: { layout: string }) => state.layout,
  logo: (state: { logo: boolean }) => state.logo,
  title: (state: { title: string }) => state.title,
  showLanguage: (state: { showLanguage: boolean }) => state.showLanguage,
  showProgressBar: (state: { showProgressBar: boolean }) =>
    state.showProgressBar,
  showRefresh: (state: { showRefresh: boolean }) => state.showRefresh,
  showSearch: (state: { showSearch: boolean }) => state.showSearch,
  showTheme: (state: { showTheme: boolean }) => state.showTheme,
  showNotice: (state: { showNotice: boolean }) => state.showNotice,
  showFullScreen: (state: { showFullScreen: boolean }) => state.showFullScreen,
  themeName: (state: { themeName: string }) => state.themeName,
}
const mutations = {
  toggleCollapse(state: { collapse: boolean }) {
    state.collapse = !state.collapse
    localStorage.setItem(
      'vue-admin-beautiful-pro-collapse',
      `{"collapse":${state.collapse}}`
    )
  },
  toggleDevice(state: { device: string }, device: string) {
    state.device = device
  },
  changeHeader(state: { header: any }, header: any) {
    state.header = header
  },
  changeLayout(state: { layout: string }, layout: string) {
    state.layout = layout
  },
  handleShowLanguage(state: { showLanguage: boolean }, showLanguage: boolean) {
    state.showLanguage = showLanguage
  },
  handleShowProgressBar(
    state: { showProgressBar: boolean },
    showProgressBar: boolean
  ) {
    state.showProgressBar = showProgressBar
  },
  handleShowRefresh(state: { showRefresh: boolean }, showRefresh: boolean) {
    state.showRefresh = showRefresh
  },
  handleShowSearch(state: { showSearch: boolean }, showSearch: boolean) {
    state.showSearch = showSearch
  },
  handleShowTheme(state: { showTheme: boolean }, showTheme: boolean) {
    state.showTheme = showTheme
  },
  handleShowNotice(state: { showNotice: boolean }, showNotice: boolean) {
    state.showNotice = showNotice
  },
  handleShowFullScreen(
    state: { showFullScreen: boolean },
    showFullScreen: boolean
  ) {
    state.showFullScreen = showFullScreen
  },
  openSideBar(state: { collapse: boolean }) {
    state.collapse = false
  },
  foldSideBar(state: { collapse: boolean }) {
    state.collapse = true
  },
  changeLanguage(state: { language: string }, language: string) {
    localStorage.setItem(
      'vue-admin-beautiful-pro-language',
      `{"language":"${language}"}`
    )
    state.language = language
  },
}
const actions = {
  toggleCollapse({ commit }: any) {
    commit('toggleCollapse')
  },
  toggleDevice({ commit }: any, device: string) {
    commit('toggleDevice', device)
  },
  changeHeader({ commit }: any, header: any) {
    commit('changeHeader', header)
  },
  changeLayout({ commit }: any, layout: string) {
    commit('changeLayout', layout)
  },
  handleShowLanguage: ({ commit }: any, showLanguage: boolean) => {
    commit('handleShowLanguage', showLanguage)
  },
  handleShowProgressBar: ({ commit }: any, showProgressBar: boolean) => {
    commit('handleShowProgressBar', showProgressBar)
  },
  handleShowRefresh: ({ commit }: any, showRefresh: boolean) => {
    commit('handleShowRefresh', showRefresh)
  },
  handleShowSearch: ({ commit }: any, showSearch: boolean) => {
    commit('handleShowSearch', showSearch)
  },
  handleShowTheme: ({ commit }: any, showTheme: boolean) => {
    commit('handleShowTheme', showTheme)
  },
  handleShowNotice: ({ commit }: any, showNotice: boolean) => {
    commit('handleShowNotice', showNotice)
  },
  handleShowFullScreen: ({ commit }: any, showFullScreen: boolean) => {
    commit('handleShowFullScreen', showFullScreen)
  },
  openSideBar({ commit }: any) {
    commit('openSideBar')
  },
  foldSideBar({ commit }: any) {
    commit('foldSideBar')
  },
  changeLanguage: ({ commit }: any, language: string) => {
    commit('changeLanguage', language)
  },
}
export default { state, getters, mutations, actions }
