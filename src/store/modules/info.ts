const state = () => ({
  grades: [],
  subjects: [],
  cp: [],
})
const getters = {
  grades: (state: { grades: string[] }) => state.grades,
  subjects: (state: { subjects: string[] }) => state.subjects,
  cp: (state: { cp: any }) => state.cp,
}
const mutations = {
  setGrades(state: { grades: string[] }, grades: string[]) {
    state.grades = grades
  },
  setSubjects(state: { subjects: string[] }, subjects: string[]) {
    state.subjects = subjects
  },
  setCp(state: { cp: any }, cp: any) {
    state.cp = cp
  },
}
const actions = {
  setGrades({ commit }: any, grades: string[]) {
    commit('setGrades', grades)
  },
  setSubjects({ commit }: any, subjects: string[]) {
    commit('setSubjects', subjects)
  },
  setCp({ commit }: any, cp: any) {
    commit('setCp', cp)
  },
}
export default { state, getters, mutations, actions }
