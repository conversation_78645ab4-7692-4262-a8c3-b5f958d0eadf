const state = () => ({
  admin: false,
  role: [],
  ability: [],
  address: '',
  email: '',
  id: '',
  system_channels: '',
  name: '',
  roleName: '',
})
const getters = {
  admin: (state: { admin: boolean }) => state.admin,
  role: (state: { role: string[] }) => state.role,
  roleName: (state: { roleName: string[] }) => state.roleName,
  ability: (state: { ability: any }) => state.ability,
  address: (state: { address: string }) => state.address,
  email: (state: { email: string }) => state.email,
  id: (state: { id: number }) => state.id,
  system_channels: (state: { system_channels: string[] }) =>
    state.system_channels,
  name: (state: { name: string }) => state.name,
}
const mutations = {
  setFull(state: { admin: boolean }, admin: boolean) {
    state.admin = admin
  },
  setRole(state: { role: string[] }, role: string[]) {
    state.role = role
  },
  setRoleName(state: { roleName: string[] }, roleName: string[]) {
    state.roleName = roleName
  },
  setAbility(state: { ability: any }, ability: any) {
    state.ability = ability
  },
  setAddress(state: { address: string }, address: string) {
    state.address = address
  },
  setEmail(state: { email: string }, email: string) {
    state.email = email
  },
  setId(state: { id: number }, id: number) {
    state.id = id
  },
  setSystem_channels(
    state: { system_channels: string[] },
    system_channels: string[]
  ) {
    state.system_channels = system_channels
  },
  setName(state: { name: string }, name: string) {
    state.name = name
  },
}
const actions = {
  setFull({ commit }: any, admin: boolean) {
    commit('setFull', admin)
  },
  setRole({ commit }: any, role: string[]) {
    commit('setRole', role)
  },
  setRoleName({ commit }: any, roleName: string[]) {
    commit('setRoleName', roleName)
  },
  setAbility({ commit }: any, ability: any) {
    commit('setAbility', ability)
  },
  setAddress({ commit }: any, address: string) {
    commit('setAddress', address)
  },
  setEmail({ commit }: any, email: string) {
    commit('setEmail', email)
  },
  setId({ commit }: any, id: number) {
    commit('setId', id)
  },
  setSystem_channels({ commit }: any, system_channels: string[]) {
    commit('setSystem_channels', system_channels)
  },
  setName({ commit }: any, name: string) {
    commit('setName', name)
  },
}
export default { state, getters, mutations, actions }
