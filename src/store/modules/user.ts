/**
 * <AUTHOR> @description 登录、获取用户信息、退出登录、清除accessToken逻辑，不建议修改
 */
import { message, notification } from 'ant-design-vue'
import { Base64 } from 'js-base64'
import JSEncrypt from 'jsencrypt'

import { getUserInfo, logout, NewLogin, refresh } from '@/api/user'
import { title, tokenName } from '@/config'
import {
  getAccessToken,
  removeAccessToken,
  setAccessToken,
} from '@/utils/accessToken'

const state = () => ({
  accessToken: getAccessToken(),
  username: '',
  avatar: '',
})
const getters = {
  accessToken: (state: { accessToken: string }) => state.accessToken,
  username: (state: { username: string }) => state.username,
  avatar: (state: { avatar: string }) => state.avatar,
}

const mutations = {
  /**
   * <AUTHOR> @description 设置accessToken
   * @param {*} state
   * @param {*} accessToken
   */
  setAccessToken(state: { accessToken: string }, accessToken: string) {
    state.accessToken = accessToken
    setAccessToken(accessToken)
  },
  /**
   * <AUTHOR> @description 设置用户名
   * @param {*} state
   * @param {*} username
   */
  setUsername(
    state: {
      (): { accessToken: string; username: string; avatar: string }
      username?: string
    },
    username: string
  ) {
    state.username = username
  },
  /**
   * <AUTHOR> @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state: { avatar: string }, avatar: string) {
    state.avatar = avatar
  },
}
const actions = {
  /**
   * <AUTHOR> @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }: any) {
    dispatch('acl/setFull', true, { root: true })
    commit('setAvatar', 'https://i.gtimg.cn/club/item/face/img/2/15922_100.gif')
    commit('setUsername', 'admin(未开启登录拦截)')
  },
  /**
   * <AUTHOR> @description 登录
   * @param {*} { commit }
   * @param {*} userInfo
   */
  async login(
    { commit }: any,
    userInfo: {
      username: string
      password: string
      key: string
      captcha: string
    }
  ) {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(
      `-----BEGIN PUBLIC KEY-----MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA0vhTo8Z7RtbA9CpWgVcH1soagoV7sNOce7NQQPEK0g3wLQdYXWWEbIOU0L/X5XpZfzcpn/L+8YcFHFAoZVnjglEg/EhprIJSyHE87gvmSsoNEEFN7sMFgnOnf9ora6NiEcJYz2FuB+YwN7utWDyStfh2rItRoX1JsxkxAXFee8e3LPbB94BjYyIMTiavMuMHLxGGi3/UYJrHMpVnx4uuSrQW9QkAY7/uZc16PbNtkiUvo+V8ZPVYFBjR9x6itxmOv7fhntUn2wLUBNvxNFN/r7o0H/tiAV4Kk3SBHxhnkbkrnaRSEq59cQXgziyKRcusqF0IhM+blhYKo57F2+60yQomDcNumk4PB5aaRY6TdRHaYCrRfCOQpIUxVljdZz99O7vgqpt+2aYt8uSZ5QnMgH5hmRJKtjuSsHe3IIeC3v4KmFx+EDlO3/+ichJdxzxuwnZKUvBpZFE6zP7PTiv1jWi+4dx8dEHJEZCWUTf4+4YVntQXmeMdadcwduqGmpW4WT+Qdt7nhOrcO2Zvnwz8yYQB0dwtOkdjxF8ZmgFrI8ZGGiI113zvAAHMkFEQoVEYeX6UfL8i+gcGNhk/+MFWLb3VFOf7+xWRY+Vg+I9seoHABE66HbsO48vPvAWvm/S2mFJgFfmVLEgL/QaJzPFGl4qop8rHRiijbuo0w55qoJECAwEAAQ==-----END PUBLIC KEY-----`
    )
    const RSA: any = encryptor.encrypt(userInfo.password)
    const base64 = RSA
    // let 加密后数据 = encryptor.encrypt("加密的内容")
    const data = await NewLogin({
      username: userInfo.username,
      password: base64,
      key: userInfo.key,
      captcha: userInfo.captcha,
    })

    switch (data?.code) {
      case 200: {
        const accessToken = `bearer ${data?.data.access_token}`
        mutations.setUsername(state, userInfo.username) // 设置用户名
        if (accessToken) {
          commit('setAccessToken', accessToken)
          const hour = new Date().getHours()
          const thisTime =
            hour < 8
              ? '早上好'
              : hour <= 11
              ? '上午好'
              : hour <= 13
              ? '中午好'
              : hour < 18
              ? '下午好'
              : '晚上好'
          notification.open({
            message: `欢迎登录${title}`,
            description: `${thisTime}！`,
          })
        } else {
          message.error(`登录接口异常，未正确返回${tokenName}...`)
        }
        break
      }
      default:
        throw new Error('手动触发的异常')
        break
    }
  },
  async refreshLogin({ commit, dispatch }: any) {
    console.log('执行refresh')
    try {
      const {
        data: {
          code,
          data: { access_token },
        },
      } = await refresh()
      switch (code) {
        case 200:
          message.success('刷新鉴权')
          {
            const accessToken = `bearer ${access_token}`
            commit('setAccessToken', accessToken)
          }
          break
        default:
          // eslint-disable-next-line no-empty-function
          dispatch('resetAll').catch(() => {})
          break
      }
    } catch (error) {
      // eslint-disable-next-line no-empty-function
      dispatch('resetAll').catch(() => {})
    }
  },
  /**
   * <AUTHOR> @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch }: any) {
    // console.log(state.accessToken)
    // if (state.accessToken !== 'puxin001') {
    //   message.error(`验证失败，请重新登录...`)
    //   await dispatch('resetAll')
    //   return false
    // }
    // console.log(state)
    const { data } = await getUserInfo()
    if (!data) {
      message.error(`验证失败，请重新登录...`)
      await dispatch('resetAll')
      return false
    }

    // dispatch('refreshLogin')

    // let admin = {
    //   rules: ['administrator'],
    //   username: '超级管理员',
    // }
    // let admin = {
    //   rules: ['business'],
    //   username: '商务',
    // }
    // let admin = {
    //   rules: ['main'],
    //   username: '总部运营',
    // }
    // let admin = {
    //   rules: ['channel'],
    //   username: '驻地运营',
    // }

    // let data = {
    //   avatar:
    //     'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    //   roles: admin.rules,
    //   ability: ['READ', 'WRITE', 'DELETE'],
    //   username: admin.username,
    //   address: '宁波',
    // }

    const {
      username,
      avatar, // null 无法设置默认值
      roles,
      ip,
      ability,
      email,
      id,
      name,
    } = data
    if (username && roles && Array.isArray(roles)) {
      dispatch('acl/setRole', [roles[0]?.slug], { root: true })
      dispatch('acl/setRoleName', [roles[0]?.name], { root: true })
      dispatch('acl/setAddress', ip, { root: true }) // 设置登录IP root:true
      dispatch('acl/setEmail', email, { root: true }) // 设置邮箱 root:true
      dispatch('acl/setId', id, { root: true }) // 设置用户ID
      dispatch('acl/setName', username, { root: true }) //设置用户账号
      if (ability && ability.length > 0)
        dispatch('acl/setAbility', ability, { root: true })
      commit('setUsername', name) // 设置用户名
      commit(
        'setAvatar',
        avatar ||
          'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
      )
    } else {
      message.error('用户信息接口异常')
    }
  },

  /**
   * <AUTHOR> @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }: any) {
    await logout()
    await dispatch('resetAll')
  },
  /**
   * <AUTHOR> @description 重置accessToken、roles、ability、router等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ dispatch }: any) {
    await dispatch('setAccessToken', '')
    await dispatch('acl/setFull', false, { root: true })
    await dispatch('acl/setRole', [], { root: true })
    await dispatch('acl/setAddress', '', { root: true })
    await dispatch('acl/setEmail', '', { root: true })
    await dispatch('acl/setId', '', { root: true })
    await dispatch('acl/setSystem_channels', '', { root: true })
    removeAccessToken()
  },
  /**
   * <AUTHOR> @description 设置token
   */
  setAccessToken({ commit }: any, accessToken: any) {
    commit('setAccessToken', accessToken)
  },
}
export default { state, getters, mutations, actions }
