/**
 * <AUTHOR> @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { getRouterList } from '@/api/router'
import { asyncRoutes, constantRoutes } from '@/router'
import { convertRouter, filterRoutes } from '@/utils/routes'
import { VabRouteRecord } from '#/router'

const state = () => ({
  routes: [],
  partialRoutes: [],
  noticeCount: 0,
  duplicateCount: 0,
  suspendCount: 0,
})
const getters = {
  routes: (state: { routes: Array<VabRouteRecord> }) => state.routes,
  partialRoutes: (state: { partialRoutes: any }) => state.partialRoutes,
  noticeCount: (state: { noticeCount: any }) => state.noticeCount,
  duplicateCount: (state: { duplicateCount: any }) => state.duplicateCount,
  suspendCount: (state: { suspendCount: any }) => state.suspendCount,
}
const mutations = {
  setRoutes(
    state: { routes: Array<VabRouteRecord> },
    routes: Array<VabRouteRecord>
  ) {
    state.routes = routes
  },
  setPartialRoutes(state: { partialRoutes: any }, routes: any) {
    state.partialRoutes = routes
  },
  setNoticeCount(state: { noticeCount: any }, routes: any) {
    state.noticeCount = routes
  },
  setDuplicateCount(state: { duplicateCount: any }, routes: any) {
    state.duplicateCount = routes
  },
  setSuspendCount(state: { suspendCount: any }, routes: any) {
    state.suspendCount = routes
  },
}
const actions = {
  /**
   * <AUTHOR> @description intelligence模式设置路由
   * @param {*} { commit }
   * @returns
   */
  async setRoutes({ commit }: any) {
    const finallyRoutes = filterRoutes([...constantRoutes, ...asyncRoutes])
    commit('setRoutes', finallyRoutes)
    return [...asyncRoutes]
  },
  /**
   * <AUTHOR> @description all模式设置路由
   * @param {*} { commit }
   * @returns
   */
  async setAllRoutes({ commit }: any) {
    const { data } = await getRouterList({})
    if (data[data.length - 1].path !== '*')
      data.push({ path: '*', redirect: '/404', hidden: true })
    const asyncRoutes = convertRouter(data)
    const finallyRoutes = filterRoutes([...constantRoutes, ...asyncRoutes])
    commit('setRoutes', finallyRoutes)
    return [...asyncRoutes]
  },
  /**
   * <AUTHOR> @description 画廊布局、综合布局设置路由
   * @param {*} { commit }
   * @param accessedRoutes 画廊布局、综合布局设置路由
   */
  setPartialRoutes({ commit }: any, accessedRoutes: any) {
    commit('setPartialRoutes', accessedRoutes)
  },
  setNoticeCount({ commit }: any, count: any) {
    commit('setNoticeCount', count)
  },
  setDuplicateCount({ commit }: any, count: any) {
    commit('setDuplicateCount', count)
  },
  setSuspendCount({ commit }: any, count: any) {
    commit('setSuspendCount', count)
  },
}
export default { state, getters, mutations, actions }
