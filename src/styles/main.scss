@use './font.scss';
@use './color.scss';
$width: 1440px;
.p-10 {
  padding: 10px;
}
.p-20 {
  padding: 20px;
}
.pl-10 {
  padding-left: 10px;
}
.pl-15 {
  padding-left: 15px;
}
.pl-20 {
  padding-left: 20px;
}
.pl-25 {
  padding-left: 25px;
}
.pl-30 {
  padding-left: 30px;
}

.pr-10 {
  padding-right: 10px;
}
.pr-15 {
  padding-right: 15px;
}
.pr-20 {
  padding-right: 20px;
}
.pr-25 {
  padding-right: 25px;
}
.pr-30 {
  padding-right: 30px;
}

.pt-10 {
  padding-top: 10px;
}
.pt-20 {
  padding-top: 20px;
}
.pt-30 {
  padding-top: 30px;
}

.pb-10 {
  padding-bottom: 10px;
}
.pb-20 {
  padding-bottom: 20px;
}
.pb-30 {
  padding-bottom: 30px;
}

.px-10 {
  padding: 0px 10px;
}
.px-30 {
  padding: 0px 30px;
}
.px-50 {
  padding: 0px 50px;
}

.py-10 {
  padding: 10px 0px;
}
.py-30 {
  padding: 30px 0px;
}

.m-10 {
  margin: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.ml-10 {
  margin-left: 10px;
}
.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}
.mt-30 {
  margin-top: 30px;
}
.mt-50 {
  margin-top: 50px;
}
.mx-15 {
  margin: 0px 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.cursor {
  cursor: pointer;
  border-radius: 15px;
  overflow: hidden;
}
.width100 {
  width: 100%;
}

:global(#dotted::before) {
  content: '.';
  display: inline-block;
  position: absolute;
  left: 5px;
  top: 5px;
}
:global(#dotted) {
  position: relative;
  display: inline-block;
}

:global(.ant-table-thead > tr > th) {
  text-align: center !important;
}
:global(.ant-table-tbody > tr) {
  text-align: center !important;
}

:global(.w-e-text-container [data-slate-editor] > table > td) {
  border-right-width: 1px !important;
}
