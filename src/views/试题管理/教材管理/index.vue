<template>
  <a-row justify="space-between">
    <a-col :span="5">
      <a-flex align="center" class="py-10" gap="10">
        <div><DefaultDivider /></div>
        <div class="font-bold">学段学科列表</div>
      </a-flex>
      <a-tree
        :field-names="{
          title: 'name',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
        @select="onSelect"
      />
    </a-col>
    <a-col :span="18">
      <a-flex align="center" class="py-10" gap="10">
        <DefaultDivider />
        <div class="font-bold">教材列表</div>
      </a-flex>
      <a-flex class="py-10" gap="10">
        <a-button :disabled="disabled" type="primary" @click="create">
          创建教材节点
        </a-button>
      </a-flex>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        rowKey="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'is_show'">
            <a-switch
              :checked="record.is_show == 1"
              checked-children="发布"
              size="small"
              un-checked-children="未发布"
              @change="onChecked($event, record)"
            />
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space :size="10">
              <a-button
                v-if="record.parent_id == 0"
                type="primary"
                @click="createChild(record)"
              >
                创建教材年级
              </a-button>
              <a-button type="primary" @click="edit(record.id)">编辑</a-button>
              <a-button danger type="primary" @click="onDelete(record.id)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-col>
    <CreateChildModal ref="createChildModalRef" @ok="handleOk" />
    <CreateModal ref="createModalRef" @ok="handleOk" />
    <EditModal ref="editModalRef" @ok="handleOk" />
  </a-row>
</template>

<script setup>
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
  import { message, Modal } from 'ant-design-vue'
  import { computed, createVNode, ref } from 'vue'
  import { usePagination } from 'vue-request'

  import { getQbGrades } from '@/api/试题/学段'
  import {
    deleteQbQueTabs,
    editQbQueTabs,
    getQbQueTabs,
  } from '@/api/试题/选项卡'
  import { setEmptyChildrenToUndefined } from '@/utils/index'

  import EditModal from './components/修改教材'
  import CreateChildModal from './components/创建教材年级'
  import CreateModal from './components/创建教材节点'

  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbQueTabs)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        children: setEmptyChildrenToUndefined(item.children),
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '教材名称',
      dataIndex: 'name',
    },
    {
      title: '是否发布',
      dataIndex: 'is_show',
    },
    {
      title: '排序权重',
      dataIndex: 'orderby',
    },
    {
      title: '编辑',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })
  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    fetch()
  }
  const fetch = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      parent_id: 0,
      qb_grade_id: qb_grade_id.value,
      qb_subject_id: qb_subject_id.value,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
      ],
    })
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  // 编辑
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 点击树节点触发
  const qb_grade_id = ref()
  const qb_subject_id = ref()
  const treeData = ref([])
  const onSelect = (item, { node }) => {
    if (item.length == 0) return
    console.log(node)
    switch (node.dataRef.type) {
      case 1:
        break
      case 2:
        pagination.value.current = 1
        qb_grade_id.value = node.dataRef.parent_id
        qb_subject_id.value = node.dataRef.id
        fetch()
        break
      default:
        break
    }
  }
  // 树-初始化
  const init = async () => {
    const { data } = await getQbGrades()
    treeData.value = data.map((item) => {
      return {
        children: item.qb_subjects.map((x) => {
          return {
            key: `${item.id}-${x.id}`,
            type: 2,
            parent_id: item.id,
            ...x,
          }
        }),
        key: item.id,
        type: 1,
        ...item,
      }
    })
  }

  // 创建教材节点-表单
  const createModalRef = ref('')
  const disabled = computed(() => (qb_subject_id.value ? false : true))
  const create = () => {
    createModalRef.value?.setQbGradeId(qb_grade_id.value)
    createModalRef.value?.setQbSubjectId(qb_subject_id.value)
    createModalRef.value?.showModal()
  }

  // 是否隐藏-单击
  const onChecked = async (checked, record) => {
    const { code } = await editQbQueTabs(
      {
        name: record.name,
        orderby: record.orderby,
        is_show: checked == false ? 0 : 1,
      },
      record.id
    )
    handleOk()
    if (code == 200) {
      message.success('编辑成功')
    }
  }

  // 创建教材年级-表单
  const createChildModalRef = ref('')
  const createChild = (record) => {
    createChildModalRef.value?.setParentId(record.id)
    createChildModalRef.value?.setQbGradeId(qb_grade_id.value)
    createChildModalRef.value?.setQbSubjectId(qb_subject_id.value)
    createChildModalRef.value?.showModal()
  }
  const handleOk = async () => {
    await init()
    // 有表格加载id则加载
    qb_subject_id.value && fetch()
  }

  // 删除
  const onDelete = async (id) => {
    Modal.confirm({
      title: '您确定要删除吗?',
      icon: createVNode(ExclamationCircleOutlined),
      okType: 'danger',
      async onOk() {
        const { code } = await deleteQbQueTabs(id)
        if (code == 200) {
          handleOk()
          message.success('删除成功')
        }
      },
      onCancel() {
        console.log('Cancel')
      },
    })
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
