<template>
  <a-modal
    v-model:open="open"
    title="创建教材年级"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        label="教材名称"
        name="name"
        :rules="[{ required: true, message: '教材名称不能为空' }]"
      >
        <a-input
          v-model:value="formState.name"
          placeholder="请输入教材名称"
          :showCount="true"
        />
      </a-form-item>
      <a-form-item
        label="排序权重"
        name="orderby"
        :rules="[
          { required: true, type: 'number', message: '排序权重不能为空' },
        ]"
      >
        <a-input-number v-model:value="formState.orderby" :min="0" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createQbQueTabs } from '@/api/试题/选项卡'

  const emits = defineEmits(['cancel', 'ok'])

  const formRef = ref()
  const formState = reactive({
    name: '',
    qb_grade_id: undefined, // 学段
    qb_subject_id: undefined, // 学科
    type: 'textbook',
    parent_id: 1,
    orderby: 0,
  })
  const setQbGradeId = (value) => {
    formState.qb_grade_id = value
  }
  const setQbSubjectId = (value) => {
    formState.qb_subject_id = value
  }
  const setParentId = (value) => {
    formState.parent_id = value
  }

  const open = ref(false)
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createQbQueTabs(formState)

    if (code == 200) {
      message.success('创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  defineExpose({
    showModal,
    formState,
    setQbGradeId,
    setQbSubjectId,
    setParentId,
  })
</script>

<style lang="scss" scoped></style>
