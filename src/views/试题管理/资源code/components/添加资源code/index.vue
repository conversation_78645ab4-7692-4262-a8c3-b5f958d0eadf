<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="新建资源"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="label1"
        name="name"
        :rules="[{ required: true, message: `请输入${label1}!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label1}`"
        />
      </a-form-item>
      <a-form-item
        :label="label2"
        name="code"
        :rules="[{ required: true, message: `请输入${label2}!` }]"
      >
        <a-input
          v-model:value="formState.code"
          :placeholder="`请输入${label2}`"
        />
      </a-form-item>

      <a-form-item
        :label="label3"
        name="parent_id"
        :rules="[
          {
            required: true,
            type: 'array',
            message: `请选择${label3}名称!`,
          },
        ]"
      >
        <a-cascader
          v-model:value="formState.parent_id"
          change-on-select
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          :placeholder="`请选择${label3}`"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import {
    createQbQueSourceCodes,
    getQbQueSourceCodes,
  } from '~/src/api/试题/资源code'

  const label1 = '属性资源code名称'
  const label2 = 'code名称'
  const label3 = '绑定父级资源'
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    name: '',
    code: '',
    parent_id: [0],
  })
  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createQbQueSourceCodes({
      ...formState,
      parent_id: formState.parent_id[formState.parent_id.length - 1],
    })
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }
  const options = ref([])
  // const transformData = (data) => {
  //   if (!data) return null
  //   const transformed = {
  //     value: data.id,
  //     label: data.name,
  //   }
  //   if (data.children && data.children.length > 0) {
  //     transformed.children = data.children.map((child) => transformData(child))
  //   }
  //   return transformed
  // }
  const handleOptions = async () => {
    const { data } = await getQbQueSourceCodes({
      parent_id: 0,
    })
    options.value = data
    options.value.unshift({
      name: '顶级资源',
      id: 0,
    })
  }
  handleOptions()

  defineExpose({
    showModal,
    formState,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
