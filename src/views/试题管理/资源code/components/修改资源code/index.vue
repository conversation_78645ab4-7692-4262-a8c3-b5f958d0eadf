<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="`编辑${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `${label}名称不能为空!` }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import {
    editQbQueSourceCodes,
    getQbQueSourceCodesDetail,
  } from '@/api/试题/资源code'

  const label = '属性资源code'
  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const formRef = ref()
  const formState = reactive({
    name: '',
  })
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    await fetch()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  // 加载数据
  const fetch = async () => {
    const { data } = await getQbQueSourceCodesDetail(id.value)
    formState.name = data.name
  }
  // 提交
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editQbQueSourceCodes(
      {
        name: formState.name,
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('修改成功')
    }
    handleCancel()
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
