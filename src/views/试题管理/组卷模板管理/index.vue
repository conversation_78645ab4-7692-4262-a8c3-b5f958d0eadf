<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="关联的学段">
            <a-select
              v-model:value="formState.qb_grade_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="关联的学科">
            <a-select
              v-model:value="formState.qb_subject_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options2"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">组卷模板列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">创建模板</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['grade'].includes(column.dataIndex)">
          <a-tag color="orange">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="['subject'].includes(column.dataIndex)">
          <a-tag color="blue">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>
            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <CreateModal ref="createModal" @ok="init" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, provide, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getQbGrades } from '~/src/api/试题/学段'
  import { getQbSubjects } from '~/src/api/试题/学科'
  import { deleteQbQueTemplates, getQbQueTemplates } from '~/src/api/试题/模板'
  import { routerReplace } from '~/src/utils/routes'

  import CreateModal from './components/创建模板'

  const route = useRoute()
  // 搜索框label
  const label = '模板名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    qb_grade_id: route.query?.qb_grade_id
      ? parseInt(route.query?.qb_grade_id)
      : undefined,
    qb_subject_id: route.query?.qb_subject_id
      ? parseInt(route.query?.qb_subject_id)
      : undefined,
  })
  // 学段
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data
  }
  provide('options', options)
  handleOptions()
  // 学科
  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getQbSubjects()
    options2.value = data
  }
  handleOptions2()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbQueTemplates)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        subject: item.qb_subject.name,
        grade: item.qb_grade.name,
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '模板id',
      dataIndex: 'id',
    },
    {
      title: '模板名称',
      dataIndex: 'name',
    },
    {
      title: '学段',
      dataIndex: 'grade',
    },
    {
      title: '学科',
      dataIndex: 'subject',
    },

    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      qb_grade_id: formState.qb_grade_id,
      qb_subject_id: formState.qb_subject_id,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
      qb_grade_id: formState.qb_grade_id,
      qb_subject_id: formState.qb_subject_id,
    })
  }

  // 添加模板
  const createModal = ref()
  const onClick = () => {
    createModal.value?.showModal()
  }
  // 编辑表格
  const edit = (id) => {
    createModal.value?.setId(id)
    createModal.value?.showModal()
  }
  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteQbQueTemplates(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
