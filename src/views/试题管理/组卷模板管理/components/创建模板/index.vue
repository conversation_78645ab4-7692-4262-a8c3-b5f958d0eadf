<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? `编辑${label}` : `创建${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" layout="vertical" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `${label}名称不能为空!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label}名称`"
        />
      </a-form-item>
      <a-form-item
        label="学段学科"
        name="value"
        :rules="[
          { required: true, type: 'array', message: '学段学科不能为空!' },
        ]"
      >
        <a-cascader
          v-model:value="formState.value"
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
            children: 'qb_subjects',
          }"
          :options="options"
          :placeholder="`请选择学段学科`"
        />
      </a-form-item>
      <a-form-item label="年份" name="year">
        <a-checkbox-group
          v-model:value="formState.year"
          :options="formState.options1"
        />
      </a-form-item>
      <a-form-item label="地区" name="city">
        <a-cascader
          v-model:value="formState.city"
          change-on-select
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          multiple
          :options="formState.options2"
          :placeholder="`请选择地区`"
        />
      </a-form-item>

      <a-flex gap="10" wrap="wrap">
        <a-checkbox-group v-model:value="formState.questionType">
          <a-form-item
            v-for="(item, i) in formState.options3"
            :key="i"
            :label="item.name"
            :name="['options3', i, 'question_num']"
          >
            <a-checkbox :value="item.id">
              <a-input-number
                v-model:value="item.question_num"
                :controls="false"
                :min="0"
              />
            </a-checkbox>
          </a-form-item>
        </a-checkbox-group>
      </a-flex>

      <a-form-item label="难度" name="difficulty">
        <a-checkbox-group
          v-model:value="formState.difficulty"
          :options="formState.options4"
        />
      </a-form-item>
      <a-form-item label="学级" name="grade">
        <a-checkbox-group
          v-model:value="formState.grade"
          :options="formState.options5"
        />
      </a-form-item>
      <a-form-item label="学期" name="semester">
        <a-checkbox-group
          v-model:value="formState.semester"
          :options="formState.options6"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { inject, reactive, ref } from 'vue'

  import {
    createQbQueTemplates,
    editQbQueTemplates,
    getQbQueTemplatesDetail,
  } from '@/api/试题/模板'
  import { getQbQueSources } from '@/api/试题/资源'
  import { getQbQueSourceCodes } from '@/api/试题/资源code'
  import { uniqueArrays } from '@/utils'
  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const label = '模板'
  const formRef = ref()
  const formState = reactive({
    name: undefined,
    value: [],
    year: [],
    options1: [],
    city: [],
    cityAll: [],
    options2: [],
    questionType: [],
    options3: [],
    difficulty: [],
    options4: [],
    grade: [],
    options5: [],
    semester: [],
    options6: [],
  })
  // 资源code
  const sourceCodeArr = ref([])
  const handleSourceCodeArr = async () => {
    const { data } = await getQbQueSourceCodes({
      // parent_id: 0,
    })
    sourceCodeArr.value = data
    handleSourceArr()
  }
  handleSourceCodeArr()
  //资源
  const sourceArr = ref([])
  const handleSourceArr = async () => {
    const { data } = await getQbQueSources()
    sourceArr.value = data
    sourceCodeArr.value = sourceCodeArr.value.map((item) => {
      const array = []
      sourceArr.value.forEach((x) => {
        if (item.id == x.code_id) array.push(x)
      })
      return {
        child: array,
        ...item,
      }
    })
    sourceCodeArr.value = organizeData(sourceCodeArr.value)
    handlePlainOption()
    handleOptions2()
    handleOptions3()
    handleOptions4()
    handleOptions5()
    handleOptions6()
  }
  // 将 parent_id 等于某元素的 id 的情况处理为子元素
  const organizeData = (data) => {
    const idMap = {}
    data.forEach((item) => {
      idMap[item.id] = item
      item.children = []
    })
    data.forEach((item) => {
      if (item.parent_id !== 0 && idMap[item.parent_id]) {
        idMap[item.parent_id].children.push(item)
      }
    })
    const topLevelElements = data.filter((item) => item.parent_id === 0)
    return topLevelElements
  }

  // 转换函数
  const flattenChildren = (data) => {
    if (!data) return []
    const flattened = []
    function flatten(node) {
      flattened.push({
        ...node,
      })

      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => flatten(child))
      }
    }
    flatten(data)
    return flattened
  }
  const findIdArray = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.id === id) {
        return item
      }
      if (item.children && item.children.length > 0) {
        const foundInChildren = findIdArray(item.children, id)
        if (foundInChildren) {
          return foundInChildren
        }
      }
    }
    return null
  }

  const getOptionsByCode = (code) => {
    return sourceCodeArr.value
      .find((item) => {
        return item.code === code
      })
      .child.map((item) => {
        return {
          label: item.name,
          value: item.id,
          ...item,
        }
      })
  }
  const handlePlainOption = () => {
    formState.options1 = getOptionsByCode('year')
  }
  const handleOptions2 = () => {
    formState.options2 = getOptionsByCode('province')
  }
  const handleOptions3 = () => {
    formState.options3 = getOptionsByCode('question_type').map((item) => {
      return {
        ...item,
        question_num: 0,
      }
    })
  }
  const handleOptions4 = () => {
    formState.options4 = getOptionsByCode('difficulty_type')
  }
  const handleOptions5 = () => {
    formState.options5 = getOptionsByCode('grade')
  }
  const handleOptions6 = () => {
    formState.options6 = getOptionsByCode('semester')
  }

  const open = ref(false)
  const showModal = async () => {
    resetForm()
    id.value && (await init())
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const questionType = formState.options3
      .filter((item) => {
        return formState.questionType.includes(item.id)
      })
      .map((item) => {
        return {
          id: item.id,
          question_num: item.question_num,
        }
      })
    const sync = [...questionType]
    formState.year &&
      sync.push(
        ...formState.year.map((item) => {
          return {
            id: item,
          }
        })
      )

    if (formState.city.length > 0) {
      const city = new Set()
      formState.city.forEach((item) => {
        if (item.length < 3) {
          const cityFind = findIdArray(
            formState.options2,
            item[item.length - 1]
          )
          flattenChildren(cityFind).forEach((x) => {
            city.add(x.id)
          })
        }

        item.map((x) => city.add(x))
      })

      sync.push(
        ...Array.from(city).map((item) => {
          return {
            id: item,
          }
        })
      )
    }
    formState.difficulty &&
      sync.push(
        ...formState.difficulty.map((item) => {
          return {
            id: item,
          }
        })
      )
    formState.grade &&
      sync.push(
        ...formState.grade.map((item) => {
          return {
            id: item,
          }
        })
      )
    formState.semester &&
      sync.push(
        ...formState.semester.map((item) => {
          return {
            id: item,
          }
        })
      )
    let qbQueTemplatesData
    let code
    if (id.value) {
      const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
        sync.map((item) => item.id),
        formState.copyIdArray
      )
      qbQueTemplatesData = {
        name: formState.name,
        qb_que_sources: {
          sync: Array.from(
            new Map(
              uniqueToFirstArray
                .map((item) => ({ id: item }))
                .concat(questionType)
                .map((item) => [item.id, item])
            ).values()
          ),

          del: uniqueToSecondArray.map((item) => ({ id: item })),
        },
      }
      ;({ code } = await editQbQueTemplates(qbQueTemplatesData, id.value))
    } else {
      qbQueTemplatesData = {
        name: formState.name,
        qb_grade_id: formState.value[0],
        qb_subject_id: formState.value[1],
        qb_que_sources: { sync },
      }
      ;({ code } = await createQbQueTemplates(qbQueTemplatesData))
    }

    if (code == 200) {
      message.success(id.value ? `编辑成功` : '创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const resetForm = () => {
    formState.name = undefined
    formState.value = []
    formState.year = []
    formState.cityAll = []
    formState.city = []
    formState.difficulty = []
    formState.grade = []
    formState.semester = []
    formState.questionType = []
    formState.options3 = formState.options3.map((item) => {
      return {
        ...item,
        question_num: 0,
      }
    })
  }
  const handleCancel = async () => {
    id.value = undefined
    await formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  const options = inject('options')
  const formatData = (data, options) => {
    const result = []

    // 遍历湖北的直接子地区（武汉和黄石）
    data.children.forEach((city) => {
      const cityId = city.id
      const cityArray = [data.id, cityId]

      // 查找对应城市在options中的子地区
      const cityOptions = findIdArray(options, cityId)
      if (cityOptions) {
        const isSubset = city.children.length == cityOptions.children.length
        if (isSubset) {
          result.push(cityArray) // 如果城市在options2中已经包含所有子地区，只推入城市ID数组
        } else {
          cityOptions.children.forEach((area) => {
            if (city.children.some((child) => child.id === area.id)) {
              const areaArray = [...cityArray, area.id]
              result.push(areaArray)
            }
          })
        }
      } else {
        result.push(cityArray) // 如果options2中没有对应城市的数据，直接推入城市ID数组
      }
    })

    return result
  }

  const init = async () => {
    const { data } = await getQbQueTemplatesDetail(id.value)
    console.log(data)
    formState.name = data.name
    formState.value = [data.qb_grade_id, data.qb_subject_id]
    data.qb_que_sources.map((item) => {
      switch (item.code.code) {
        case 'year':
          formState.year.push(item.id)
          break
        case 'province':
          formState.cityAll = flattenChildren(item).map((x) => x.id)
          formState.city = formatData(item, formState.options2)
          break
        case 'question_type':
          if (formState.options3.find((x) => x.id == item.id))
            formState.options3.find((x) => x.id == item.id).question_num =
              item.question_num
          formState.questionType.push(item.id)
          break
        case 'difficulty_type':
          formState.difficulty.push(item.id)
          break
        case 'grade':
          formState.grade.push(item.id)
          break
        case 'semester':
          formState.semester.push(item.id)
          break
        default:
          break
      }
    })
    formState.copyIdArray = [
      ...new Set(
        data.qb_que_sources.map((item) => item.id).concat(...formState.cityAll)
      ),
    ]
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
