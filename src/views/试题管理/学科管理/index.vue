<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <!-- 查询有问题 -->
          <a-form-item label="关联的学段">
            <a-select
              v-model:value="formState.qb_grade_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">学科列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button :hidden="true" type="primary" @click="onClick">
        添加学科
      </a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name'].includes(column.dataIndex)">
          <div>
            <a-input
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
            />
            <template v-else>
              <a-tag color="blue">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-if="['grades'].includes(column.dataIndex)">
          <div>
            <a-select
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id].gradesArr"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              mode="tags"
              :options="options"
              placeholder="请选择学段"
              style="width: 200px"
            />
            <template v-else>
              <a-tag v-for="(item, i) in text" :key="i" color="orange">
                {{ item.name }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div>
            <span v-if="editableData[record.id]">
              <a-space :size="10">
                <a-button type="primary" @click="save(record.id)">
                  保存
                </a-button>
                <a-popconfirm title="确定取消?" @confirm="cancel(record.id)">
                  <a-button>取消</a-button>
                </a-popconfirm>
              </a-space>
            </span>
            <span v-else>
              <a-space :size="10">
                <a-button type="primary" @click="edit(record.id)">
                  编辑
                </a-button>

                <a-popconfirm
                  cancel-text="否"
                  ok-text="是"
                  title="是否确定删除?"
                  @confirm="onDelete(record.id)"
                >
                  <a-button danger :hidden="true" type="primary">删除</a-button>
                </a-popconfirm>
              </a-space>
            </span>
          </div>
        </template>
      </template>
    </a-table>

    <CreateModal ref="createModalRef" @ok="init" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getQbGrades } from '@/api/试题/学段'
  import {
    deleteQbSubjects,
    editQbSubjects,
    getQbSubjects,
  } from '@/api/试题/学科'
  import { uniqueArrays } from '@/utils'
  import { routerReplace } from '~/src/utils/routes'

  import CreateModal from './components/新建学科'

  const route = useRoute()
  // 搜索框label
  const label = '学科名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    qb_grade_id: route.query?.qb_grade_id
      ? parseInt(route.query?.qb_grade_id)
      : undefined,
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data
  }
  handleOptions()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbSubjects)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        gradesArr: item.grades.map((item) => item.id),
        ...item,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '关联学段',
      dataIndex: 'grades',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      qb_grade_id: formState.qb_grade_id,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
      qb_grade_id: formState.qb_grade_id,
    })
  }

  const editableData = reactive({})
  const edit = (id) => {
    editableData[id] = cloneDeep(
      dataSource.value.filter((item) => id === item.id)[0]
    )
  }
  const save = async (id) => {
    const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
      editableData[id].gradesArr,
      editableData[id].grades.map((item) => item.id)
    )

    const { code } = await editQbSubjects(
      {
        name: editableData[id].name,
        qb_grades: {
          sync: uniqueToFirstArray.map((item) => {
            return {
              id: item,
            }
          }),
          del: uniqueToSecondArray.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
      delete editableData[id]
    }
  }
  const cancel = (id) => {
    delete editableData[id]
  }
  const onDelete = async (id) => {
    const { code } = await deleteQbSubjects(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  // 新建学科
  const createModalRef = ref()
  const onClick = () => {
    createModalRef.value?.showModal()
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
