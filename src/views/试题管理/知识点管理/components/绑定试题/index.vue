<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="绑定试题"
    :width="900"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 2 }" :model="formState">
      <a-form-item label="试题主体" name="content">
        <a-input-search
          v-model:value="formState.content"
          allow-clear
          enter-button
          :placeholder="`输入试题主体快速查询`"
          @pressEnter="handleSearch"
          @search="handleSearch"
        />
      </a-form-item>
      <a-form-item label="地区" name="city">
        <a-cascader
          v-model:value="formState.city"
          change-on-select
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="formState.options2"
          :placeholder="`请选择地区`"
          style="width: 350px"
          @change="handleSearch"
        />
      </a-form-item>
      <a-form-item label="有无答案" name="has_answer">
        <a-select
          v-model:value="formState.has_answer"
          allow-clear
          :options="[
            {
              label: '有',
              value: 1,
            },
            {
              label: '无',
              value: 0,
            },
          ]"
          style="width: 140px"
          @change="handleSearch"
        />
      </a-form-item>
    </a-form>
    <a-flex align="center" gap="5">
      <span>已选中</span>
      <a-flex wrap="wrap">
        <a-tag
          v-for="(item, i) in state.selectedRows"
          :key="i"
          closable
          @close.prevent="onAlbumsClose(item.id)"
        >
          {{ item.id }}
        </a-tag>
      </a-flex>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :defaultExpandAllRows="true"
      :loading="loading"
      :pagination="pagination"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        preserveSelectedRowKeys: true,
      }"
      rowKey="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text }">
        <template
          v-if="
            [
              'question_type',
              'city',
              'difficulty_type',
              'qb_que_columns',
            ].includes(column.dataIndex)
          "
        >
          <a-tag v-for="(item, i) in text" :key="i">
            {{ item.name }}
          </a-tag>
        </template>
        <template v-else-if="['has_answer'].includes(column.dataIndex)">
          <template v-if="text == 1">
            <a-tag color="success">有</a-tag>
          </template>
          <template v-else>
            <a-tag color="error">无</a-tag>
          </template>
        </template>
        <template v-else-if="['qbGradeName'].includes(column.dataIndex)">
          <a-tag color="orange">{{ text }}</a-tag>
        </template>
        <template v-else-if="['qbSubjectName'].includes(column.dataIndex)">
          <a-tag color="blue">{{ text }}</a-tag>
        </template>
        <template v-else-if="!['id'].includes(column.dataIndex)">
          <a-tag>{{ text }}</a-tag>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <Editor
          v-model="record.content"
          :defaultConfig="{ readOnly: true }"
        />
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
  import { Editor } from '@wangeditor/editor-for-vue'
  import { message } from 'ant-design-vue'
  import { computed, inject, nextTick, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'

  import { editQbQueColumns } from '@/api/试题/知识点'
  import { getQbQueSources } from '@/api/试题/资源'
  import { getQbQueQuestions } from '@/api/试题/题目'

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    content: '',
    qb_grade_id: undefined,
    qb_subject_id: undefined,
    qb_que_source_ids: undefined,
    has_answer: undefined,
    qb_que_tab_id: undefined,
    city: [],
  })
  const open = ref(false)
  const handleCancel = () => {
    state.selectedRowKeys = []
    state.selectedRows = []
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = () => {
    handleSearch()
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editQbQueColumns(
      {
        qb_que_questions: {
          sync: state.selectedRowKeys.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }
  // 资源code
  const options3 = inject('options')

  // 资源code
  const sourceCodeArr = ref([])

  const handleSourceArr = async () => {
    const { data } = await getQbQueSources()
    sourceCodeArr.value = options3.value.map((item) => {
      const array = []
      data.forEach((x) => {
        if (item.id == x.code_id) array.push(x)
      })
      return {
        child: array,
        ...item,
      }
    })
    sourceCodeArr.value = organizeData(sourceCodeArr.value)
    handleOptions2()
  }
  const organizeData = (data) => {
    const idMap = {}
    data.forEach((item) => {
      idMap[item.id] = item
      item.children = []
    })
    data.forEach((item) => {
      if (item.parent_id !== 0 && idMap[item.parent_id]) {
        idMap[item.parent_id].children.push(item)
      }
    })
    const topLevelElements = data.filter((item) => item.parent_id === 0)
    return topLevelElements
  }
  handleSourceArr()
  const getOptionsByCode = (code) => {
    return sourceCodeArr.value
      .find((item) => {
        return item.code === code
      })
      .child.map((item) => {
        return {
          label: item.name,
          value: item.id,
          ...item,
        }
      })
  }
  const handleOptions2 = () => {
    formState.options2 = getOptionsByCode('province')
  }
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }
  // 表格
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbQueQuestions)

  const dataSource = computed(() => {
    if (!data.value) return []
    nextTick(() => {
      window.MathJax.typesetPromise()
    })
    // 地区
    const city_codeId = options3.value
      .filter((item) => ['province', 'city', 'area'].includes(item.code))
      .map((item) => item.id)
    // 题型
    const question_type = options3.value
      .filter((item) => ['question_type'].includes(item.code))
      .map((item) => item.id)
    // 难度
    const difficulty_type = options3.value
      .filter((item) => ['difficulty_type'].includes(item.code))
      .map((item) => item.id)
    return data.value.data.map((item) => {
      return {
        qbGradeName: item.qb_grade?.name,
        qbSubjectName: item.qb_subject?.name,
        qbQueTabName: item.qb_que_tab?.name,
        qbQueTabName_name: item.qb_que_tab?.parent?.name,
        city: item.qb_que_sources?.filter((x) =>
          city_codeId.includes(x.code_id)
        ),
        question_type: item.qb_que_sources?.filter((x) =>
          question_type.includes(x.code_id)
        ),
        difficulty_type: item.qb_que_sources?.filter((x) =>
          difficulty_type.includes(x.code_id)
        ),
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试题id',
      dataIndex: 'id',
    },
    // {
    //   title: '试题主体',
    //   dataIndex: 'content',
    // },
    {
      title: '题目类型',
      dataIndex: 'question_type',
    },
    {
      title: '地区',
      dataIndex: 'city',
    },
    {
      title: '难度',
      dataIndex: 'difficulty_type',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '知识点',
      dataIndex: 'qb_que_columns',
    },
    {
      title: '学段',
      dataIndex: 'qbGradeName',
    },
    {
      title: '学科',
      dataIndex: 'qbSubjectName',
    },
    {
      title: '教材版本',
      dataIndex: 'qbQueTabName',
    },
    {
      title: '教材年级',
      dataIndex: 'qbQueTabName_name',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      content: formState.content,
      unrelated_qb_que_column_id: id.value,
      qb_grade_id: formState.qb_grade_id,
      qb_subject_id: formState.qb_subject_id,
      has_answer: formState.has_answer,
      qb_que_tab_id: formState.qb_que_tab_id,
      qb_que_source_ids: formState.city
        ? [formState.city[formState.city.length - 1]]?.map((item) => {
            return {
              id: item,
            }
          })
        : undefined,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
  }

  // 批量删除
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
  })
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const onAlbumsClose = (value) => {
    state.selectedRowKeys = state.selectedRowKeys.filter((item) => {
      return item !== value
    })
    state.selectedRows = state.selectedRows.filter((item) => {
      return item.id !== value
    })
  }
  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
