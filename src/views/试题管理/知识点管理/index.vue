<template>
  <a-row justify="space-between">
    <a-col :span="5">
      <a-flex align="center" class="py-10" gap="10">
        <div><DefaultDivider /></div>
        <div class="font-bold">知识点列表</div>
      </a-flex>

      <a-form ref="formRef" layout="vertical" :model="formState">
        <a-form-item
          :label="label1"
          name="value"
          :rules="[
            { required: true, type: 'array', message: `请选择${label1}!` },
          ]"
        >
          <a-cascader
            v-model:value="formState.value"
            expand-trigger="hover"
            :fieldNames="{
              label: 'name',
              value: 'id',
              children: 'qb_subjects',
            }"
            :options="options"
            :placeholder="`请选择${label1}`"
            @change="onChange"
          />
        </a-form-item>
        <a-form-item
          :label="label2"
          name="qb_que_tab_id"
          :rules="[{ required: true, message: `请选择${label2}!` }]"
        >
          <a-cascader
            v-model:value="formState.qb_que_tab_id"
            :allowClear="false"
            expand-trigger="hover"
            :fieldNames="{
              label: 'name',
              value: 'id',
            }"
            :options="options2"
            :placeholder="`请选择${label2}`"
            @change="onChange2"
          />
        </a-form-item>
      </a-form>
      <a-input-search v-model:value="searchValue" style="margin-bottom: 8px" />
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :autoExpandParent="autoExpandParent"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
        @expand="onExpand"
        @select="onSelect"
      >
        <template #title="{ type, id, name, is_show }">
          <a-space :size="10">
            <a-dropdown :trigger="['contextmenu']">
              <span v-if="name.indexOf(searchValue) > -1">
                {{ name.substring(0, name.indexOf(searchValue)) }}
                <span style="color: #f50">{{ searchValue }}</span>
                {{
                  name.substring(name.indexOf(searchValue) + searchValue.length)
                }}
              </span>
              <span v-else>{{ name }}</span>
              <template #overlay>
                <a-menu
                  @click="({ key: menuKey }) => onContextMenuClick(menuKey, id)"
                >
                  <template v-if="type == 1">
                    <a-menu-item key="1">创建目录</a-menu-item>
                    <a-menu-item key="2">创建试题包(能绑定试题)</a-menu-item>
                  </template>
                  <a-menu-item key="4">修改名称</a-menu-item>
                  <a-menu-item key="3">删除知识点</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-switch
              :checked="is_show == 1"
              checked-children="发布"
              size="small"
              un-checked-children="未发布"
              @change="onChecked($event, id)"
            />
          </a-space>
        </template>

        <!-- <template #switcherIcon="{ switcherCls }">
          <down-outlined :class="switcherCls" />
        </template> -->
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-col>
    <a-col :span="18">
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label3">
            <a-input-search
              v-model:value="formState.content"
              allow-clear
              enter-button
              :placeholder="`输入${label3}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
      <a-divider style="height: 3px; background-color: #eeeeee" />
      <a-flex align="center" class="py-10" gap="10">
        <DefaultDivider />
        <div class="font-bold">试题列表</div>
      </a-flex>
      <a-flex class="py-10" gap="10">
        <a-flex gap="10" vertical>
          <a-flex gap="10">
            <a-button
              :disabled="formState.qb_que_tab_id.length !== 2"
              type="primary"
              @click="onContextMenuClick('1', 0)"
            >
              创建顶级知识点
            </a-button>
            <a-button
              :disabled="formState.qb_que_tab_id.length == 0 || disabled"
              type="primary"
              @click="onClick"
            >
              绑定试题
            </a-button>
          </a-flex>
          <a-flex gap="10">
            <a-popconfirm
              cancel-text="否"
              :disabled="!hasSelected"
              ok-text="是"
              title="是否确定解除?"
              @confirm="onAllDelete"
            >
              <a-button
                :disabled="!hasSelected"
                :loading="state.loading"
                type="primary"
              >
                解除绑定
              </a-button>
            </a-popconfirm>
            <a-flex v-if="hasSelected" align="center" gap="5">
              <span>已选中</span>
              <a-flex wrap="wrap">
                <a-tag
                  v-for="(item, i) in state.selectedRows"
                  :key="i"
                  closable
                  @close.prevent="onAlbumsClose(item.id)"
                >
                  {{ item.id }}
                </a-tag>
              </a-flex>
            </a-flex>
          </a-flex>
        </a-flex>
      </a-flex>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :defaultExpandAllRows="true"
        :loading="loading"
        :pagination="pagination"
        :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
        }"
        rowKey="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <template
            v-if="
              ['question_type', 'city', 'difficulty_type'].includes(
                column.dataIndex
              )
            "
          >
            <a-tag v-for="(item, i) in text" :key="i">
              {{ item.name }}
            </a-tag>
          </template>
          <template v-else-if="['has_answer'].includes(column.dataIndex)">
            <template v-if="text == 1">
              <a-tag color="success">有</a-tag>
            </template>
            <template v-else>
              <a-tag color="error">无</a-tag>
            </template>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space :size="10">
              <a-button type="primary" @click="onEdit(record.id)">
                编辑
              </a-button>

              <a-popconfirm
                cancel-text="否"
                ok-text="是"
                title="是否确定解除?"
                @confirm="onDelete(record)"
              >
                <a-button danger type="primary">解除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
        <template #expandedRowRender="{ record }">
          <Editor
            v-model="record.content"
            :defaultConfig="{ readOnly: true }"
          />
        </template>
      </a-table>
    </a-col>
    <CreateQuestions ref="createQuestionsRef" @ok="handleOk" />
    <CreateColumn ref="createColumnRef" @ok="handleOk" />
    <EditModal ref="editModalRef" @ok="handleOk" />
    <EditQuestions ref="editQuestionsRef" @ok="handleOk" />
  </a-row>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { Editor } from '@wangeditor/editor-for-vue'
  import { message } from 'ant-design-vue'
  import { computed, nextTick, provide, reactive, ref, watch } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getQbGrades } from '@/api/试题/学段'
  import {
    deleteQbQueColumns,
    editQbQueColumns,
    getQbQueColumns,
  } from '@/api/试题/知识点'
  import { getQbQueSourceCodes } from '@/api/试题/资源code'
  import { getQbQueTabs } from '@/api/试题/选项卡'
  import { getQbQueQuestions } from '@/api/试题/题目'
  import { modal } from '@/utils/modal'
  import { routerReplaceNoPagi } from '~/src/utils/routes'

  import EditQuestions from '../试题管理/components/创建试题'
  import EditModal from './components/修改知识点'
  import CreateColumn from './components/创建知识点'
  import CreateQuestions from './components/绑定试题'
  const route = useRoute()

  const label1 = '学段学科'
  const label2 = '教材-教材年级'
  const label3 = '试题主体'
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data
  }
  handleOptions()
  const options2 = ref([])
  const onChange = () => {
    formState.qb_que_tab_id = []
    treeData.value = []
    handleOptions2()
  }
  const handleOptions2 = async () => {
    if (!formState.value) return
    const { data } = await getQbQueTabs({
      parent_id: 0,
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
    })
    options2.value = data
  }
  const onChange2 = () => {
    fetch()
  }

  // 资源code
  const options3 = ref([])
  const handleOptions3 = async () => {
    const { data } = await getQbQueSourceCodes()
    options3.value = data
  }
  provide('options', options3)
  handleOptions3()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const searchValue = ref('')
  const formRef = ref()
  const formState = reactive({
    content: '',
    value: route.query?.qb_grade_id
      ? [
          parseInt(route.query?.qb_grade_id),
          parseInt(route.query?.qb_subject_id),
        ]
      : [],
    qb_que_tab_id: route.query?.qb_que_tab_id
      ? route.query?.qb_que_tab_id.split(',').map((item) => parseInt(item))
      : [],
  })

  // 展开指定的树节点
  const expandedKeys = ref(
    route.query?.expandedKeys
      ? route.query?.expandedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  const selectedKeys = ref(
    route.query?.selectedKeys
      ? route.query?.selectedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  // treeNodes 数据
  const treeData = ref([])
  const getParentKey = (id, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some((item) => item.id === id)) {
          parentKey = node.id
        } else if (getParentKey(id, node.children)) {
          parentKey = getParentKey(id, node.children)
        }
      }
    }
    return parentKey
  }
  // 树-所有项放到同级
  const dataList = ref([])
  const generateList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i]
      dataList.value.push({
        name: node.name,
        id: node.id,
      })
      if (node.children) {
        generateList(node.children)
      }
    }
  }

  // 是否自动展开
  const autoExpandParent = ref(true)
  // 树-展开事件
  const onExpand = (keys) => {
    expandedKeys.value = keys
    autoExpandParent.value = false
  }
  watch(searchValue, (value) => {
    generateList(treeData.value)
    const expanded = dataList.value
      .map((item) => {
        if (item.name.indexOf(value) > -1) {
          return getParentKey(item.id, treeData.value)
        }
        return null
      })
      .filter((item, i, self) => {
        return item && self.indexOf(item) === i
      })
    expandedKeys.value = expanded
    searchValue.value = value
    autoExpandParent.value = true
  })

  // 树-右键菜单
  const onContextMenuClick = (menuKey, id) => {
    switch (menuKey) {
      case '1':
      case '2':
        createColumnRef.value.formState.parent_id = id
        createColumnRef.value.formState.type = Number(menuKey)
        createColumnRef.value.formState.qb_que_tab_id =
          formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1]
        createColumnRef.value?.showModal()
        break
      case '3':
        handleDeleteColumns(id)
        break
      case '4':
        edit(id)
        break
      default:
        break
    }
  }
  // 删除知识点
  const handleDeleteColumns = async (value) => {
    modal(async () => {
      const { code, data } = await deleteQbQueColumns(value)
      if (code == 200) {
        message.success(data)
        expandedKeys.value.filter((item) => item.id == value)
      }
      await fetch()
    })
  }

  // 弹窗
  const createColumnRef = ref('')
  // 创建知识点-成功
  const handleOk = async () => {
    // expandedKeys.value = []
    await fetch()
    // 有表格加载id则加载
    qb_que_column_id.value && init()
  }

  const editQuestionsRef = ref()
  // 试题编辑
  const onEdit = (id) => {
    editQuestionsRef.value?.setId(id)
    editQuestionsRef.value?.showModal()
  }
  // 删除试题
  const onDelete = async (item) => {
    const { code } = await editQbQueColumns(
      {
        qb_que_questions: {
          del: [
            {
              id: item.id,
            },
          ],
        },
      },
      qb_que_column_id.value
    )
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('解除成功')
    }
  }
  // 知识点发布
  const onChecked = async (checked, id) => {
    const { code } = await editQbQueColumns(
      {
        is_show: checked == false ? 0 : 1,
      },
      id
    )
    handleOk()
    if (code == 200) {
      message.success('编辑成功')
    }
  }

  // 批量删除
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
    loading: false,
  })
  const hasSelected = computed(() => state.selectedRowKeys.length > 0)
  const onAllDelete = async () => {
    state.loading = true
    const { code } = await editQbQueColumns(
      {
        qb_que_questions: {
          del: state.selectedRowKeys.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      qb_que_column_id.value
    )
    state.loading = false
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('删除成功')
    }
  }
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const onAlbumsClose = (value) => {
    state.selectedRowKeys = state.selectedRowKeys.filter((item) => {
      return item !== value
    })
    state.selectedRows = state.selectedRows.filter((item) => {
      return item.id !== value
    })
  }

  // 绑定试题
  const createQuestionsRef = ref()
  const onClick = () => {
    createQuestionsRef.value.formState.qb_que_tab_id =
      formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1]
    createQuestionsRef.value.formState.qb_grade_id = formState.value[0]
    createQuestionsRef.value.formState.qb_subject_id = formState.value[1]
    createQuestionsRef.value.setId(qb_que_column_id.value)
    createQuestionsRef.value.showModal()
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  // 编辑
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 表格
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbQueQuestions)

  const dataSource = computed(() => {
    if (!data.value) return []
    nextTick(() => {
      window.MathJax.typesetPromise()
    })
    // 地区
    const city_codeId = options3.value
      .filter((item) => ['province', 'city', 'area'].includes(item.code))
      .map((item) => item.id)
    // 题型
    const question_type = options3.value
      .filter((item) => ['question_type'].includes(item.code))
      .map((item) => item.id)
    // 难度
    const difficulty_type = options3.value
      .filter((item) => ['difficulty_type'].includes(item.code))
      .map((item) => item.id)
    return data.value.data.map((item) => {
      return {
        qbGradeName: item.qb_grade.name,
        qbSubjectName: item.qb_subject.name,
        qbQueTabName: item.qb_que_tab.name,
        qbQueTabName_name: item.qb_que_tab.parent.name,
        city: item.qb_que_sources.filter((x) =>
          city_codeId.includes(x.code_id)
        ),
        question_type: item.qb_que_sources.filter((x) =>
          question_type.includes(x.code_id)
        ),
        difficulty_type: item.qb_que_sources.filter((x) =>
          difficulty_type.includes(x.code_id)
        ),
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试题id',
      dataIndex: 'id',
    },
    // {
    //   title: '试题主体',
    //   dataIndex: 'content',
    // },
    {
      title: '题目类型',
      dataIndex: 'question_type',
    },
    {
      title: '地区',
      dataIndex: 'city',
    },
    {
      title: '难度',
      dataIndex: 'difficulty_type',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      content: formState.content,
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
      qb_que_tab_id:
        formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1],
      qb_que_column_id: qb_que_column_id.value,
    })
    routerReplaceNoPagi({
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
      qb_que_tab_id: formState.qb_que_tab_id.join(','),
      expandedKeys: expandedKeys.value.join(','),
      qb_que_column_id: qb_que_column_id.value,
      selectedKeys: selectedKeys.value.join(','),
    })
  }
  const qb_que_column_id = ref(route.query?.qb_que_column_id)
  qb_que_column_id.value && init()
  const disabled = ref(true)
  // 点击树节点触发
  const onSelect = async (item, { node: { dataRef } }) => {
    await formRef.value?.validate()
    if (item.length == 0) return
    console.log(dataRef)
    disabled.value = dataRef.type == 2 ? false : true
    if (!expandedKeys.value.includes(dataRef.id))
      expandedKeys.value.push(dataRef.id)
    selectedKeys.value = [dataRef.id]
    pagination.value.current = 1
    qb_que_column_id.value = dataRef.id
    state.selectedRowKeys = []
    init()
  }

  const fetch = async () => {
    const { data } = await getQbQueColumns({
      qb_que_tab_id:
        formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1],
      parent_id: 0,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
      ],
    })
    treeData.value = data
  }
  if (route.query?.qb_que_tab_id) {
    handleOptions2()
    onChange2()
  }
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
