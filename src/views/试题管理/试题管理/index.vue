<template>
  <div>
    <a-flex align="center" justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10" justify="flex-end" wrap="wrap">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.content"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学段">
            <a-select
              v-model:value="formState.qb_grade_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学科">
            <a-select
              v-model:value="formState.qb_subject_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options2"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="地区">
            <a-cascader
              v-model:value="formState.province"
              change-on-select
              expand-trigger="hover"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options4"
              :placeholder="`请选择地区`"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="题型">
            <a-select
              v-model:value="formState.question_type"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options5"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="难度">
            <a-select
              v-model:value="formState.difficulty_type"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options6"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年份">
            <a-select
              v-model:value="formState.year"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options7"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学期">
            <a-select
              v-model:value="formState.semester"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options8"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年级">
            <a-select
              v-model:value="formState.grade"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options9"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">试题列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">创建试题</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :defaultExpandAllRows="true"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template
          v-if="
            [
              'question_type',
              'city',
              'difficulty_type',
              'qb_que_columns',
            ].includes(column.dataIndex)
          "
        >
          <a-tag v-for="(item, i) in text" :key="i">
            {{ item.name }}
          </a-tag>
        </template>
        <template v-else-if="['qb_que_columns'].includes(column.dataIndex)">
          <p v-for="(item, i) in text" :key="i">
            {{ item.name }}
          </p>
        </template>
        <template v-else-if="['has_answer'].includes(column.dataIndex)">
          <template v-if="text == 1">
            <a-tag color="success">有</a-tag>
          </template>
          <template v-else>
            <a-tag color="error">无</a-tag>
          </template>
        </template>
        <template v-else-if="['qbGradeName'].includes(column.dataIndex)">
          <a-tag color="orange">{{ text }}</a-tag>
        </template>
        <template v-else-if="['qbSubjectName'].includes(column.dataIndex)">
          <a-tag color="blue">{{ text }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>

            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
        <!-- <template v-else-if="!['id'].includes(column.dataIndex)">
          <a-tag>{{ text }}</a-tag>
        </template> -->
      </template>
      <template #expandedRowRender="{ record }">
        <Editor
          v-model="record.content"
          :defaultConfig="{ readOnly: true }"
        />
      </template>
    </a-table>
    <CreateQuestions ref="createQuestionsRef" @ok="handleOk" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { Editor } from '@wangeditor/editor-for-vue'
  import { message } from 'ant-design-vue'
  import {
    computed,
    provide,
    // nextTick,
    reactive,
    ref,
  } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getQbQueSources } from '@/api/试题/资源'
  import { getQbQueSourceCodes } from '@/api/试题/资源code'
  import { getQbGrades } from '~/src/api/试题/学段'
  import { getQbSubjects } from '~/src/api/试题/学科'
  import { deleteQbQueQuestions, getQbQueQuestions } from '~/src/api/试题/题目'
  import { routerReplace } from '~/src/utils/routes'

  import CreateQuestions from './components/创建试题'

  const route = useRoute()
  // 搜索框label
  const label = '试题主体'
  // 搜索表单
  const formState = reactive({
    content: route.query?.content,
    qb_grade_id: route.query?.qb_grade_id
      ? parseInt(route.query?.qb_grade_id)
      : undefined,
    qb_subject_id: route.query?.qb_subject_id
      ? parseInt(route.query?.qb_subject_id)
      : undefined,
    province: route.query?.province
      ? route.query?.province?.split(',').map((item) => Number(item))
      : [],
    question_type: route.query?.question_type
      ? parseInt(route.query?.question_type)
      : undefined,
    difficulty_type: route.query?.difficulty_type
      ? parseInt(route.query?.difficulty_type)
      : undefined,
    year: route.query?.year ? parseInt(route.query?.year) : undefined,
    semester: route.query?.semester
      ? parseInt(route.query?.semester)
      : undefined,
    grade: route.query?.grade ? parseInt(route.query?.grade) : undefined,
  })
  // 学段
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data
  }
  provide('options', options)
  handleOptions()
  // 学科
  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getQbSubjects()
    options2.value = data
  }
  handleOptions2()
  // 资源code
  const options3 = ref([])
  const handleOptions3 = async () => {
    const { data } = await getQbQueSourceCodes()
    options3.value = data
  }
  handleOptions3()
  // 区域
  const options4 = ref([])
  // 题型
  const options5 = ref([])
  // 难度
  const options6 = ref([])
  // 年份
  const options7 = ref([])
  // 学期
  const options8 = ref([])
  // 年级
  const options9 = ref([])
  const handleOptions4 = async () => {
    const { data } = await getQbQueSources({
      parent_id: 0,
    })
    data.forEach((item) => {
      switch (item.code.code) {
        case 'province':
          options4.value.push(item)
          break
        case 'question_type':
          options5.value.push(item)
          break
        case 'difficulty_type':
          options6.value.push(item)
          break
        case 'year':
          options7.value.push(item)
          break
        case 'semester':
          options8.value.push(item)
          break
        case 'grade':
          options9.value.push(item)
          break
        default:
          break
      }
    })
  }
  handleOptions4()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbQueQuestions)

  const dataSource = computed(() => {
    if (!data.value) return []
    // 地区
    const city_codeId = options3.value
      .filter((item) => ['province', 'city', 'area'].includes(item.code))
      .map((item) => item.id)
    // 题型
    const question_type = options3.value
      .filter((item) => ['question_type'].includes(item.code))
      .map((item) => item.id)
    // 难度
    const difficulty_type = options3.value
      .filter((item) => ['difficulty_type'].includes(item.code))
      .map((item) => item.id)
    return data.value.data.map((item) => {
      return {
        qbGradeName: item.qb_grade?.name,
        qbSubjectName: item.qb_subject?.name,
        qbQueTabName: item.qb_que_tab?.name,
        qbQueTabName_name: item.qb_que_tab?.parent?.name,
        city: item.qb_que_sources?.filter((x) =>
          city_codeId.includes(x.code_id)
        ),
        question_type: item.qb_que_sources?.filter((x) =>
          question_type.includes(x.code_id)
        ),
        difficulty_type: item.qb_que_sources?.filter((x) =>
          difficulty_type.includes(x.code_id)
        ),
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试题id',
      dataIndex: 'id',
    },
    {
      title: '题目类型',
      dataIndex: 'question_type',
    },
    {
      title: '地区',
      dataIndex: 'city',
    },
    {
      title: '难度',
      dataIndex: 'difficulty_type',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '知识点',
      dataIndex: 'qb_que_columns',
    },
    {
      title: '学段',
      dataIndex: 'qbGradeName',
    },
    {
      title: '学科',
      dataIndex: 'qbSubjectName',
    },
    {
      title: '教材版本',
      dataIndex: 'qbQueTabName',
    },
    {
      title: '教材年级',
      dataIndex: 'qbQueTabName_name',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    const must_qb_que_source_ids = []
    // 检查并添加值到数组
    const addIfExists = (value) => {
      if (value) {
        must_qb_que_source_ids.push(value)
      }
    }
    // 检查 province 是否存在并是数组
    if (Array.isArray(formState.province) && formState.province.length > 0) {
      addIfExists(formState.province[formState.province.length - 1])
    }
    addIfExists(formState.question_type)
    addIfExists(formState.difficulty_type)
    addIfExists(formState.year)
    addIfExists(formState.semester)
    addIfExists(formState.grade)
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      content: formState.content,
      qb_grade_id: formState.qb_grade_id, // 学段
      qb_subject_id: formState.qb_subject_id, // 学科
      must_qb_que_source_ids,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      content: formState.content,
      qb_grade_id: formState.qb_grade_id,
      qb_subject_id: formState.qb_subject_id,
      must_qb_que_source_ids: formState.province?.join(','),
      question_type: formState.question_type,
      difficulty_type: formState.difficulty_type,
      year: formState.year,
      semester: formState.semester,
      grade: formState.grade,
    })
  }

  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteQbQueQuestions(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }
  // 创建试题
  const createQuestionsRef = ref('')
  const onClick = () => createQuestionsRef.value?.showModal()
  const handleOk = () => {
    init()
  }
  // 编辑试题
  const edit = (id) => {
    createQuestionsRef.value?.setId(id)
    createQuestionsRef.value?.showModal()
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
