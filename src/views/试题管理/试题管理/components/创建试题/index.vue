<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :maskClosable="false"
    :title="id ? '编辑试题' : '创建试题'"
    :width="1000"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" layout="vertical" :model="formState">
      <a-form-item
        label="试题主体"
        name="content"
        :rules="[{ required: true, message: '试题主体不能为空' }]"
      >
        <Toolbar
          ref="editor"
          :defaultConfig="toolbarConfig"
          :editor="editorRef"
          :mode="mode"
          style="border-bottom: 1px solid #ccc"
        />
        <Editor
          v-model="formState.content"
          :defaultConfig="editorConfig"
          :defaultHtml="defaultHtml"
          :mode="mode"
          style="height: 300px; overflow-y: hidden; line-height: 2"
          @onCreated="handleCreated"
        />
      </a-form-item>
      <a-form-item label="解析" name="answer">
        <Toolbar
          ref="editor2"
          :defaultConfig="toolbarConfig"
          :editor="editor2Ref"
          :mode="mode"
          style="border-bottom: 1px solid #ccc"
        />
        <Editor
          v-model="formState.answer"
          :defaultConfig="editorConfig"
          :defaultHtml="defaultHtml2"
          :mode="mode"
          style="height: 300px; overflow-y: hidden; line-height: 2"
          @onCreated="handleCreated2"
        />
      </a-form-item>
      <a-form-item
        label="学段学科"
        name="value"
        :rules="[{ required: true, type: 'array', message: `请选择学段学科!` }]"
      >
        <a-cascader
          v-model:value="formState.value"
          :allowClear="false"
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
            children: 'qb_subjects',
          }"
          :options="options"
          :placeholder="`请选择学段学科`"
          @change="onChange"
        />
      </a-form-item>
      <a-form-item
        v-if="formState?.value.length !== 0"
        label="选择组卷模板"
        name="value2"
        :rules="[{ required: true, message: '组卷模板不能为空' }]"
      >
        <a-select
          v-model:value="formState.value2"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options2"
          :placeholder="`请选择组卷模板`"
          @change="onChange2"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options1.length !== 0"
        label="年份"
        name="year"
      >
        <a-radio-group
          v-model:value="formState.year"
          :options="formState.options1"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options2.length !== 0"
        label="地区"
        name="city"
      >
        <a-cascader
          v-model:value="formState.city"
          change-on-select
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :multiple="true"
          :options="formState.options2"
          :placeholder="`请选择地区`"
          :showCheckedStrategy="Cascader.SHOW_CHILD"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options3.length !== 0"
        label="题型"
        name="questionType"
      >
        <a-radio-group
          v-model:value="formState.questionType"
          :options="formState.options3"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options4.length !== 0"
        label="难度"
        name="difficulty"
      >
        <a-radio-group
          v-model:value="formState.difficulty"
          :options="formState.options4"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options5.length !== 0"
        label="学级"
        name="grade"
      >
        <a-radio-group
          v-model:value="formState.grade"
          :options="formState.options5"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.options6.length !== 0"
        label="学期"
        name="semester"
      >
        <a-radio-group
          v-model:value="formState.semester"
          :options="formState.options6"
        />
      </a-form-item>
      <a-form-item
        v-if="formState?.value.length !== 0"
        label="教材-教材年级"
        name="qb_que_tab_id"
        :rules="[{ required: true, message: `请选择教材-教材年级!` }]"
      >
        <a-cascader
          v-model:value="formState.qb_que_tab_id"
          :allowClear="false"
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options3"
          :placeholder="`请选择教材-教材年级`"
          @change="onChange3"
        />
      </a-form-item>
      <a-tree
        v-model:checkedKeys="checkedKeys"
        :checkable="true"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
      >
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import { Cascader, message } from 'ant-design-vue'
  import { inject, onBeforeUnmount, reactive, ref, shallowRef } from 'vue'

  import { getQbQueTemplates, getQbQueTemplatesDetail } from '@/api/试题/模板'
  import { getQbQueColumns } from '@/api/试题/知识点'
  import { getQbQueTabs } from '@/api/试题/选项卡'
  import {
    createQbQueQuestions,
    editQbQueQuestions,
    getQbQueQuestionsDetail,
  } from '@/api/试题/题目'
  import { mode, toolbarConfig } from '@/config'
  import { uniqueArrays } from '@/utils'
  import editorConfig from '@/utils/editorConfig'
  // 试题主体
  const defaultHtml = ref('<p style="line-height: 2;"></p>')
  const editor = ref()
  const editorRef = shallowRef()
  const handleCreated = (editor) => {
    editorRef.value = editor // 记录 editor 实例，重要！
  }
  // 解析
  const defaultHtml2 = ref('<p style="line-height: 2;"></p>')
  const editor2 = ref()
  const editor2Ref = shallowRef()
  const handleCreated2 = (editor) => {
    console.log(editor.getAllMenuKeys())
    editor2Ref.value = editor // 记录 editor 实例，重要！
  }

  // 组件销毁时，也及时销毁编辑器
  onBeforeUnmount(() => {
    const editor = editorRef.value
    const editor2 = editor2Ref.value
    if (editor == null) return
    if (editor2 == null) return
    editor.destroy()
    editor2.destroy()
  })

  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    content: '',
    answer: '',
    value: [],
    value2: undefined,
    year: undefined,
    options1: [],
    city: [],
    options2: [],
    questionType: undefined,
    options3: [],
    difficulty: undefined,
    options4: [],
    grade: undefined,
    options5: [],
    semester: undefined,
    options6: [],
    qb_que_tab_id: [],
  })

  // 重置表单
  const resetForm = () => {
    formState.value2 = undefined
    formState.year = undefined
    formState.options1 = []
    formState.city = []
    formState.options2 = []
    formState.questionType = undefined
    formState.options3 = []
    formState.difficulty = undefined
    formState.options4 = []
    formState.grade = undefined
    formState.options5 = []
    formState.semester = undefined
    formState.options6 = []
    formState.qb_que_tab_id = []
    checkedKeys.value = []
    treeData.value = []
  }
  const open = ref(false)
  const showModal = async () => {
    formState.value = []
    formState.content = ''
    formState.answer = ''
    resetForm()
    if (id.value) {
      await init()
    }
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()

    let requestData
    let code
    const sources_sync = [
      formState.year,
      formState.difficulty,
      formState.grade,
      formState.semester,
      formState.questionType,
      ...formState.city.map((item) => item[item.length - 1]),
    ]
      .filter((item) => item)
      .map((item) => {
        return {
          id: item,
        }
      })

    const columns_sync = checkedKeys.value.map((item) => {
      return {
        id: item,
      }
    })

    if (id.value) {
      const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
        sources_sync.map((item) => item.id),
        formState.qb_que_sources
      )
      const {
        uniqueToFirstArray: uniqueToFirstArray2,
        uniqueToSecondArray: uniqueToSecondArray2,
      } = uniqueArrays(
        columns_sync.map((item) => item.id),
        formState.qb_que_columns
      )

      requestData = {
        content: formState.content,
        answer: formState.answer,
        qb_grade_id: formState.value[0],
        qb_subject_id: formState.value[1],
        qb_que_tab_id:
          formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1],
        qb_que_columns: {
          sync: uniqueToFirstArray2.map((item) => {
            return {
              id: item,
            }
          }),
          del: uniqueToSecondArray2.map((item) => {
            return {
              id: item,
            }
          }),
        },
        qb_que_sources: {
          sync: uniqueToFirstArray.map((item) => {
            return {
              id: item,
            }
          }),
          del: uniqueToSecondArray.map((item) => {
            return {
              id: item,
            }
          }),
        },
      }
      ;({ code } = await editQbQueQuestions(requestData, id.value))
    } else {
      requestData = {
        content: formState.content,
        answer: formState.answer,
        qb_grade_id: formState.value[0],
        qb_subject_id: formState.value[1],
        qb_que_tab_id:
          formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1],
        qb_que_columns: {
          sync: columns_sync,
        },
        qb_que_sources: {
          sync: sources_sync,
        },
      }
      ;({ code } = await createQbQueQuestions(requestData))
    }

    if (code == 200) {
      message.success(id.value ? `编辑成功` : '创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    id.value = undefined
    defaultHtml.value = '<p style="line-height: 2;"></p>'
    defaultHtml2.value = '<p style="line-height: 2;"></p>'
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  // 学段学科
  const options = inject('options')

  const onChange = () => {
    resetForm()
    handleOptions2()
    handleOptions3()
  }
  // 组卷模板
  const options2 = ref([])
  const handleOptions2 = async () => {
    if (!formState.value) return
    const { data } = await getQbQueTemplates({
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
    })
    options2.value = data
    formState.value2 = data?.[0]?.id
    await onChange2()
  }
  const onChange2 = async () => {
    if (!formState.value2) return
    const { data } = await getQbQueTemplatesDetail(formState.value2)
    data.qb_que_sources.map((item) => {
      switch (item.code.code) {
        case 'year':
          formState.options1.push({
            label: item.name,
            value: item.id,
          })
          break
        case 'province':
          formState.options2 = [item]
          break
        case 'question_type':
          formState.options3.push({
            label: item.name,
            value: item.id,
            ...item,
          })
          break
        case 'difficulty_type':
          formState.options4.push({
            label: item.name,
            value: item.id,
          })
          break
        case 'grade':
          formState.options5.push({
            label: item.name,
            value: item.id,
          })
          break
        case 'semester':
          formState.options6.push({
            label: item.name,
            value: item.id,
          })
          break
        default:
          break
      }
    })
  }
  // 教材-教材年级
  const options3 = ref([])
  const handleOptions3 = async () => {
    if (!formState.value) return
    const { data } = await getQbQueTabs({
      parent_id: 0,
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
    })
    options3.value = data
  }
  const onChange3 = async () => {
    const { data } = await getQbQueColumns({
      qb_que_tab_id:
        formState.qb_que_tab_id[formState.qb_que_tab_id.length - 1],
      parent_id: 0,
    })
    // formState.qb_que_columns = []
    treeData.value = addDisabledToChildren(data)
  }
  // 递归函数：给树形结构的每个节点添加 disabled: true 字段
  const addDisabledToChildren = (array) => {
    return array.map((item) => ({
      ...item,
      disabled: item.type == 1 ? true : false,
      children: item.children ? addDisabledToChildren(item.children) : [], // 递归处理子对象
    }))
  }
  // 树节点
  const checkedKeys = ref([])
  // treeNodes 数据
  const treeData = ref([])

  // 递归函数获取指定id的所有父级id
  const getParentIds = (data, targetId) => {
    const parentIds = []

    // 辅助函数，递归查找父级id
    function findParentIds(currentData) {
      for (let i = 0; i < currentData.length; i++) {
        const item = currentData[i]
        if (item.id === targetId) {
          parentIds.push(item.id) // 将当前节点的id加入结果数组
          return true // 找到目标节点，返回true表示找到了
        }
        if (item.children && item.children.length > 0) {
          if (findParentIds(item.children)) {
            parentIds.push(item.id) // 将当前节点的id加入结果数组
            return true // 在子节点中找到目标节点，返回true表示找到了
          }
        }
      }
      return false // 遍历完当前层级未找到目标节点，返回false表示未找到
    }

    // 开始查找指定id的所有父级id
    findParentIds(data)

    return parentIds.reverse() // 返回结果需要逆序，使得根节点id在最前面
  }

  const init = async () => {
    const { data } = await getQbQueQuestionsDetail(id.value)
    console.log(data)
    formState.content = data.content
    formState.answer = data.answer
    formState.value = [data.qb_grade_id, data.qb_subject_id]
    defaultHtml.value = data.content
    defaultHtml2.value = data.answer
    resetForm()
    handleOptions3()
    formState.qb_que_tab_id = [data.qb_que_tab.parent.id, data.qb_que_tab.id]
    onChange3()
    await handleOptions2()

    data.qb_que_sources.map((item) => {
      switch (item.code.code) {
        case 'year':
          formState.year = item.id
          break
        case 'province':
        case 'city':
        case 'area':
          formState.city.push(getParentIds(formState.options2, item.id))
          break
        case 'question_type':
          formState.questionType = item.id
          break
        case 'difficulty_type':
          formState.difficulty = item.id
          break
        case 'grade':
          formState.grade = item.id
          break
        case 'semester':
          formState.semester = item.id
          break
        default:
          break
      }
    })
    checkedKeys.value = data.qb_que_columns.map((item) => item.id)
    formState.qb_que_columns = data.qb_que_columns.map((item) => item.id)
    formState.qb_que_sources = data.qb_que_sources.map((item) => item.id)
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped></style>
