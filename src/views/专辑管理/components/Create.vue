<template>
  <a-modal
    :destroyOnClose="true"
    :open="show"
    style="width: 1000px"
    title="新建专辑"
    @cancel="onClose"
    @ok="onSubmit"
  >
    <a-form ref="formRef" :label-col="{ span: 3 }" :model="form" :rules="rules">
      <a-form-item label="专辑类型" name="type">
        <a-select
          v-model:value="form.type"
          allow-clear
          disabled
          :options="typeOptions"
        />
      </a-form-item>
      <a-form-item label="专辑名称" name="title">
        <a-input v-model:value="form.title" placeholder="请输入专辑名称" />
      </a-form-item>
      <a-form-item label="供应商" name="cp">
        <a-select v-model:value="form.cp" allow-clear :options="cpOptions" />
      </a-form-item>
      <a-form-item label="封面" name="image1">
        <Audio ref="AudioRef" @setImageAddress="handleParamsCover_url_id" />
      </a-form-item>
      <a-form-item label="所属学级" name="grades">
        <a-select
          v-model:value="form.grades"
          allow-clear
          mode="multiple"
          :options="gradesOptions"
        />
      </a-form-item>
      <a-form-item label="所属学科" name="subjects">
        <a-select
          v-model:value="form.subjects"
          allow-clear
          mode="multiple"
          :options="subjectsOptions"
        />
      </a-form-item>
      <a-form-item label="专辑介绍" name="desc">
        <a-textarea
          v-model:value="form.desc"
          :autoSize="{ minRows: 2, maxRows: 4 }"
          showCount
        />
      </a-form-item>
      <a-form-item v-if="form.type == 2" label="绑定专辑">
        <a-button type="primary" @click="handleAlbumClick">绑定</a-button>
      </a-form-item>
      <a-form-item
        v-if="form.type == 2 && form.albums.length > 0"
        label="专辑队列"
        name="albums"
      >
        <template v-for="item in form.albums" :key="item">
          <a-tag closable @close="onAlbumsClose(item)">
            {{ item }}
          </a-tag>
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
  <Modal ref="modalRef" @cancel="modalCancel" @ok="modalOk" />
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { inject, reactive, ref, toRefs } from 'vue'

  import { createAlbums } from '@/api/专辑'
  import { analyzeArray, findIndex } from '@/utils'
  import Audio from '@/views/audio'
  import Modal from '@/views/绑定专辑'

  const props = defineProps(['params'])
  const { show, type } = toRefs(props.params)

  // 表单参数
  const typeOptions = ref([
    { value: 1, label: '视频专辑' },
    { value: 2, label: '专辑包' },
  ])
  const gradesOptions = inject('gradesOptions')
  const subjectsOptions = inject('subjectsOptions')
  const cpOptions = inject('cpOptions')

  // 表单
  const formRef = ref()
  const form = reactive({
    title: '',
    type: type.value,
    grades: [],
    subjects: [],
    cp: '',
    albums: [],
    desc: '',
    image1: '',
    image2: '',
  })
  const syncArr = ref([])
  const audioRequired = (_rule, value) => {
    if (value == '') {
      return Promise.reject('请上传封面图片')
    }
    return Promise.resolve()
  }
  const limitLen = 256
  const textareaRequired = (_rule, value) => {
    if (value.length >= limitLen) {
      return Promise.reject('字数超过限制')
    }
    return Promise.resolve()
  }
  const rules = {
    title: [{ required: true, message: '专辑名称不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '专辑类型不能为空', trigger: 'change' }],
    cp: [{ required: true, message: '供应商不能为空', trigger: 'change' }],
    image1: [{ required: true, trigger: 'change', validator: audioRequired }],
    desc: [{ required: false, trigger: 'change', validator: textareaRequired }],
  }

  // 修改封面
  const AudioRef = ref('')
  const handleParamsCover_url_id = (value) => {
    form.image1 = value
  }

  // 删除表单 单个标签
  const onAlbumsClose = (val) => {
    const index = findIndex(form.albums, val)
    form.albums.splice(index, 1)
  }

  // 退出表单
  const onClose = () => {
    formRef.value?.resetFields()
    show.value = false
    syncArr.value = []
    form.albums = []
  }
  // 提交表单
  const onSubmit = async () => {
    await formRef.value?.validate()
    const albums = {}
    if (syncArr.value.length > 0) {
      albums['sync'] = syncArr.value
    }
    const params = {
      ...getBasicParams(form),
      albums,
    }
    console.log(params)
    const { code } = await createAlbums(params)
    code == 200 &&
      (props.params?.init(), message.success('创建成功'), onClose())
  }
  // 绑定
  const handleAlbumClick = () => {
    modalRef.value.showDrawer()
  }

  // 组件
  const modalRef = ref()
  const modalCancel = () => {
    console.log(1)
  }
  const modalOk = (albums) => {
    console.log(albums.value)
    form.albums = albums.value
    syncArr.value = albums.value.map((item) => {
      return {
        id: parseInt(item.split('-')[0]),
        alias: '',
        orderby: 0,
      }
    })
    console.log(syncArr.value)
  }

  // 基本参数,避免代码重复
  const getBasicParams = (record) => {
    return {
      title: record.title,
      type: record.type,
      grades: analyzeArray(record.grades),
      subjects: analyzeArray(record.subjects),
      cp_id: record.cp,
      desc: record.desc,
      image1: record.image1,
      image2: record.image2,
    }
  }
</script>

<style scoped>
  .flex_item {
    margin-left: 5px;
  }
</style>
