<template>
  <a-modal
    :destroyOnClose="true"
    :open="show"
    style="width: 1000px"
    title="编辑专辑"
    @cancel="onClose"
    @ok="onSubmit"
  >
    <a-form
      ref="formRef"
      :label-col="{ span: 3 }"
      :model="Content"
      :rules="rules"
    >
      <a-form-item label="专辑名称" name="title">
        <a-input v-model:value="Content.title" placeholder="请输入专辑名称" />
      </a-form-item>
      <a-form-item label="供应商" name="cp">
        <a-select v-model:value="Content.cp" allow-clear :options="cpOptions" />
      </a-form-item>
      <a-form-item label="封面" name="image1">
        <Audio
          ref="AudioRef"
          @init="getImageAddress"
          @setImageAddress="handleParamsCover_url_id"
        />
      </a-form-item>
      <a-form-item label="所属学级" name="grades">
        <a-select
          v-model:value="Content.grades"
          mode="multiple"
          :options="gradesOptions"
        />
      </a-form-item>
      <a-form-item label="所属学科" name="subjects">
        <a-select
          v-model:value="Content.subjects"
          mode="multiple"
          :options="subjectsOptions"
        />
      </a-form-item>
      <a-form-item label="专辑介绍" name="desc">
        <a-textarea
          v-model:value="Content.desc"
          :autoSize="{ minRows: 2, maxRows: 4 }"
          showCount
        />
      </a-form-item>
      <a-form-item
        v-if="Content.type == 2 && Content.albums2.length > 0"
        label="已绑专辑列表"
        name="Content"
      >
        <template v-for="item in Content.albums2" :key="item">
          <a-tag closable @close="onAlbumsClose(item)">
            {{ item }}
          </a-tag>
        </template>
      </a-form-item>
      <a-form-item v-if="Content.type == 2" label="绑定专辑">
        <a-button type="primary" @click="handleAlbumClick">绑定</a-button>
      </a-form-item>
      <a-form-item
        v-if="Content.type == 2 && form.albums.length > 0"
        label="专辑队列"
        name="albums"
      >
        <template v-for="item in form.albums" :key="item">
          <a-tag closable @close="onAddAlbumsClose(item)">
            {{ item }}
          </a-tag>
        </template>
      </a-form-item>
    </a-form>
  </a-modal>
  <Modal ref="modalRef" @cancel="modalCancel" @ok="modalOk" />
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { inject, reactive, ref, toRefs } from 'vue'

  import { editAlbums } from '@/api/专辑'
  import { analyzeArray, findIndex } from '@/utils'
  import Audio from '@/views/audio'
  import Modal from '@/views/绑定专辑'

  const props = defineProps(['params'])
  const { show, Content } = toRefs(props.params)

  const gradesOptions = inject('gradesOptions')
  const subjectsOptions = inject('subjectsOptions')
  const cpOptions = inject('cpOptions')

  // 表单
  const formRef = ref()
  const form = reactive({
    albums: [],
    delAlbums: [],
  })
  const syncArr = ref([])
  const delArr = ref([])
  const audioRequired = (_rule, value) => {
    if (value == '') {
      return Promise.reject('请上传封面图片')
    }
    return Promise.resolve()
  }
  const limitLen = 256
  const textareaRequired = (_rule, value) => {
    if (value.length >= limitLen) {
      return Promise.reject('字数超过限制')
    }
    return Promise.resolve()
  }
  const rules = {
    title: [{ required: true, message: '专辑名称不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '专辑类型不能为空', trigger: 'change' }],
    cp: [{ required: true, message: '供应商不能为空', trigger: 'change' }],
    image1: [{ required: true, trigger: 'change', validator: audioRequired }],
    desc: [{ required: false, trigger: 'change', validator: textareaRequired }],
  }

  // 修改封面
  const AudioRef = ref('')
  const getImageAddress = () => {
    AudioRef.value?.fileList.push({
      url: Content.value.image1_url,
    })
  }
  const handleParamsCover_url_id = (value) => {
    Content.value.image1 = value
  }

  // 删除表单 单个标签
  const onAlbumsClose = (val) => {
    const index = findIndex(Content.value.albums2, val)
    const delItem = Content.value.albums2.splice(index, 1)
    const id = parseInt(delItem[0].split('-')[0])
    const _index = syncArr.value.findIndex((item) => item.id === id)
    console.log(_index)
    if (_index > -1) {
      syncArr.value.splice(_index, 1)
    } else if (delArr.value.find((item) => item.id === id) === undefined) {
      delArr.value.push({
        id,
      })
    }
  }
  // 删除队列 单个标签
  const onAddAlbumsClose = (val) => {
    const index = findIndex(form.albums, val)
    const delItem = form.albums.splice(index, 1)
    const id = parseInt(delItem[0].split('-')[0])
    const _index = syncArr.value.findIndex((item) => item.id === id)
    console.log(_index)
    if (_index > -1) {
      syncArr.value.splice(_index, 1)
    }
  }

  // 退出表单
  const onClose = () => {
    formRef.value?.resetFields()
    show.value = false
    syncArr.value = []
    delArr.value = []
    form.albums = []
  }
  // 提交表单
  const onSubmit = async () => {
    await formRef.value?.validate()
    const albums = {}
    if (syncArr.value.length > 0) {
      albums['sync'] = syncArr.value
    }
    if (delArr.value.length > 0) {
      albums['del'] = delArr.value
    }
    const params = {
      ...getBasicParams(Content.value),
      albums,
    }
    console.log(params)
    const { code } = await editAlbums(params, Content.value.id)
    code == 200 &&
      (props.params?.init(), message.success('修改成功'), onClose())
  }

  // 绑定
  const handleAlbumClick = () => {
    modalRef.value.setAlbumId(Content.value.id)
    modalRef.value.showDrawer()
  }

  // 组件
  const modalRef = ref()
  const modalCancel = () => {
    console.log(1)
  }
  const modalOk = (albums) => {
    console.log(albums.value)
    form.albums = albums.value
    syncArr.value = albums.value.map((item) => {
      return {
        id: parseInt(item.split('-')[0]),
        alias: '',
        orderby: 0,
      }
    })
    console.log(syncArr.value)
  }

  // 基本参数,避免代码重复
  const getBasicParams = (record) => {
    return {
      title: record.title,
      type: record.type,
      grades: analyzeArray(record.grades),
      subjects: analyzeArray(record.subjects),
      cp_id: record.cp,
      desc: record.desc,
      image1: record.image1,
      image2: record.image2,
    }
  }
</script>

<style scoped>
  .flex_item {
    margin-left: 5px;
  }
</style>
