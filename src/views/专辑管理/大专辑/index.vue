<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><PictureOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form ref="formRef" :model="formState">
        <a-flex gap="10">
          <a-form-item label="专辑名称" name="title">
            <a-input-search
              v-model:value="formState.title"
              allow-clear
              enter-button
              placeholder="输入专辑名称快速查询"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学级" name="grades">
            <VabSelectOption
              :formName="'grades'"
              :formState="formState"
              :options="gradesOptions"
              :style="style"
              @change="handleFormItemChange"
            />
          </a-form-item>
          <a-form-item label="学科" name="subjects">
            <VabSelectOption
              :formName="'subjects'"
              :formState="formState"
              :options="subjectsOptions"
              :style="style"
              @change="handleFormItemChange"
            />
          </a-form-item>
          <a-form-item label="供应商" name="cp">
            <VabSelectOption
              :formName="'cp'"
              :formState="formState"
              :options="cpOptions"
              :style="style"
              @change="handleFormItemChange"
            />
          </a-form-item>
          <a-form-item label="栏目" name="top_column_id">
            <VabSelectOption
              :formName="'top_column_id'"
              :formState="formState"
              :options="columnsOptions"
              :style="style"
              @change="handleFormItemChange"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">专辑包列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onCreate">新建专辑</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :expandedRowKeys="expandedRowKeys"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      @expand="handleTableExpand"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'gradesNames'">
          <a-tag v-for="tag in record.gradesNames" :key="tag" color="orange">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'subjectsNames'">
          <a-tag v-for="tag in record.subjectsNames" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'cpName'">
          <a-tag color="purple">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'top_columns'">
          <a-tag v-for="tag in record.top_columns" :key="tag" color="green">
            {{ tag.name }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'image'">
          <a-image :src="record.image1_url" style="width: 100px" />
          <a-button
            danger
            style="display: none"
            type="text"
            @click="editImage(record)"
          >
            修改
          </a-button>
        </template>
        <template v-else-if="column.dataIndex === 'enable'">
          <a-tooltip>
            <template #title>点击{{ record.enableTitle }}</template>
            <a-switch
              :checked="record.enable == 1"
              checked-children="上架"
              :loading="switchLoading"
              un-checked-children="下架"
              @change="handleSwitchChange($event, record)"
            />
          </a-tooltip>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="onEdit(record)">编辑</a-button>

            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="永久删除，请谨慎操作"
              @cancel="onCancel"
              @confirm="onDelete(record.key)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <a-table
          :columns="childColumns"
          :data-source="record.childrenData"
          :loading="childLoading"
          :pagination="false"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="['alias', 'orderby'].includes(column.dataIndex)">
              <div class="editable-cell">
                <div
                  v-if="editableData[record.key]"
                  class="editable-cell-input-wrapper"
                >
                  <a-input
                    v-model:value="editableData[record.key][column.dataIndex]"
                    @change="
                      handleOrderbyChange(editableData[record.key], column)
                    "
                  />
                  <a style="margin-right: 5px" @click="save(record)">保存</a>
                  <a type="link" @click="cancel(record.key)">取消</a>
                </div>
                <div v-else class="editable-cell-text-wrapper">
                  {{ text }}
                  <edit-outlined class="editable-cell-icon" @click="edit" />
                </div>
              </div>
            </template>
            <template v-if="column.dataIndex === 'grades'">
              <a-tag v-for="tag in record.grades" :key="tag" color="orange">
                {{ tag }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'subjects'">
              <a-tag v-for="tag in record.subjects" :key="tag" color="blue">
                {{ tag }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'cpName'">
              <a-tag color="purple">
                {{ text }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'image'">
              <a-image
                :src="record.image1_url"
                style="margin-bottom: 5px; height: 40px"
              />
            </template>
          </template>
        </a-table>
      </template>
    </a-table>
    <CreateModal ref="createRef" :params="createModalData" />
    <EditModal ref="editRef" :params="editModalData" />

    <a-modal
      v-model:open="open"
      :destroyOnClose="true"
      :maskClosable="false"
      okText="保存修改"
      title="编辑封面"
      @cancel="handleAudioCancel"
      @ok="handleAudioOk"
    >
      <a-form ref="formRef" :model="params">
        <a-form-item label="封面">
          <Audio
            ref="AudioRef"
            @init="getImageAddress"
            @setImageAddress="handleParamsCover_url_id"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
  import { EditOutlined, PictureOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, onMounted, provide, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getCps } from '@/api/CP'
  import {
    deleteAlbums,
    editAlbums,
    getAlbums,
    getAlbumsDetail,
  } from '@/api/专辑'
  import { getSubjects } from '@/api/学科'
  import { getGrades } from '@/api/学级'
  import { getColumns } from '@/api/栏目'
  import VabSelectOption from '@/components/VabSelect/option/index.vue'
  import { useAutoRequest } from '@/hook/useAutoRequest'
  import { analyzeArray, getProperty, getSplitArray } from '@/utils'
  import { routerReplace } from '@/utils/routes'
  import Audio from '@/views/audio'

  import CreateModal from '../components/Create.vue'
  import EditModal from '../components/Edit.vue'

  const style = { width: '140px' }
  const route = useRoute()
  const formState = reactive({
    title: route.query?.title ?? '',
    grades: route.query?.grades ? parseInt(route.query?.grades) : undefined,
    subjects: route.query?.subjects
      ? parseInt(route.query?.subjects)
      : undefined,
    cp: route.query?.cp ? parseInt(route.query?.cp) : undefined,
    top_column_id: route.query?.top_column_id
      ? parseInt(route.query?.top_column_id)
      : undefined,
  })

  const { data, run, total, loading, current, pageSize } =
    usePagination(getAlbums)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        key: item.id,
        albums2: item.albums?.map((item) => `${item.id}-${item.title}`),
        gradesNames: getProperty(item.grades, 'name'),
        subjectsNames: getProperty(item.subjects, 'name'),
        grades: getSplitArray(item.grades),
        subjects: getSplitArray(item.subjects),
        cpName: item.cp?.name,
        cp: item.cp?.id,
        childrenData: [],
        enableTitle: item.enable == 0 ? '上架' : '下架',
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      sorter: true,
    },
    {
      title: '专辑名称',
      dataIndex: 'title',
    },
    {
      title: '所属学级',
      dataIndex: 'gradesNames',
    },
    {
      title: '所属学科',
      dataIndex: 'subjectsNames',
    },
    {
      title: '供应商',
      dataIndex: 'cpName',
    },
    {
      title: '所属栏目',
      dataIndex: 'top_columns',
    },
    {
      title: '封面',
      dataIndex: 'image',
    },
    {
      title: '上架/下架',
      dataIndex: 'enable',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleFormItemChange = () => {
    init(1)
  }

  const handleTableChange = (pag, filter, sorter) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init(
      pag?.current,
      pag?.pageSize,
      sorter.order?.replace('end', '') ?? 'desc'
    )
  }
  const init = (
    page = pagination.value.current,
    pageSize = pagination.value.pageSize,
    direction
  ) => {
    const { title, grades, subjects, cp, top_column_id } = formState
    run({
      page,
      pageSize,
      type: 2,
      title,
      grade_ids: [grades],
      subject_ids: [subjects],
      cp_ids: [cp],
      top_column_id,
      orders: [
        {
          column: 'id',
          direction: direction || 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      title,
      grades,
      subjects,
      cp,
      top_column_id,
    })
  }
  const switchLoading = ref(false)
  const handleSwitchChange = async (checked, record) => {
    switchLoading.value = true
    const params = {
      ...getBasicParams(record),
      enable: checked == true ? 1 : 0,
    }
    const { code } = await editAlbums(params, record.id)
    code == 200 &&
      (message.success('操作成功'), init(), (switchLoading.value = false))
  }
  init()

  // 新建专辑
  const createRef = ref()
  const createModalData = reactive({
    show: false,
    type: 2,
    init: () => {
      init()
    },
  })
  const onCreate = async () => {
    createModalData.show = true
  }

  // 修改专辑
  const editRef = ref()
  const editModalData = reactive({
    show: false,
    Content: {},
    init: () => {
      init()
      handleTableExpand(false)
    },
  })
  const onEdit = async (record) => {
    editModalData.Content = cloneDeep(record)
    editModalData.show = true
    const { data } = await getAlbumsDetail(record.id)
    editModalData.Content = Object.assign(data, editModalData.Content)
    console.log(editModalData.Content)
  }
  const onCancel = () => {
    message.info('取消')
  }
  // 删除
  const onDelete = async (key) => {
    const { code } = await deleteAlbums(key)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  // 修改封面
  const AudioRef = ref('')
  const open = ref(false)
  const editImage = (record) => {
    open.value = true
    editModalData.Content = cloneDeep(record)
  }

  const params = reactive({
    play_id: '', // 阿里云返回id 或者 未来学校填写的 id
    upload_type: undefined, // 上传类型  1 阿里 2未来学校
    albums: undefined,
    title: '', // 标签
    grades: undefined,
    subjects: undefined,
    cp_id: undefined, // 这里 如果上传类型是未来学校 那么 cp 就需要和 未来学校 同步
    desc: undefined,
    image1: undefined, // 图片
    image2: undefined,
  })
  const handleInit = async () => {
    const { title, grades, subjects, cp, albums, desc, image1, image2 } =
      editModalData.Content
    params.albums = albums
    params.title = title
    params.grades = analyzeArray(grades)
    params.subjects = analyzeArray(subjects)
    params.cp_id = cp.split('-')[0]
    params.desc = desc
    params.image1 = image1
    params.image2 = image2
    return params
  }
  const getImageAddress = () => {
    handleInit()
    AudioRef.value?.fileList.push({
      url: editModalData.Content.image1_url,
    })
  }
  const handleParamsCover_url_id = (value) => {
    params.image1 = value
  }
  const handleAudioOk = async () => {
    const { code } = await editAlbums(params, editModalData.Content.id)
    if (code == 200) {
      init()
      open.value = false
      message.success('修改成功')
    }
  }
  // 取消编辑封面
  const handleAudioCancel = () => {
    params.image1 = ''
  }

  // 可编辑表格
  const editableData = reactive({})
  const edit = () => {
    childDataSource.value.forEach((item) => {
      editableData[item.id] = cloneDeep(item)
    })
    console.log(editableData)
  }
  const handleOrderbyChange = (value, column) => {
    if (column.dataIndex != 'orderby') return
    let val = value['orderby'].replace(/[^\d]/g, '')
    val = parseInt(val)
    console.log(val)
    if (isNaN(val)) val = 0
    if (val > 9999) val = 9999
    value['orderby'] = val
    return val
  }
  const save = async () => {
    const flag = validateRecord(expandedRecord.value[0])
    if (flag == false) return
    let obj = {}
    const albums = {
      sync: childDataSource.value.map((item) => {
        if (editableData[item.key]) {
          obj = {
            id: item.id,
            alias: editableData[item.key]?.alias,
            orderby: parseInt(editableData[item.key]?.orderby),
          }
        } else {
          obj = {
            id: item.id,
            alias: '',
            orderby: item.orderby,
          }
        }
        console.log(obj)
        return obj
      }),
    }
    const params = {
      ...getBasicParams(expandedRecord.value[0]),
      albums,
    }
    const { code } = await editAlbums(params, expandedRecord.value[0].id)
    if (code == 200) {
      init()
      handleTableExpand(false)
      message.success('编辑成功')
    }
    for (const key in editableData) {
      delete editableData[key]
    }
  }
  const cancel = () => {
    for (const key in editableData) {
      delete editableData[key]
    }
  }

  const childColumns = [
    {
      title: 'id',
      dataIndex: 'id',
      width: '10%',
    },
    {
      title: '专辑名称',
      dataIndex: 'title',
      width: 200,
    },
    {
      title: '别名',
      dataIndex: 'alias',
      width: 120,
    },
    {
      title: '权重',
      dataIndex: 'orderby',
      width: 120,
    },
    {
      title: '所属学级',
      dataIndex: 'grades',
    },
    {
      title: '所属学科',
      dataIndex: 'subjects',
    },
    {
      title: '供应商',
      dataIndex: 'cpName',
    },
    {
      title: '封面',
      dataIndex: 'image',
    },
  ]
  // 展开子表格
  const expandedRecord = ref([]) // 展开行的数据
  const childDataSource = ref([]) // 展开行的子表格数组
  const expandedRowKeys = ref([]) // 展开行的id数组
  const handleTableExpand = (expanded, record) => {
    cancel()
    if (expanded) {
      expandedRecord.value[0] = cloneDeep(record)
      expandedRowKeys.value[0] = record.id
    } else {
      expandedRecord.value = []
      expandedRowKeys.value = []
    }

    requestData(expanded, record)
  }
  const [childLoading, requestData] = useAutoRequest(
    async (expanded, record) => {
      if (!expanded) return []
      const { data } = await getAlbumsDetail(record.id)
      return [data, record]
    },
    {
      loading: true,
      onSuccess: ([data, record]) => {
        if (!data) return
        dataSource.value.forEach((item) => {
          if (item.id === record.id) {
            item.childrenData = data.albums.map((item) => {
              return {
                ...item,
                key: item.id,
                grades: getProperty(item.grades, 'name'),
                subjects: getProperty(item.subjects, 'name'),
                cpName: item.cp?.name,
                cp: item.cp?.id,
              }
            })
            childDataSource.value = item.childrenData
          }
        })
      },
    }
  )

  const gradesOptions = ref([]) // 学级
  const subjectsOptions = ref([]) // 学科
  const cpOptions = ref([]) // 供应商
  const columnsOptions = ref([]) // 栏目
  const handleSearch = async () => {
    init(1)
  }
  const getInfos = async () => {
    const { data: grades } = await getGrades()
    gradesOptions.value = grades.map((item) => ({
      label: item.name,
      value: item.id,
    }))
    const { data: subjects } = await getSubjects()
    subjectsOptions.value = subjects.map((item) => ({
      label: item.name,
      value: item.id,
    }))
    const { data: cp } = await getCps()
    cpOptions.value = cp.map((item) => ({
      label: item.name,
      value: item.id,
    }))
    const { data: columns } = await getColumns()
    columnsOptions.value = columns.map((item) => ({
      label: item.name,
      value: item.id,
    }))
  }
  provide('gradesOptions', gradesOptions)
  provide('subjectsOptions', subjectsOptions)
  provide('cpOptions', cpOptions)

  onMounted(() => {
    getInfos()
  })

  const validateRecord = (record) => {
    let flag = true
    const { image1 } = record
    if (image1 == '') {
      message.info('封面不能为空,请先上传封面')
      flag = false
    }
    return flag
  }

  const getBasicParams = (record) => {
    return {
      title: record.title,
      type: record.type,
      grades: analyzeArray(record.grades),
      subjects: analyzeArray(record.subjects),
      cp_id: record.cp,
      desc: record.desc,
      image1: record.image1,
      image2: record.image2,
    }
  }
</script>

<style lang="scss" scoped>
  .editable-cell {
    position: relative;
    min-height: 30px;
    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
      padding-right: 14px;
    }

    .editable-cell-text-wrapper {
      padding: 5px 14px 5px 5px;
    }

    .editable-cell-icon,
    .editable-cell-icon-check {
      position: absolute;
      right: 0;
      width: 20px;
      cursor: pointer;
    }

    .editable-cell-icon {
      margin-top: 4px;
      display: none;
    }

    .editable-cell-icon-check {
      line-height: 28px;
    }

    .editable-cell-icon:hover,
    .editable-cell-icon-check:hover {
      color: #108ee9;
    }

    .editable-add-btn {
      margin-bottom: 8px;
    }
  }
  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }
</style>
