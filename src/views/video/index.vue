<template>
  <a-form ref="formRef" :model="formState">
    <a-form-item
      label="供应商"
      name="cp_id"
      :rules="[{ required: true, message: '供应商不能为空!' }]"
    >
      <a-select v-model:value="formState.cp_id" :options="cpOption" />
    </a-form-item>
    <template v-if="formState.cp_id == 2">
      <a-form-item
        label="视频标题"
        name="title"
        :rules="[{ required: true, message: '视频标题不能为空!' }]"
      >
        <a-input v-model:value="formState.title" />
      </a-form-item>
      <a-form-item
        label="视频id"
        name="play_id"
        :rules="[{ required: true, message: '视频id不能为空!' }]"
      >
        <a-input v-model:value="formState.play_id" />
      </a-form-item>
    </template>
    <template v-else>
      <a-form-item
        label="上传文件"
        name="file"
        :rules="[
          {
            required: true,
            validator: fileRequired,
            trigger: 'change',
          },
        ]"
      >
        <a-upload-dragger
          v-model:fileList="fileList"
          accept="video/*"
          :before-upload="beforeUpload"
          :multiple="true"
          @preview="filePreview"
          @remove="fileRemove"
        >
          <p>
            <inbox-outlined />
          </p>
          <p>点击或者文件拖拽到这里上传</p>
          <p>
            支持3GP、ASF、AVI、DAT、DV、FLV、F4V、GIF、M2T、M4V、MJ2、MJPEG、MKV、MOV、MP4、MPE、MPG、MPEG、MTS、OGG、QT、RM、RMVB、SWF、TS、VOB、WMV、WEBM
            等视频格式上传
          </p>
          <template #itemRender="{ file }">
            {{ console.log(file) }}
          </template>
        </a-upload-dragger>
      </a-form-item>
      <div v-for="(file, i) in fileList" :key="i">
        <a-space :size="30">
          <a-input v-model:value="file.name" style="width: 300px">
            {{ file.name }}
          </a-input>
          <span>{{ bytesToMB(file.originFileObj.size) }}MB</span>
          <span>{{ file.originFileObj.type }}</span>
          <a v-if="show" @click="fileRemove(file)">
            <DeleteOutlined />
          </a>
          <span>
            <template v-if="file.percent < 100">
              <a-progress
                :percent="file.percent"
                size="small"
                style="width: 200px"
              />
            </template>
            <template v-else>上传完成</template>
          </span>
        </a-space>
      </div>
    </template>
  </a-form>
</template>

<script setup>
  import { DeleteOutlined, InboxOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { reactive, ref, toRefs } from 'vue'

  import { getCps } from '@/api/CP/index'
  import { getVOD } from '@/api/user'
  import { createVideos } from '@/api/视频/index'
  import { bytesToMB } from '@/utils/index'

  const props = defineProps(['show'])
  const { show } = toRefs(props)
  const formRef = ref()
  const formState = reactive({
    cp_id: undefined,
    title: '',
    play_id: '',
  })
  const cpOption = ref([])

  // 文件上传
  const fileList = ref([])
  // 上传前
  const beforeUpload = () => {
    console.log(fileList)
    return false
  }

  const fileType = [
    'video/avi',
    'video/3gpp',
    'video/x-ms-asf',
    'video/x-msvideo',
    'video/x-dat',
    'video/x-dv',
    'video/x-flv',
    'video/x-f4v',
    'image/gif',
    'video/mp2t',
    'video/x-m4v',
    'video/mj2',
    'video/x-motion-jpeg',
    'video/x-matroska',
    'video/quicktime',
    'video/mp4',
    'video/mpeg',
    'video/mpeg',
    'video/mpeg',
    'video/MP2T',
    'video/ogg',
    'video/quicktime',
    'application/vnd.rn-realmedia',
    'application/vnd.rn-realmedia-vbr',
    'application/x-shockwave-flash',
    'video/MP2T',
    'video/dvd',
    'video/x-ms-wmv',
    'video/webm',
  ]
  let timer = null
  const fileRequired = async () => {
    if (fileList.value.length <= 0) {
      return Promise.reject('文件不能为空')
    }
    fileList.value = fileList.value.filter((item, i) => {
      if (!fileType.includes(item.type)) {
        clearTimeout(timer)
        timer = setTimeout(() => {
          message.error('存在不支持的格式或非音视频文件，已自动过滤')
        }, 100)
      } else {
        return item
      }
    })
  }

  // 删除
  const fileRemove = (file) => {
    const index = fileList.value.indexOf(file)
    const newFileList = fileList.value.slice()
    newFileList.splice(index, 1)
    fileList.value = newFileList
  }

  const previewVisible = ref(false)
  const previewImage = ref('')
  const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = (error) => reject(error)
    })
  }

  const filePreview = async (file) => {
    if (formState.cp_id == 2) return
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj)
    }

    previewImage.value = file.url || file.preview
    previewVisible.value = true
  }

  const loading = ref(false)

  class ParamData {
    constructor(data) {
      this.Vod = {
        Title: data.Title, // 音/视频标题
        CateId: data.CateId,
        TemplateGroupId: data.TemplateGroupId,
      }
    }
    getVodString() {
      return `{"Vod":${JSON.stringify(this.Vod)}}`
    }
  }

  const onSubmit = async () => {
    await formRef.value.validate()
    if (formState.cp_id == 2) {
      createVideo()
    } else {
      fileUpload()
    }
  }

  const createVideo = async () => {
    await createVideos({
      title: formState.title, // 标签
      play_id: formState.play_id, // 阿里云返回id 或者 未来学校填写的 id
      upload_type: 2, // 上传类型  1 阿里 2未来学校
      cp_id: 2, // 这里 如果上传类型是未来学校 那么 cp 就需要和 未来学校 同步
      cover_url_id: '', // 可以为空字符串, 这边是这样的 如果用户没上传封面 那么默认封面就是 视频提取的, 如果上传了封面 那么封面就是上传的
    })
  }
  const fileUpload = async () => {
    loading.value = true
    const promises = []
    for (let i = 0; i < fileList.value.length; i++) {
      promises.push(
        new Promise((resolve, reject) => {
          const paramData = new ParamData({
            Title: fileList.value[i].name,
            CateId: '**********',
            TemplateGroupId: 'bb64a9dc06485cf91450b325f07b3a66',
          })
          const j = i
          const uploader = new window.AliyunUpload.Vod({
            i: j,
            //userID，必填，您可以使用阿里云账号访问账号中心（https://account.console.aliyun.com/），即可查看账号ID
            userId: '****************', //'287994285953822931',
            //上传到视频点播的地域，默认值为'cn-shanghai'，
            //eu-central-1，ap-southeast-1
            region: 'cn-shanghai',
            //分片大小默认1 MB，不能小于100 KB（100*1024）
            partSize: 1048576,
            //并行上传分片个数，默认5
            parallel: 5,
            //网络原因失败时，重新上传次数，默认为3
            retryCount: 3,
            //网络原因失败时，重新上传间隔时间，默认为2秒
            retryDuration: 2,
            //开始上传
            async onUploadstarted(uploadInfo) {
              // 如果是STSToken上传方式, 需要调用uploader.setUploadAuthAndAddress方法
              // 设置上传身份和凭证
              // 以下请求实现为示例，用于演示设置凭证
              // 获取 accessKeyId, accessKeySecret,secretToken可能因AppServer实现有差异
              const { data } = await getVOD()
              uploader.setSTSToken(
                uploadInfo,
                // 'STS.NSkZsJEm2fxq1rkeKWpxhgt9x',
                // 'EXE73w68WCSgLcymmxnSPDrHAA99HymZ7vnTKQN3B6Eu',
                // 'CAISugJ1q6Ft5B2yfSjIr5DeEcn+qLIT0bqaM1TagUsCfPdEiLGSmjz2IHhJf3drBOwasf80mmFV6/cSlq5tTJNfQkjJNUiyATfDqFHPWZHInuDox55m4cTXNAr+Ihr/29CoEIedZdjBe/CrRknZnytou9XTfimjWFrXWv/gy+QQDLItUxK/cCBNCfpPOwJms7V6D3bKMuu3OROY6Qi5TmgQ41cs2DMuuf3vnpzEskqA02eXkLFF+97DRbG/dNRpMZtFVNO44fd7bKKp0lQLs0URqvwr0PAbomyZ74zAUglLjAuBP/DT9tB/x+W4qlCSmsSuxdqLdp0Q0oS7/+yfQAb4pODaJA+Wwgz0dUEgtDc10nodNNeddT2EmbOUFbqO+GAfQkkQV0gBWNEKRxE9d1pCA1+1JMfQ9YS+PG/LIxqAAUdTRPc5QCpjgz85I69Fs7CpFjFnkYVaSdfw/cqbHTi3rOxpBfLSpIoK4japRh80eHAxXae9OpMj4Xmy8Pbe1sfbF/LifC4s6raFcGAWB3YuankwULpbb+gbvykW1tBwOdn6nigMaariPbc6oV0xjL9QzvX7CtkKFX7aJW5gQ0vvIAA='
                data.Credentials.AccessKeyId,
                data.Credentials.AccessKeySecret,
                data.Credentials.SecurityToken
              )
            },
            //文件上传成功
            async onUploadSucceed(uploadInfo) {
              console.log(uploadInfo)
              // emits('update-video-id', uploadInfo.videoId)
              message.success('文件上传成功')
              await createVideos({
                title: uploadInfo.videoInfo.Title, // 标签
                play_id: uploadInfo.videoId, // 阿里云返回id 或者 未来学校填写的 id
                upload_type: 1, // 上传类型  1 阿里 2未来学校
                cp_id: formState.cp_id, // 这里 如果上传类型是未来学校 那么 cp 就需要和 未来学校 同步
                cover_url_id: '', // 可以为空字符串, 这边是这样的 如果用户没上传封面 那么默认封面就是 视频提取的, 如果上传了封面 那么封面就是上传的
              })
            },
            //文件上传失败
            onUploadFailed(uploadInfo, code, { message: msg }) {
              console.log('onUploadFailed=')
              console.log(uploadInfo)
              console.log(code)
              console.log(msg)
              message.error('文件上传失败')
              reject()
            },
            //文件上传进度，单位：字节
            onUploadProgress(uploadInfo, totalSize, loadedPercent) {
              console.log('onUploadProgress=')
              console.log(uploadInfo)
              console.log(totalSize)
              if (fileList.value[this.i].percent == undefined) {
                fileList.value[this.i].percent = 0
              } else {
                fileList.value[this.i].percent = Math.ceil(loadedPercent * 100)
              }
            },
            //上传凭证或STS token超时
            async onUploadTokenExpired() {
              // 如果是上传方式二即根据STSToken实现时，从新获取STS临时账号用于恢复上传
              // 上传文件过大时可能在上传过程中sts token就会失效, 所以需要在token过期的回调中调用resumeUploadWithSTSToken方法
              // 以下请求实现为示例，用于演示设置凭证
              // 获取 accessKeyId, accessKeySecret,secretToken 可能因AppServer实现有差异
              const { data } = await getVOD()
              uploader.resumeUploadWithSTSToken(
                data.Credentials.AccessKeyId,
                data.Credentials.AccessKeySecret,
                data.Credentials.SecurityToken
              )
            },
            //全部文件上传结束
            onUploadEnd(uploadInfo) {
              console.log(uploadInfo)
              resolve()
            },
          })
          uploader.cleanList()
          uploader.addFile(
            fileList.value[i].originFileObj,
            null,
            null,
            null,
            paramData.getVodString()
          )
          uploader.startUpload()
        })
      )
    }
    Promise.all(promises)
      .then((results) => {
        // 所有的Promise都成功解析后的处理逻辑
        loading.value = false
      })
      .catch((error) => {
        // 任何一个Promise被拒绝后的处理逻辑
        console.error(error)
        loading.value = false
      })

    // 将选中的文件添加到上传列表中
    // for (let i = 0; i < fileList.value.length; i++) {
    //   const paramData = new ParamData({
    //     Title: fileList.value[i].name,
    //     CateId: '**********',
    //   })
    //   const j = i
    //   const uploader = new window.AliyunUpload.Vod({
    //     i: j,
    //     //userID，必填，您可以使用阿里云账号访问账号中心（https://account.console.aliyun.com/），即可查看账号ID
    //     userId: '****************', //'287994285953822931',
    //     //上传到视频点播的地域，默认值为'cn-shanghai'，
    //     //eu-central-1，ap-southeast-1
    //     region: 'cn-shanghai',
    //     //分片大小默认1 MB，不能小于100 KB（100*1024）
    //     partSize: 1048576,
    //     //并行上传分片个数，默认5
    //     parallel: 5,
    //     //网络原因失败时，重新上传次数，默认为3
    //     retryCount: 3,
    //     //网络原因失败时，重新上传间隔时间，默认为2秒
    //     retryDuration: 2,
    //     //开始上传
    //     async onUploadstarted(uploadInfo) {
    //       // 如果是STSToken上传方式, 需要调用uploader.setUploadAuthAndAddress方法
    //       // 设置上传身份和凭证
    //       // 以下请求实现为示例，用于演示设置凭证
    //       // 获取 accessKeyId, accessKeySecret,secretToken可能因AppServer实现有差异
    //       const { data } = await getVOD()
    //       uploader.setSTSToken(
    //         uploadInfo,
    //         // 'STS.NSkZsJEm2fxq1rkeKWpxhgt9x',
    //         // 'EXE73w68WCSgLcymmxnSPDrHAA99HymZ7vnTKQN3B6Eu',
    //         // 'CAISugJ1q6Ft5B2yfSjIr5DeEcn+qLIT0bqaM1TagUsCfPdEiLGSmjz2IHhJf3drBOwasf80mmFV6/cSlq5tTJNfQkjJNUiyATfDqFHPWZHInuDox55m4cTXNAr+Ihr/29CoEIedZdjBe/CrRknZnytou9XTfimjWFrXWv/gy+QQDLItUxK/cCBNCfpPOwJms7V6D3bKMuu3OROY6Qi5TmgQ41cs2DMuuf3vnpzEskqA02eXkLFF+97DRbG/dNRpMZtFVNO44fd7bKKp0lQLs0URqvwr0PAbomyZ74zAUglLjAuBP/DT9tB/x+W4qlCSmsSuxdqLdp0Q0oS7/+yfQAb4pODaJA+Wwgz0dUEgtDc10nodNNeddT2EmbOUFbqO+GAfQkkQV0gBWNEKRxE9d1pCA1+1JMfQ9YS+PG/LIxqAAUdTRPc5QCpjgz85I69Fs7CpFjFnkYVaSdfw/cqbHTi3rOxpBfLSpIoK4japRh80eHAxXae9OpMj4Xmy8Pbe1sfbF/LifC4s6raFcGAWB3YuankwULpbb+gbvykW1tBwOdn6nigMaariPbc6oV0xjL9QzvX7CtkKFX7aJW5gQ0vvIAA='
    //         data.Credentials.AccessKeyId,
    //         data.Credentials.AccessKeySecret,
    //         data.Credentials.SecurityToken
    //       )
    //     },
    //     //文件上传成功
    //     async onUploadSucceed(uploadInfo) {
    //       console.log(uploadInfo)
    //       // emits('update-video-id', uploadInfo.videoId)
    //       message.success('文件上传成功')
    //       await createVideos({
    //         title: uploadInfo.videoInfo.Title, // 标签
    //         play_id: uploadInfo.videoId, // 阿里云返回id 或者 未来学校填写的 id
    //         upload_type: 1, // 上传类型  1 阿里 2未来学校
    //         cp_id: formState.cp_id, // 这里 如果上传类型是未来学校 那么 cp 就需要和 未来学校 同步
    //         cover_url_id: '', // 可以为空字符串, 这边是这样的 如果用户没上传封面 那么默认封面就是 视频提取的, 如果上传了封面 那么封面就是上传的
    //       })
    //     },
    //     //文件上传失败
    //     onUploadFailed(uploadInfo, code, { message: msg }) {
    //       console.log('onUploadFailed=')
    //       console.log(uploadInfo)
    //       console.log(code)
    //       console.log(msg)
    //       message.error('文件上传失败')
    //     },
    //     //文件上传进度，单位：字节
    //     onUploadProgress(uploadInfo, totalSize, loadedPercent) {
    //       console.log('onUploadProgress=')
    //       console.log(uploadInfo)
    //       console.log(totalSize)
    //       if (fileList.value[this.i].percent == undefined) {
    //         fileList.value[this.i].percent = 0
    //       } else {
    //         fileList.value[this.i].percent = Math.ceil(loadedPercent * 100)
    //       }
    //     },
    //     //上传凭证或STS token超时
    //     async onUploadTokenExpired() {
    //       // 如果是上传方式二即根据STSToken实现时，从新获取STS临时账号用于恢复上传
    //       // 上传文件过大时可能在上传过程中sts token就会失效, 所以需要在token过期的回调中调用resumeUploadWithSTSToken方法
    //       // 以下请求实现为示例，用于演示设置凭证
    //       // 获取 accessKeyId, accessKeySecret,secretToken 可能因AppServer实现有差异
    //       const { data } = await getVOD()
    //       uploader.resumeUploadWithSTSToken(
    //         data.Credentials.AccessKeyId,
    //         data.Credentials.AccessKeySecret,
    //         data.Credentials.SecurityToken
    //       )
    //     },
    //     //全部文件上传结束
    //     onUploadEnd(uploadInfo) {
    //       console.log(uploadInfo)
    //       loading.value = false
    //     },
    //   })
    //   uploader.cleanList()
    //   uploader.addFile(
    //     fileList.value[i].originFileObj,
    //     null,
    //     null,
    //     null,
    //     paramData.getVodString()
    //   )
    //   uploader.startUpload()
    // }
  }
  const validate = async () => {
    await formRef.value.validate()
  }
  const resetFields = async () => {
    fileList.value = []
    await formRef.value.resetFields()
  }

  const init = async () => {
    const { data } = await getCps()
    data.map((item) => {
      cpOption.value.push({
        value: item.id,
        label: item.name,
      })
    })
  }
  init()
  defineExpose({
    validate,
    resetFields,
    onSubmit,
    formState,
    fileList,
    loading,
  })
</script>

<script setup></script>

<style lang="scss" scoped>
  .default {
  }
</style>
