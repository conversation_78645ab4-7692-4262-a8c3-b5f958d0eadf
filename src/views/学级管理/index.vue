<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ShoppingOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="关联的学科">
            <a-select
              v-model:value="formState.related_subject_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">学级列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加学级</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :expandedRowKeys="expandedRowKeys"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
      @expand="handleTableExpand"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name'].includes(column.dataIndex)">
          <div>
            <a-input
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
            />
            <template v-else>
              <a-tag color="orange">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div>
            <span v-if="editableData[record.id]">
              <a-space :size="10">
                <a-button type="primary" @click="save(record.id)">
                  保存
                </a-button>
                <a-popconfirm title="确定取消?" @confirm="cancel(record.id)">
                  <a-button>取消</a-button>
                </a-popconfirm>
              </a-space>
            </span>
            <span v-else>
              <a-space :size="10">
                <a-button type="primary" @click="showModal(record.id)">
                  新增学科
                </a-button>
                <a-button type="primary" @click="edit(record.id)">
                  编辑
                </a-button>

                <a-popconfirm
                  cancel-text="否"
                  ok-text="是"
                  title="是否确定删除?"
                  @confirm="onDelete(record.id)"
                >
                  <a-button danger type="primary">删除</a-button>
                </a-popconfirm>
              </a-space>
            </span>
          </div>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <a-table
          :columns="childColumns"
          :data-source="record.childrenData"
          :loading="childLoading"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'name'">
              <a-tag color="blue">
                {{ text }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <a-space :size="10">
                <a-button type="primary" @click="onMoveUp(record)">
                  <UpOutlined />
                </a-button>
                <a-button type="primary" @click="onMoveDown(record)">
                  <DownOutlined />
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </template>
    </a-table>
    <CreateGradesModal ref="createGradesModalRef" @ok="handleOk" />
    <CreateSubjectsModal ref="createSubjectsModalRef" @ok="handleOk" />
  </div>
</template>

<script setup>
  import {
    DownOutlined,
    ShoppingOutlined,
    UpOutlined,
  } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, provide, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getSubjects } from '@/api/学科'
  import {
    deleteGrades,
    editGrades,
    getGrades,
    getGradesDetail,
  } from '@/api/学级'
  import { useAutoRequest } from '@/hook/useAutoRequest'
  import { routerReplace } from '~/src/utils/routes'

  import CreateSubjectsModal from './components/添加学科'
  import CreateGradesModal from './components/添加学级'

  const route = useRoute()
  // 搜索框label
  const label = '学级名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    related_subject_id: route.query?.related_subject_id
      ? parseInt(route.query?.related_subject_id)
      : undefined,
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getSubjects()
    options.value = data
  }
  provide('options', options)
  handleOptions()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getGrades)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        childrenData: [],
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      related_subject_id: formState.related_subject_id,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
      related_subject_id: formState.related_subject_id,
    })
  }

  const childColumns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '学科名称',
      dataIndex: 'name',
    },
    {
      title: '顺序操作',
      dataIndex: 'operation',
    },
  ]

  // 展开项
  const expandedRowKeys = ref([])

  const reRequestTableExpand = (id) => handleTableExpand(true, { id })

  const handleTableExpand = (expanded, record) => {
    if (expanded) {
      expandedRowKeys.value = [record.id]
      requestData(expanded, record)
    } else {
      expandedRowKeys.value = []
    }
  }
  const [childLoading, requestData] = useAutoRequest(
    async (expanded, record) => {
      const { data } = await getGradesDetail(record.id)
      return [data, record]
    },
    {
      loading: true,
      onSuccess: ([data, record]) => {
        if (!data) return
        dataSource.value.forEach((item) => {
          if (item.id === record.id) {
            item.childrenData = data.subjects.map((item) => {
              return {
                columnId: record.id,
                ...item,
              }
            })
          }
        })
      },
    }
  )
  // 编辑子表格
  const editChildrenTableData = reactive({})
  // 上移子表格
  const onMoveUp = async (record) => {
    editChildren(record)
    await saveChildren(record.id, 'Up')
  }
  // 下移子表格
  const onMoveDown = async (record) => {
    editChildren(record)
    await saveChildren(record.id, 'Down')
  }
  // 编辑子表格
  const editChildren = (record) => {
    editChildrenTableData[record.id] = cloneDeep(
      dataSource.value[
        dataSource.value.findIndex((item) => item.id == record.columnId)
      ].childrenData.filter((item) => record.id === item.id)[0]
    )
  }
  // 保存子表格编辑
  const saveChildren = async (id, txt) => {
    const findItem =
      dataSource.value[
        dataSource.value.findIndex(
          (item) => item.id == editChildrenTableData[id].columnId
        )
      ]
    // 要移动的值
    const value = findItem.childrenData.find((item) => item.id == id)
    const index = findItem.childrenData.findIndex((item) => item.id == id)
    console.log(value)
    console.log(findItem)
    // 开始排序
    if (index != -1) {
      findItem.childrenData.splice(index, 1)
      findItem.childrenData.splice(
        txt == 'Up' ? (index - 1 <= 0 ? 0 : index - 1) : index + 1,
        0,
        value
      )
    }

    const { code } = await editGrades(
      {
        name: findItem.name,
        subjects: {
          sync: findItem.childrenData.map((item, i) => {
            return {
              id: item.id,
              orderby: findItem.childrenData.length - 1 - i,
            }
          }),
        },
      },
      findItem.id
    )
    code == 200 &&
      (message.success('编辑成功'),
      reRequestTableExpand(editChildrenTableData[id].columnId))
    delete cancelChildren(id)
  }
  // 取消子表格编辑
  const cancelChildren = (id) => {
    delete editChildrenTableData[id]
  }

  // 编辑表格
  const editableData = reactive({})
  // 编辑表格
  const edit = (id) => {
    editableData[id] = cloneDeep(
      dataSource.value.filter((item) => id === item.id)[0]
    )
  }
  // 保存表格
  const save = async (id) => {
    console.log(id)
    const { code } = await editGrades(
      {
        name: editableData[id].name,
      },
      id
    )
    if (code == 200) {
      console.log(expandedRowKeys)
      init()
      expandedRowKeys.value = []
      message.success('修改成功')
      delete editableData[id]
    }
  }
  // 取消编辑
  const cancel = (id) => {
    delete editableData[id]
  }
  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteGrades(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  // 添加学级
  const createGradesModalRef = ref()
  const onClick = () => {
    createGradesModalRef.value?.showModal()
  }

  // 添加学科
  const createSubjectsModalRef = ref()
  const showModal = async (id) => {
    createSubjectsModalRef.value.setId(id)
    createSubjectsModalRef.value?.showModal()
  }
  const handleOk = () => {
    expandedRowKeys.value = []
    init()
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
