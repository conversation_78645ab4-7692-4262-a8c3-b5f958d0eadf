<template>
  <a-modal
    v-model:open="open"
    :title="`添加${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item :label="`关联${label}`" name="subjects">
        <a-select
          v-model:value="formState.subjects"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          mode="tags"
          :options="options"
          :placeholder="`请选择${label}`"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { inject, reactive, ref } from 'vue'

  import { editGrades, getGradesDetail } from '@/api/学级'
  import { uniqueArrays } from '@/utils'

  const label = '学科'
  const emits = defineEmits(['cancel', 'ok'])
  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    key: undefined,
    name: '',
    subjects: [],
    copySubjects: [],
  })
  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    await fetch()
    open.value = true
  }
  // 加载数据
  const fetch = async () => {
    const { data } = await getGradesDetail(id.value)
    formState.subjects = data.subjects.map((item) => item.id)
    formState.copySubjects = formState.subjects
    formState.name = data.name
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
      formState.subjects,
      formState.copySubjects
    )
    const { code } = await editGrades(
      {
        name: formState.name,
        subjects: {
          sync: uniqueToFirstArray.map((item, i) => {
            return {
              id: item,
              orderby: uniqueToFirstArray.length - 1 - i,
            }
          }),
          del: uniqueToSecondArray.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }
  const options = inject('options')

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
