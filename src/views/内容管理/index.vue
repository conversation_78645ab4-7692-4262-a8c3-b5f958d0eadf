<template>
  <div>
    <a-row>
      <a-col :span="6">
        <a-flex align="center" class="py-10" gap="10">
          <div><DefaultDivider /></div>
          <div class="font-bold">专辑列表</div>
        </a-flex>
        <a-form :model="form">
          <a-flex gap="10">
            <a-form-item label="专辑名称">
              <a-input
                v-model:value="form.title"
                allow-clear
                enter-button
                placeholder="输入专辑名称快速查询"
                style="width: 100%"
                @change="handleTitleSearch"
              />
            </a-form-item>
          </a-flex>
        </a-form>
        <a-tabs
          v-model:activeKey="activeKey"
          :destroyInactiveTabPane="true"
          :style="{ maxHeight: '800px' }"
          tab-position="left"
          @change="handleAlbumClick"
        >
          <a-tab-pane
            v-for="(item, i) in dataList"
            :key="i"
            :tab="`${item.id}-${item.title}`"
          />
        </a-tabs>
      </a-col>
      <a-col class="table" :offset="1" :span="17">
        <a-flex align="center" class="py-10" gap="10">
          <DefaultDivider />
          <div class="font-bold">视频列表</div>
        </a-flex>
        <a-flex class="py-10" gap="10">
          <a-button type="primary" @click="getRelatedVideos">绑定视频</a-button>
          <a-button type="primary" @click="getUnRelatedVideos">
            解绑视频
          </a-button>
        </a-flex>
        <a-table
          bordered
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          :row-selection="rowSelection"
          tableLayout="fixed"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.dataIndex === 'image'">
              <a-image
                :src="record.cover_url"
                style="margin-bottom: 5px; height: 60px"
              />
            </template>
            <template
              v-else-if="['alias', 'orderby'].includes(column.dataIndex)"
            >
              <div class="editable-cell">
                <div
                  v-if="editableData[record.key]"
                  class="editable-cell-input-wrapper"
                >
                  <a-input
                    v-model:value="editableData[record.key][column.dataIndex]"
                    @change="
                      handleOrderbyChange(editableData[record.key], column)
                    "
                  />
                  <a style="margin-right: 5px" @click="save(record)">保存</a>
                  <a type="link" @click="cancel(record.key)">取消</a>
                </div>
                <div v-else class="editable-cell-text-wrapper">
                  {{ text }}
                  <edit-outlined
                    class="editable-cell-icon"
                    @click="edit(record)"
                  />
                </div>
              </div>
            </template>
            <template v-else-if="['cpName'].includes(column.dataIndex)">
              <a-tag color="purple">
                {{ text }}
              </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'operation'">
              <div class="editable-row-operations">
                <span v-if="editableData[record.key]">
                  <a-popconfirm title="确定修改?" @confirm="save(record)">
                    <a style="margin-right: 5px">保存</a>
                  </a-popconfirm>
                  <a-button type="link" @click="cancel(record.key)">
                    取消
                  </a-button>
                </span>
                <span v-else>
                  <a @click="edit(record)">编辑</a>
                </span>
              </div>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
    <a-modal
      :open="show"
      style="width: 1200px; top: 0px"
      title="绑定视频"
      @cancel="onClose"
      @ok="onSubmit"
    >
      <a-form ref="formRef" :label-col="{ span: 2 }" :model="form">
        <a-form-item label="视频名称" name="videoName">
          <a-input
            v-model:value="form.videoName"
            :filter-option="false"
            label-in-value
            mode="multiple"
            :not-found-content="'没有搜索到...'"
            placeholder="输入名称搜索"
            @change="handleNameChange"
          />
        </a-form-item>
      </a-form>
      <a-table
        bordered
        :columns="relatedState.columns"
        :data-source="relatedState.dataSource"
        :loading="relatedState.loading"
        :pagination="relatedState.pagination"
        :row-selection="{
          selectedRowKeys: relatedState.selectedRowKeys,
          onChange: relatedState.onSelectChange,
        }"
        tableLayout="fixed"
        @change="relatedState.handleTableChange"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'image'">
            <a-image
              :src="record.cover_url"
              style="margin-bottom: 5px; height: 30px"
            />
          </template>
          <template v-else-if="['cpName'].includes(column.dataIndex)">
            <a-tag color="purple">
              {{ text }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { message, Table } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, onMounted, reactive, ref, unref } from 'vue'
  import { useRoute } from 'vue-router'

  import { editAlbums, getAlbums } from '@/api/专辑'
  import { getVideos } from '@/api/视频'
  import { modal } from '@/utils/modal'
  import { routerReplaceNoPagi } from '@/utils/routes'

  const route = useRoute()
  const activeKey = ref(parseInt(route.query?.activeKey) || 0)
  const dataList = ref([])
  const dataSource = ref([])
  const loading = ref(false)
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      width: '10%',
    },
    {
      title: '视频名称',
      dataIndex: 'title',
    },
    {
      title: '别名',
      dataIndex: 'alias',
    },
    {
      title: '权重',
      dataIndex: 'orderby',
    },
    {
      title: '供应商',
      dataIndex: 'cpName',
    },
    {
      title: '封面',
      dataIndex: 'image',
    },
    // {
    //   title: '修改',
    //   dataIndex: 'operation',
    // },
  ]
  const totals = ref(0)
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current) || 1,
      pageSize: parseInt(route.query?.pageSize) || 10,
      total: totals.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })
  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    getVideoss()
  }
  const handleAlbumClick = () => {
    selectedRowKeys.value = []
    getVideoss(1)
  }

  const handleTitleSearch = () => {
    activeKey.value = 0
    getAlbumss()
  }
  const getAlbumss = async () => {
    const { id, type } = route.query
    const { title } = form
    let params = {}
    if (type == 1) {
      params = {
        id,
        type: 1,
      }
    } else if (type == 2) {
      params = {
        related_album_id: id,
      }
    } else {
      params = {
        title,
        type: 1,
      }
    }
    const { data } = await getAlbums(params)
    console.log('getAlbums', data)
    dataList.value = data.map((item) => {
      return {
        ...item,
      }
    })
    if (dataList.value[activeKey.value]) getVideoss(1)
  }
  const getVideoss = async (
    page = pagination.value.current,
    pageSize = pagination.value.pageSize
  ) => {
    loading.value = true
    const {
      data,
      meta: {
        pagination: { total },
      },
    } = await getVideos({
      page,
      pageSize,
      related_album_id: dataList.value[activeKey.value].id,
    })
    console.log('getVideos', data)
    dataSource.value = data.map((item) => {
      return {
        ...item,
        key: item.id,
        cpName: item.cp?.name,
        cp: item.cp?.id,
      }
    })
    dataList.value[activeKey.value]['videos'] = dataSource.value
    loading.value = false
    totals.value = total
    pagination.value.current = page
    pagination.value.pageSize = pageSize
    routerReplaceNoPagi({
      activeKey: activeKey.value,
      title: form.title,
      current: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })
  }
  onMounted(() => {
    getAlbumss()
  })

  const getParams = () => {
    const d = dataList.value[activeKey.value]
    const { title, grades, subjects, cp_id, desc, image1, image2 } = d
    const params = {
      title,
      grades: grades.map((item) => ({ id: item.id })),
      subjects: subjects.map((item) => ({ id: item.id })),
      cp_id,
      desc,
      image1,
      image2,
    }
    return params
  }

  // 可编辑表格
  const editableData = reactive({})
  const edit = () => {
    dataSource.value.forEach((item) => {
      editableData[item.key] = cloneDeep(item)
    })
  }
  const handleOrderbyChange = (value, column) => {
    if (column.dataIndex != 'orderby') return
    let val = value['orderby'].replace(/[^\d]/g, '')
    val = parseInt(val)
    console.log(val)
    if (isNaN(val)) val = 0
    if (val > 9999) val = 9999
    value['orderby'] = val
    return val
  }
  const save = async () => {
    let obj = {}
    const videos = {
      sync: dataList.value[activeKey.value]['videos'].map((item) => {
        if (editableData[item.key]) {
          obj = {
            id: item.id,
            alias: editableData[item.key]?.alias,
            orderby: parseInt(editableData[item.key]?.orderby),
          }
        } else {
          obj = {
            id: item.id,
            alias: '',
            orderby: item.orderby,
          }
        }
        console.log(obj)
        return obj
      }),
    }
    const params = {
      ...getParams(),
      videos,
      albums: {},
    }
    console.log(params)
    const { code } = await editAlbums(
      params,
      dataList.value[activeKey.value].id
    )
    console.log(code)
    if (code == 200) {
      getVideoss(1)
      message.success('编辑成功')
    }
    for (const key in editableData) {
      delete editableData[key]
    }
  }
  const cancel = () => {
    for (const key in editableData) {
      delete editableData[key]
    }
  }

  const selectedRowKeys = ref([])
  const onSelectChange = (changableRowKeys) => {
    console.log('selectedRowKeys changed: ', changableRowKeys)
    selectedRowKeys.value = changableRowKeys
  }
  const rowSelection = computed(() => {
    return {
      selectedRowKeys: unref(selectedRowKeys),
      onChange: onSelectChange,
      hideDefaultSelections: true,
      selections: [
        Table.SELECTION_ALL,
        Table.SELECTION_INVERT,
        Table.SELECTION_NONE,
      ],
    }
  })

  // 弹窗
  const formRef = ref()
  const show = ref(false)
  const form = reactive({
    title: route.query?.title ?? '',
    videoName: '',
  })
  const resetForm = () => {
    selectedRowKeys.value = []
    relatedState.selectedRowKeys = []
    pagination.value.current = 1
    relatedState.pagination.current = 1
    getVideoss()
  }
  const handleNameChange = () => {
    relatedState.pagination.current = 1
    relatedState.pagination.pageSize = 10
    relatedState.selectedRowKeys = []
    handleSearch()
  }
  const handleSearch = async () => {
    if (!dataList.value[activeKey.value]) return
    relatedState.loading = true
    const {
      data,
      meta: {
        pagination: { total },
      },
    } = await getVideos({
      title: form.videoName,
      page: relatedState.pagination.current,
      pageSize: relatedState.pagination.pageSize,
      unrelated_album_id: dataList.value[activeKey.value].id,
    })
    relatedState.dataSource = data.map((item) => {
      return {
        ...item,
        key: item.id,
        cpName: item.cp?.name,
        cp: item.cp?.id,
      }
    })
    relatedState.loading = false
    relatedState.pagination.total = total
  }
  const getRelatedVideos = async () => {
    show.value = true
    handleSearch()
  }
  const getUnRelatedVideos = async () => {
    if (selectedRowKeys.value.length <= 0)
      return message.info('请选择要解绑的视频')
    const videoList = selectedRowKeys.value.map((item) => {
      return {
        id: item,
      }
    })
    const videos = {}
    if (videoList.length > 0) {
      videos['del'] = videoList
    }
    const params = {
      ...getParams(),
      videos,
      albums: {},
    }
    console.log(params)
    modal(async () => {
      try {
        const { code } = await editAlbums(
          params,
          dataList.value[activeKey.value].id
        )
        if (code == 200) {
          message.success('解绑成功')
          resetForm()
        }
      } catch (error) {
        console.log(error)
      }
    })
  }
  const onClose = () => {
    show.value = false
    formRef.value.resetFields()
    relatedState.selectedRowKeys = []
  }
  const onSubmit = async () => {
    await formRef.value.validate()
    const videoList = relatedState.selectedRowKeys.map((item) => {
      return {
        id: item,
        alias: '',
        orderby: 0,
      }
    })
    const videos = {}
    if (videoList.length > 0) {
      videos['sync'] = videoList
    }
    const params = {
      ...getParams(),
      videos,
      albums: {},
    }
    console.log(params)
    modal(async () => {
      try {
        const { code } = await editAlbums(
          params,
          dataList.value[activeKey.value].id
        )
        if (code == 200) {
          message.success('绑定成功')
          resetForm()
          handleSearch()
        }
      } catch (error) {
        console.log(error)
      }
    })
  }

  const relatedState = reactive({
    columns: [
      {
        title: 'id',
        dataIndex: 'id',
        width: '10%',
      },
      {
        title: '视频名称',
        dataIndex: 'title',
      },
      {
        title: '供应商',
        dataIndex: 'cpName',
      },
      {
        title: '封面',
        dataIndex: 'image',
      },
    ],
    dataSource: [],
    loading: false,
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: (total) => {
        return `总共${total}条`
      },
    },
    selectedRowKeys: [],
    onSelectChange: (selectedRowKeys) => {
      console.log('selectedRowKeys changed: ', selectedRowKeys)
      relatedState.selectedRowKeys = selectedRowKeys
    },
    handleTableChange: (pag) => {
      relatedState.pagination.current = pag?.current
      relatedState.pagination.pageSize = pag?.pageSize
      handleSearch()
    },
  })
</script>

<style scoped lang="scss">
  .editable-cell {
    position: relative;
    min-height: 30px;
    .editable-cell-input-wrapper,
    .editable-cell-text-wrapper {
      padding-right: 24px;
    }

    .editable-cell-text-wrapper {
      padding: 5px 24px 5px 5px;
    }

    .editable-cell-icon,
    .editable-cell-icon-check {
      position: absolute;
      right: 0;
      width: 20px;
      cursor: pointer;
    }

    .editable-cell-icon {
      margin-top: 4px;
      display: none;
    }

    .editable-cell-icon-check {
      line-height: 28px;
    }

    .editable-cell-icon:hover,
    .editable-cell-icon-check:hover {
      color: #108ee9;
    }

    .editable-add-btn {
      margin-bottom: 8px;
    }
  }
  .editable-cell:hover .editable-cell-icon {
    display: inline-block;
  }
</style>
