<template>
  <template v-if="type === 'text1'">
    <div class="item-txt">
      <div class="font-size14 font-bold text1">
        <a-tooltip>
          <template #title>{{ item.text1 }}</template>
          {{ item.text1 }}
        </a-tooltip>
      </div>
      <div class="grey font-size12 text2">
        <a-tooltip>
          <template #title>{{ item.text2 }}</template>
          {{ item.text2 }}
        </a-tooltip>
      </div>
    </div>
  </template>
  <template v-else-if="type === 'text2'">
    <div class="font-bold text3">
      <a-tooltip>
        <template #title>{{ item.text1 }}</template>
        {{ item.text1 }}
      </a-tooltip>
    </div>
  </template>
  <template v-else-if="type === 'text3'">
    <div class="font-size14 font-bold text1">
      <a-tooltip>
        <template #title>{{ item.text1 }}</template>
        {{ item.text1 }}
      </a-tooltip>
    </div>
    <div class="grey font-size12 text2">
      <a-tooltip>
        <template #title>{{ item.text2 }}</template>
        {{ item.text2 }}
      </a-tooltip>
    </div>
  </template>
</template>

<script setup>
  import { toRefs } from 'vue'
  const props = defineProps(['type', 'item'])

  const { type, item } = toRefs(props)
</script>

<style lang="scss" scoped>
  .text1,
  .text2,
  .text3 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .text3 {
    padding: 3px 10px 0px 10px;
  }
</style>
