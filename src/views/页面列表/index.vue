<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><UnorderedListOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">页面列表</div>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-button type="primary" @click="onEdit(record)">查看编辑</a-button>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { UnorderedListOutlined } from '@ant-design/icons-vue'
  import { computed, reactive } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getPages } from '@/api/页面'
  import { pushWithQuery, routerReplace } from '~/src/utils/routes'

  const route = useRoute()
  console.log(route)
  // 搜索框label
  const label = '页面名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
  })
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getPages)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '页面名称',
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
    })
  }

  const onEdit = (record) => {
    switch (record.name) {
      case '品牌包K12（小学）一级页面':
        pushWithQuery('K12PrimarySchool', {
          id: record.id,
        })
        break
      case '品牌包未来学校（初中）一级页面':
        pushWithQuery('FutureSchoolJuniorHighSchool', {
          id: record.id,
        })
        break
      case '品牌包新东方（高中）一级页面':
        pushWithQuery('NewOrientalHighSchool', {
          id: record.id,
        })
        break
      case '融合包高中一级页面':
        pushWithQuery('HighSchoolFirstLevel', {
          id: record.id,
        })
        break
      case '融合包小学一级页面':
        pushWithQuery('PrimarySchoolFirstLevel', {
          id: record.id,
        })
        break
      case '融合包初中一级页面':
        pushWithQuery('JuniorHighSchoolFirstLevel', {
          id: record.id,
        })
        break
      case '品牌包新东方（初中）一级页面':
        pushWithQuery('NewOrientalJuniorHighSchool', {
          id: record.id,
        })
        break
      case '品牌包K12（初中）一级页面':
        pushWithQuery('K12JuniorHighSchool', {
          id: record.id,
        })
        break
      case '品牌包K12（高中）一级页面':
        pushWithQuery('K12HighSchool', {
          id: record.id,
        })
        break
      case '品牌包未来学校（高中）一级页面':
        pushWithQuery('FutureSchoolHighSchool', {
          id: record.id,
        })
        break
      case '品牌包未来学校（小学）一级页面':
        pushWithQuery('FutureSchoolPrimarySchool', {
          id: record.id,
        })
        break
      case '品牌包新东方（小学）一级页面':
        pushWithQuery('NewOrientalPrimarySchool', {
          id: record.id,
        })
        break
      case '名师讲堂':
        pushWithQuery('DivisionAdmin', {
          id: record.id,
        })
        break
      case '题库组卷':
        pushWithQuery('QuestionBankAdmin', {
          id: record.id,
        })
        break
      default:
        break
    }
    console.log(record)
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
