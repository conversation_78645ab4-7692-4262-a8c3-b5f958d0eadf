<template>
  <div class="container">
    <a-flex align="center" class="title">
      <EditOutlined class="pr-10" />
      <span class="font-size16">未来学校小学教育</span>
    </a-flex>
    <div class="container_bottom">
      <div class="box">
        <div class="m-10">
          <a-carousel>
            <div
              v-for="(item, i) in recommendedPosition.G8"
              :key="i"
              class="cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
          </a-carousel>
          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G9"
              :key="i"
              class="area1_title cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G1"
                :key="i"
                class="cursor"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G10"
              :key="i"
              class="area1_title cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <a-flex
                align="center"
                gap="10"
                justify="space-between"
                wrap="wrap"
              >
                <div
                  v-for="(item, i) in recommendedPosition.G11"
                  :key="i"
                  class="cursor"
                  @click="item.onClick(item)"
                >
                  <img :src="item.image1_url" />
                </div>
                <div
                  v-for="(item, i) in recommendedPosition.G2"
                  :key="i"
                  class="cursor background-color-white"
                  @click="item.onClick(item)"
                >
                  <a-flex>
                    <img :src="item.image1_url" style="width: 60%" />
                    <use-item :item="item" :type="'text1'" />
                  </a-flex>
                </div>
              </a-flex>

              <a-flex
                align="center"
                gap="10"
                justify="space-between"
                wrap="wrap"
              >
                <div
                  v-for="(item, i) in recommendedPosition.G12"
                  :key="i"
                  class="cursor"
                  @click="item.onClick(item)"
                >
                  <img :src="item.image1_url" />
                </div>
                <div
                  v-for="(item, i) in recommendedPosition.G3"
                  :key="i"
                  class="cursor background-color-white"
                  @click="item.onClick(item)"
                >
                  <a-flex>
                    <img :src="item.image1_url" style="width: 60%" />
                    <use-item :item="item" :type="'text1'" />
                  </a-flex>
                </div>
              </a-flex>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G13"
              :key="i"
              class="cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G4"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G14"
              :key="i"
              class="cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G5"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G15"
              :key="i"
              class="cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G6"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G16"
              :key="i"
              class="cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G7"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
            </a-flex>
          </div>
        </div>
      </div>
    </div>
    <useModal :id="id" ref="ModalRef" @init="fetchData" />
    <Release :id="page_id" />
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { clearArrays } from '@/utils/index'
  import useModal from '@/views/广告位弹窗'
  import Release from '@/views/页面发布'
  import { getAdverts } from '~/src/api/广告位'

  const route = useRoute()

  const ModalRef = ref('')
  const id = ref('')
  const onClick = (item) => {
    id.value = item.id
    ModalRef.value?.showModal()
  }

  const page_id = route.query?.id
  const recommendedPosition = reactive({})
  const fetchData = async () => {
    const { data } = await getAdverts({
      page_id,
    })
    clearArrays(recommendedPosition)

    data.map((item) => {
      if (!recommendedPosition[item.group_label]) {
        recommendedPosition[item.group_label] = []
      }
      recommendedPosition[item.group_label].push(
        Object.assign(item, {
          onClick: (x) => onClick(x),
        })
      )
    })
  }
  fetchData()
</script>

<style lang="scss" scoped>
  .container {
    img {
      width: 100%;
      height: auto;
    }
    // background: linear-gradient(90deg, #60b2fb, #6485f6);
    // color: #fff !important;
    .title {
      padding: 15px;
      // background: linear-gradient(90deg, #60b2fb, #6485f6);
      // color: #fff !important;
      border-bottom: 1px solid #ebeef5;
      // border_bottom: 1px solid #000000;
    }
    .container_bottom {
      margin: 15px;
      .cursor {
        border: 1px dashed #9e9e9e;
      }
      .box {
        margin: 0 auto;
        border: 2px dashed #9e9e9e;
        width: 1000px;
        background-color: #f5f5f5;
        color: black;
        .bg {
          width: 100%;
          border: 1px dashed #9e9e9e;
        }

        .area1_title {
          width: 200px;
          margin: 0 auto;
        }
      }
    }
  }
</style>
