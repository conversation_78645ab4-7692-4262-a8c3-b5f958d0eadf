<template>
  <div class="container">
    <a-flex align="center" class="title">
      <EditOutlined class="pr-10" />
      <span class="font-size14">小学一级</span>
    </a-flex>
    <div class="container_bottom">
      <div class="box">
        <div class="m-10">
          <div class="bg">
            <a-carousel>
              <div
                v-for="(item, i) in recommendedPosition.G1"
                :key="i"
                class="cursor"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-carousel>
          </div>
          <a-flex>
            <div>
              <div class="mt-10">
                <a-flex align="center" gap="10" justify="space-between">
                  <div
                    v-for="(item, i) in recommendedPosition.G2?.slice(0, 1)"
                    :key="i"
                    class="cursor area2_content"
                    @click="item.onClick(item)"
                  >
                    <img :src="item.image1_url" />
                    <div class="item-txt area2_item_1">
                      <div class="font-size14 font-bold text1">
                        {{ item.text1 }}
                      </div>
                      <div>{{ item.text2 }}</div>
                    </div>
                  </div>
                  <a-flex
                    align="center"
                    gap="10"
                    justify="space-between"
                    wrap="wrap"
                  >
                    <div
                      v-for="(item, i) in recommendedPosition.G2?.slice(1, 9)"
                      :key="i"
                      class="cursor area2_content2"
                      @click="item.onClick(item)"
                    >
                      <img :src="item.image1_url" />
                      <use-item :item="item" :type="'text3'" />
                    </div>
                  </a-flex>
                </a-flex>

                <a-flex
                  align="center"
                  class="mt-10"
                  gap="10"
                  justify="space-between"
                >
                  <div
                    v-for="(item, i) in recommendedPosition.G2?.slice(9)"
                    :key="i"
                    class="cursor area2_content3"
                    @click="item.onClick(item)"
                  >
                    <img :src="item.image1_url" />
                    <use-item :item="item" :type="'text3'" />
                  </div>
                </a-flex>
              </div>

              <div class="mt-10">
                <div
                  v-for="(item, i) in recommendedPosition.G5"
                  :key="i"
                  class="area1_text cursor"
                  @click="item.onClick(item)"
                >
                  {{ item.text1 }}
                </div>

                <a-tabs
                  v-model:activeKey="activeKey"
                  destroyInactiveTabPane
                  @dblclick="onDblClick"
                >
                  <a-tab-pane
                    v-for="item in [
                      recommendedPosition.G8?.find((x) => x.orderby == 1),
                      recommendedPosition.G9?.find((x) => x.orderby == 1),
                      recommendedPosition.G10?.find((x) => x.orderby == 1),
                      recommendedPosition.G11?.find((x) => x.orderby == 1),
                      recommendedPosition.G12?.find((x) => x.orderby == 1),
                      recommendedPosition.G13?.find((x) => x.orderby == 1),
                    ]"
                    :key="item?.id"
                    :tab="item?.text1 || item?.group_label"
                    @click="item.onClick(item)"
                  >
                    <template v-if="item?.group_label == 'G8'">
                      <a-flex align="center" gap="10" justify="space-between">
                        <div
                          v-for="(child, i) in recommendedPosition.G3"
                          :key="i"
                          class="cursor area3_content"
                          @click="child.onClick(child)"
                        >
                          <img :src="child.image1_url" />
                          <use-item :item="child" :type="'text3'" />
                        </div>
                      </a-flex>
                    </template>
                    <template v-else>
                      <a-flex align="center" gap="10" justify="space-between">
                        <div
                          v-for="(child, i) in recommendedPosition[
                            item?.group_label
                          ]?.filter((x) => x.orderby != 1)"
                          :key="i"
                          class="cursor area3_content"
                          @click="child.onClick(child)"
                        >
                          <img :src="child.image1_url" />
                          <use-item :item="child" :type="'text3'" />
                        </div>
                      </a-flex>
                    </template>
                  </a-tab-pane>
                </a-tabs>
              </div>

              <div class="mt-10">
                <div
                  v-for="(item, i) in recommendedPosition.G6"
                  :key="i"
                  class="area1_text cursor"
                  @click="item.onClick(item)"
                >
                  {{ item.text1 }}
                </div>

                <a-flex align="center" gap="10" justify="space-between">
                  <div
                    v-for="(item, i) in recommendedPosition.G4?.slice(0, 1)"
                    :key="i"
                    class="cursor area2_content"
                    @click="item.onClick(item)"
                  >
                    <img :src="item.image1_url" />
                    <div class="item-txt area2_item_1">
                      <div class="font-size14 font-bold text1">
                        {{ item.text1 }}
                      </div>
                      <div class="font-size12">{{ item.text2 }}</div>
                    </div>
                  </div>
                  <a-flex
                    align="center"
                    gap="10"
                    justify="space-between"
                    wrap="wrap"
                  >
                    <div
                      v-for="(item, i) in recommendedPosition.G4?.slice(1, 9)"
                      :key="i"
                      class="cursor area2_content2"
                      @click="item.onClick(item)"
                    >
                      <img :src="item.image1_url" />
                      <use-item :item="item" :type="'text3'" />
                    </div>
                  </a-flex>
                </a-flex>
                <a-flex
                  align="center"
                  class="mt-10"
                  gap="10"
                  justify="space-between"
                >
                  <div
                    v-for="(item, i) in recommendedPosition.G4?.slice(9)"
                    :key="i"
                    class="cursor area2_content3"
                    @click="item.onClick(item)"
                  >
                    <img :src="item.image1_url" />
                    <use-item :item="item" :type="'text3'" />
                  </div>
                </a-flex>
              </div>
            </div>

            <div class="mt-10">
              <div
                v-for="(item, i) in recommendedPosition.G7"
                :key="i"
                class="cursor ml-10"
                style="width: 118px"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </div>
          </a-flex>
        </div>
      </div>
    </div>
    <useModal :id="id" ref="ModalRef" @init="fetchData" />
    <Release :id="page_id" />
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { clearArrays } from '@/utils/index'
  // import item_txt from '@/views/item_txt'
  import useModal from '@/views/广告位弹窗'
  import Release from '@/views/页面发布'
  import { getAdverts } from '~/src/api/广告位'

  const route = useRoute()

  const ModalRef = ref('')
  const id = ref('')
  const onClick = (item) => {
    id.value = item.id
    ModalRef.value?.showModal()
  }
  const onDblClick = () => {
    id.value = activeKey.value
    ModalRef.value?.showModal()
  }

  const page_id = route.query?.id
  const recommendedPosition = reactive({})
  const fetchData = async () => {
    const { data } = await getAdverts({
      page_id,
    })
    clearArrays(recommendedPosition)

    data.map((item) => {
      if (!recommendedPosition[item.group_label]) {
        recommendedPosition[item.group_label] = []
      }
      recommendedPosition[item.group_label].push(
        Object.assign(item, {
          onClick: (x) => onClick(x),
        })
      )
    })
  }

  const activeKey = ref(869)

  fetchData()
</script>

<style lang="scss" scoped>
  .container {
    img {
      width: 100%;
      height: auto;
    }
    .title {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
    }
    .container_bottom {
      margin: 15px;
      .cursor {
        border: 1px dashed #9e9e9e;
      }
      .box {
        margin: 0 auto;
        border: 2px dashed #9e9e9e;
        width: 1000px;
        background-color: #f5f5f5;
        color: black;
        .bg {
          width: 100%;
        }
        .area1_text {
          width: 200px;
          height: 30px;
          line-height: 30px;
          padding-left: 10px;
        }
        .area2_item_1 {
          padding: 10px;
          background: #3791c3;
          color: #fff;
          border-radius: 0 0 12px 12px;
        }
        .area2_content {
          width: 570px;
        }
        .area2_content2 {
          width: 115px;
        }
        .area2_content3 {
          width: 130px;
        }
        .area3_content {
          width: 132px;
        }
      }
    }
  }
</style>
