<template>
  <div class="container">
    <a-flex align="center" class="title">
      <EditOutlined class="pr-10" />
      <span class="font-size14">小学一级</span>
    </a-flex>
    <div class="container_bottom">
      <div class="box">
        <div class="m-10">
          <div class="bg">
            <img src="@/assets/未来学校/高中/bg.png" />
          </div>
          <div class="mt-10">
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in area1"
                :key="i"
                class="cursor area1_content"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <div class="item-txt">
                  <div class="font-size14 font-bold text1">
                    {{ item.text1 }}
                  </div>
                  <div class="grey text2 font-size12">{{ item.text2 }}</div>
                </div>
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in area2"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <div class="item-txt">
                  <div class="font-size14 font-bold text1">
                    {{ item.text1 }}
                  </div>
                  <div class="grey text2 font-size12">{{ item.text2 }}</div>
                </div>
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in area2_1"
                :key="i"
                class="cursor background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <div class="item-txt">
                  {{ item.text1 }}
                </div>
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <a-flex align="center" gap="10" justify="space-between">
              <a-flex
                align="center"
                gap="10"
                justify="space-between"
                style="width: 600px"
                vertical
              >
                <div
                  v-for="(item, i) in area3"
                  :key="i"
                  class="cursor area3_content background-color-white"
                  @click="item.onClick(item)"
                >
                  <a-flex>
                    <img :src="item.image1_url" style="width: 60%" />
                    <div class="item-txt mt-10">
                      <div class="font-size14 font-bold text1">
                        {{ item.text1 }}
                      </div>
                      <div class="grey text2 font-size12">{{ item.text2 }}</div>
                    </div>
                  </a-flex>
                </div>
              </a-flex>

              <a-flex
                align="center"
                gap="10"
                justify="space-between"
                wrap="wrap"
              >
                <div
                  v-for="(item, i) in area3_1"
                  :key="i"
                  class="cursor area3_content2 background-color-white"
                  @click="item.onClick(item)"
                >
                  <img :src="item.image1_url" />
                  <div class="item-txt">
                    {{ item.text1 }}
                  </div>
                </div>
              </a-flex>
            </a-flex>
          </div>
        </div>
      </div>
    </div>
    <useModal :id="id" ref="ModalRef" @init="fetchData" />
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { clearArrays } from '@/utils/index'
  import useModal from '@/views/广告位弹窗'
  import { getAdverts } from '~/src/api/广告位'

  const route = useRoute()

  const ModalRef = ref('')
  const id = ref('')
  const onClick = (item) => {
    id.value = item.id
    ModalRef.value?.showModal()
  }
  const area1 = ref([
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
  ])
  const area2 = ref([
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
  ])
  const area2_1 = ref([
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
  ])
  const area3 = ref([
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
  ])
  const area3_1 = ref([
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
    {
      onClick: (item) => onClick(item),
    },
  ])

  const page_id = route.query?.id
  const recommendedPosition = reactive({
    G1: [],
    G2: [],
    G3: [],
    G4: [],
    G5: [],
  })
  const fetchData = async () => {
    const { data } = await getAdverts({
      page_id,
    })
    clearArrays(recommendedPosition)

    data.map((item) => {
      if (item.group_label == 'G1') recommendedPosition.G1.push(item)
      if (item.group_label == 'G2') recommendedPosition.G2.push(item)
      if (item.group_label == 'G3') recommendedPosition.G3.push(item)
    })

    area1.value.forEach((item, i) => {
      Object.assign(item, recommendedPosition.G1[i])
    })
    area2.value.forEach((item, i) => {
      Object.assign(item, recommendedPosition.G2[i])
    })
    area2_1.value.forEach((item, i) => {
      Object.assign(item, recommendedPosition.G2[i + 3])
    })
    Object.assign(area3.value[0], recommendedPosition.G3[0])
    Object.assign(area3.value[1], recommendedPosition.G3[5])
    Object.assign(area3.value[2], recommendedPosition.G3[6])
    area3_1.value.forEach((item, i) => {
      const index = i < 4 ? i + 1 : i + 3
      Object.assign(item, recommendedPosition.G3[index])
    })
  }
  fetchData()
</script>

<style lang="scss" scoped>
  .container {
    img {
      width: 100%;
      height: auto;
    }
    .title {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
    }
    .container_bottom {
      margin: 15px;
      .cursor {
        border: 1px dashed #9e9e9e;
      }
      .box {
        margin: 0 auto;
        border: 2px dashed #9e9e9e;
        width: 1000px;
        background-color: #f5f5f5;
        color: black;
        .bg {
          width: 100%;
        }
        .area3_content2 {
          width: 155px;
        }
      }
    }
  }
</style>
