<template>
  <a-modal
    :destroyOnClose="true"
    :open="albumShow"
    style="width: 1100px; top: 0px"
    title="绑定专辑"
    @cancel="onAlbumModalClose"
    @ok="onAlbumModalSubmit"
  >
    <a-form ref="albumFormRef" :label-col="{ span: 3 }" :model="form">
      <a-form-item label="添加队列" name="albumsAlias">
        <template v-for="item in albumsAlias" :key="item">
          <a-tag closable @close="delAlbum(item)">
            {{ item }}
          </a-tag>
        </template>
      </a-form-item>
      <a-form-item label="专辑搜索" name="albumName">
        <a-input
          v-model:value="form.albumName"
          :filter-option="false"
          label-in-value
          mode="multiple"
          :not-found-content="'没有搜索到...'"
          placeholder="输入视频名称快速查询"
          @change="handleAlbumNameChange"
        />
      </a-form-item>
    </a-form>
    <a-flex>
      <a-button class="flex_item" type="primary" @click="addCurrentPageAlbum">
        批量添加
      </a-button>
      <a-button
        class="flex_item"
        :disabled="albumsAlias.length == 0"
        type="primary"
        @click="clearAlbum"
      >
        重置
      </a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      :row-selection="rowSelection"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'gradesNames'">
          <a-tag v-for="tag in record.gradesNames" :key="tag" color="orange">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'subjectsNames'">
          <a-tag v-for="tag in record.subjectsNames" :key="tag" color="blue">
            {{ tag }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'cpName'">
          <a-tag color="purple">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'image'">
          <a-image
            :src="record.image1_url"
            style="margin-bottom: 5px; height: 30px"
          />
        </template>
        <template v-else-if="column.dataIndex === 'desc'">
          <a-tooltip>
            <template #title>{{ text }}</template>
            {{ record.sliceDesc }}
          </a-tooltip>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-button
            :disabled="albumsAlias.indexOf(`${record.id}-${record.title}`) > -1"
            type="primary"
            @click="addSingleAlbum(record)"
          >
            添加
          </a-button>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
  import { Table } from 'ant-design-vue'
  import { computed, reactive, ref, unref } from 'vue'
  import { usePagination } from 'vue-request'

  import { getAlbums } from '@/api/专辑'
  import { findTarget, getProperty, sliceTxt, sortFunc } from '@/utils'

  const emits = defineEmits(['cancel', 'ok'])
  // 绑定专辑
  const albumShow = ref(false)
  const albumFormRef = ref()
  const form = reactive({
    albumName: '', // 搜索框
  })
  const albums = ref([]) // 暴露给外部的数组
  const albumsAlias = ref([]) // 显示在添加队列

  // 删除标签
  const delAlbum = (val) => {
    const index = albumsAlias.value.findIndex((item) => item == val)
    albumsAlias.value.splice(index, 1)
  }
  // 添加
  const addSingleAlbum = (record) => {
    const album = `${record.id}-${record.title}`
    albumsAlias.value.push(album)
    albumsAlias.value.sort(sortFunc)
  }
  // 批量添加
  const addCurrentPageAlbum = () => {
    selectedRowKeys.value.forEach((item) => {
      const data = dataSource.value.find((d) => d.id == item)
      if (data) {
        const album = `${data.id}-${data.title}`
        if (findTarget(albumsAlias.value, album)) {
          albumsAlias.value.push(album)
          albumsAlias.value.sort(sortFunc)
        }
      }
    })
    selectedRowKeys.value = []
  }
  // 重新添加
  const clearAlbum = () => {
    albumsAlias.value = []
    selectedRowKeys.value = []
  }
  // 退出绑定
  const onAlbumModalClose = () => {
    albumShow.value = false
    emits('cancel')
    resetForm()
  }
  // 确认
  const onAlbumModalSubmit = async () => {
    albumShow.value = false
    albums.value = albumsAlias.value.map((item) => {
      return item
    })
    emits('ok', albums)
    resetForm()
  }
  // 重置表单
  const resetForm = () => {
    albumFormRef.value?.resetFields()
    selectedRowKeys.value = []
    albums.value = []
    albumsAlias.value = []
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getAlbums)
  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value?.data.map((item) => {
      return {
        ...item,
        key: item.id,
        gradesNames: getProperty(item.grades, 'name'),
        subjectsNames: getProperty(item.subjects, 'name'),
        cpName: item.cp?.name,
        cp: item.cp?.id,
        sliceDesc: sliceTxt(item.desc),
        desc: item.desc,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      width: '10%',
    },
    {
      title: '专辑名称',
      dataIndex: 'title',
      width: 250,
    },
    {
      title: '所属学级',
      dataIndex: 'gradesNames',
    },
    {
      title: '所属学科',
      dataIndex: 'subjectsNames',
    },
    {
      title: '供应商',
      dataIndex: 'cpName',
    },
    {
      title: '封面',
      dataIndex: 'image',
    },
    // {
    //   title: '介绍',
    //   dataIndex: 'desc',
    // },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })
  const selectedRowKeys = ref([])
  const onSelectChange = (changableRowKeys) => {
    console.log('selectedRowKeys changed: ', changableRowKeys)
    selectedRowKeys.value = changableRowKeys
  }
  const rowSelection = computed(() => {
    return {
      selectedRowKeys: unref(selectedRowKeys),
      onChange: onSelectChange,
      hideDefaultSelections: true,
      selections: [
        Table.SELECTION_ALL,
        Table.SELECTION_INVERT,
        Table.SELECTION_NONE,
      ],
    }
  })
  const initTable = async (
    page = pagination.value.current,
    pageSize = pagination.value.pageSize
  ) => {
    run({
      title: form.albumName,
      page,
      pageSize,
      type: unrelated_column_id.value ? undefined : 1,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
      is_top: unrelated_column_id.value ? 1 : undefined,
      unrelated_album_id: unrelated_album_id.value,
      unrelated_column_id: unrelated_column_id.value,
    })
  }
  // 搜索文本
  const handleAlbumNameChange = () => {
    pagination.value.current = 1
    pagination.value.pageSize = 10
    initTable()
  }
  // 翻页
  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    initTable()
  }

  const showDrawer = () => {
    albumShow.value = true
    initTable(1)
  }

  const unrelated_album_id = ref()
  const unrelated_column_id = ref()
  const setAlbumId = (val) => {
    unrelated_album_id.value = val
  }
  const setColumnId = (val) => {
    unrelated_column_id.value = val
  }

  defineExpose({
    showDrawer,
    setAlbumId,
    setColumnId,
  })
</script>

<style scoped>
  .flex_item {
    margin-left: 5px;
  }
</style>
