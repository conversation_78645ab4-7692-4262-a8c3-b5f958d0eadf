<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><UserOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item :label="label2">
            <a-input-search
              v-model:value="formState.username"
              allow-clear
              enter-button
              :placeholder="`输入${label2}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">用户列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加后台用户</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="['name'].includes(column.dataIndex)">
          <div>
            <a-input
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
            />
            <template v-else>
              <a-tag color="orange">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="['cps'].includes(column.dataIndex)">
          <div>
            <a-select
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id].cp_id"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="cpOptions"
              style="width: 150px"
            />
            <template v-else>
              <a-tag v-if="text" color="purple">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="['roles'].includes(column.dataIndex)">
          <div>
            <a-select
              v-if="editableData[record.id] && record.role_id != 1"
              v-model:value="editableData[record.id].role_id"
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="ruleOptions"
              style="width: 150px"
            />
            <template v-else>
              <a-tag v-if="text" color="blue">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="['status'].includes(column.dataIndex)">
          <a-switch
            v-model:checked="record.status"
            checked-children="封"
            :checkedValue="0"
            un-checked-children="解"
            :unCheckedValue="1"
            @change="onChange($event, record)"
          />
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div>
            <span v-if="editableData[record.id]">
              <a-space :size="10">
                <a-button type="primary" @click="save(record.id)">
                  保存
                </a-button>
                <a-popconfirm title="确定取消?" @confirm="cancel(record.id)">
                  <a-button>取消</a-button>
                </a-popconfirm>
              </a-space>
            </span>
            <span v-else>
              <a-space :size="10">
                <a-button type="primary" @click="edit(record.id)">
                  编辑
                </a-button>
                <a-button @click="editPassword(record.id)">修改密码</a-button>
              </a-space>
            </span>
          </div>
        </template>
      </template>
    </a-table>
    <CreateModal ref="modalRef" @ok="handleOk" />
    <EditPasswordModal ref="modalPasswordRef" @ok="handleOk" />
  </div>
</template>

<script setup>
  import { UserOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, provide, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getCps } from '@/api/CP'
  import { editUsers, getUsers } from '@/api/用户'
  import { getRoles } from '@/api/角色'
  import { routerReplace } from '~/src/utils/routes'

  import EditPasswordModal from './components/修改密码'
  import CreateModal from './components/创建用户'

  const route = useRoute()
  // 搜索框label
  const label = '用户名称'
  const label2 = '登录名'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    username: route.query?.username,
    site_id: 0,
  })
  // 搜索
  const handleSearch = () => handleOk()

  // 添加用户
  const onClick = () => {
    modalRef.value?.showModal()
  }

  // 创建用户-弹窗
  const modalRef = ref()
  const handleOk = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getUsers)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        role_id: item.roles?.[0]?.id ?? 0,
        roles: item.roles?.[0]?.name ?? '',
        cp_id: item.cps?.[0]?.id ?? 0,
        cps: item.cps?.[0]?.name ?? '',
      }
    })
  })
  const columns = ref([
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: label2,
      dataIndex: 'username',
    },
    {
      title: '供应商名称',
      dataIndex: 'cps',
    },
    {
      title: '角色',
      dataIndex: 'roles',
    },
    {
      title: '封禁/解禁',
      dataIndex: 'status',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ])
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      username: formState.username,
      site_id: formState.site_id,
    })
    routerReplace(pagination.value, {
      name: formState.name,
      username: formState.username,
    })
  }

  // 编辑表格
  const editableData = reactive({})
  // 编辑表格
  const edit = (id) => {
    if (id == 1) return message.warning('该用户不允许编辑')
    editableData[id] = cloneDeep(
      dataSource.value.filter((item) => id === item.id)[0]
    )
  }
  // 保存表格
  const save = async (id) => {
    console.log(id)
    const { code } = await editUsers(
      {
        name: editableData[id].name,
        role_id: editableData[id].role_id,
        cp_id: editableData[id].cp_id,
      },
      id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
      delete editableData[id]
    }
  }
  // 取消编辑
  const cancel = (id) => {
    delete editableData[id]
  }

  // 修改密码
  const modalPasswordRef = ref()
  const editPassword = async (id) => {
    // if (id == 1) return message.warning('该用户不允许编辑')
    modalPasswordRef.value?.setId(id)
    modalPasswordRef.value?.showModal()
  }

  // 账号状态
  const onChange = async (checked, record) => {
    console.log(checked, record)
    const { code } = await editUsers(
      {
        name: record.name,
        status: checked,
      },
      record.id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
    }
  }

  // 供应商
  const cpOptions = ref()
  const handleCpData = async () => {
    const { data } = await getCps()
    cpOptions.value = data
    cpOptions.value.unshift({
      name: '暂不绑定',
      id: 0,
    })
  }

  // 角色

  const ruleOptions = ref()
  const handleRuleData = async () => {
    const { data } = await getRoles()
    ruleOptions.value = data
    ruleOptions.value.unshift({
      name: '暂不绑定',
      id: 0,
    })
  }
  provide('ruleOptions', ruleOptions)
  provide('cpOptions', cpOptions)
  handleRuleData()
  handleCpData()
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
