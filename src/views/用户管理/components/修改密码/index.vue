<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="修改密码"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item
        label="原密码"
        name="oldPassword"
        :rules="[
          {
            required: true,
            trigger: 'change',
            message: '请输入原密码',
          },
        ]"
      >
        <a-input
          v-model:value="formState.oldPassword"
          :allowClear="true"
          autocomplete="off"
          :maxlength="30"
          placeholder="请输入原密码"
          :showCount="true"
          type="password"
        />
      </a-form-item>
      <a-form-item
        label="密码设置"
        name="password"
        :rules="[
          {
            required: true,
            validator: validatePass,
            trigger: 'change',
          },
        ]"
      >
        <a-input
          v-model:value="formState.password"
          :allowClear="true"
          autocomplete="off"
          :maxlength="30"
          placeholder="请输入密码"
          :showCount="true"
          type="password"
        />
      </a-form-item>
      <a-form-item
        label="确认密码"
        name="checkPass"
        :rules="[
          {
            required: true,
            validator: validateCheckPass,
            trigger: 'change',
          },
        ]"
      >
        <a-input
          v-model:value="formState.checkPass"
          :allowClear="true"
          autocomplete="off"
          :maxlength="30"
          placeholder="确认密码"
          :showCount="true"
          type="password"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { editUsers, getUsersDetail } from '@/api/用户'

  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    name: '',
    oldPassword: '',
    password: '',
    checkPass: '',
  })

  const validatePass = (_rule, value) => {
    const lengthRegex = /.{8,}/
    const letterRegex = /[a-zA-Z]/
    const numberRegex = /[0-9]/
    const specialCharRegex = /[!@#$%^&*(),.?":{}|<>]/
    const lengthValid = lengthRegex.test(value)
    const hasLetter = letterRegex.test(value)
    const hasNumber = numberRegex.test(value)
    const hasSpecialChar = specialCharRegex.test(value)
    if (!lengthValid) {
      return Promise.reject('密码长度必须至少为 8 个字符')
    }
    if (!hasLetter) {
      return Promise.reject('密码必须包含至少一个字母')
    }
    if (!hasNumber) {
      return Promise.reject('密码必须包含至少一个数字')
    }
    if (!hasSpecialChar) {
      return Promise.reject('密码必须包含至少一个特殊字符')
    }
    if (formState.checkPass !== '') {
      formRef.value.validateFields('checkPass')
    }
    return Promise.resolve()
  }
  const validateCheckPass = (_rule, value) => {
    if (value === '') {
      return Promise.reject('请再次输入密码')
    } else if (value !== formState.password) {
      return Promise.reject('两次密码不一致')
    }
    return Promise.resolve()
  }

  const open = ref(false)
  const showModal = async () => {
    open.value = true
    const {
      data: { name },
    } = await getUsersDetail(id.value)
    formState.name = name
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editUsers(formState, id.value)

    if (code == 200) {
      message.success('修改成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
