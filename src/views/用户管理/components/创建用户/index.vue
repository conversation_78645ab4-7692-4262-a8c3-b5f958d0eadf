<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="创建后台用户"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item
        label="用户名称"
        name="name"
        :rules="[{ required: true, message: '用户名称不能为空!' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入用户名称" />
      </a-form-item>
      <a-form-item
        label="登录名"
        name="username"
        :rules="[{ required: true, message: '登录名不能为空!' }]"
      >
        <a-input
          v-model:value="formState.username"
          placeholder="请输入登录名"
        />
      </a-form-item>
      <a-form-item
        label="密码设置"
        name="password"
        :rules="[
          {
            required: true,
            validator: validatePass,
            trigger: 'change',
          },
        ]"
      >
        <a-input
          v-model:value="formState.password"
          :allowClear="true"
          autocomplete="off"
          :maxlength="30"
          placeholder="请输入密码"
          :showCount="true"
          type="password"
        />
      </a-form-item>
      <a-form-item
        label="确认密码"
        name="checkPass"
        :rules="[
          {
            required: true,
            validator: validateCheckPass,
            trigger: 'change',
          },
        ]"
      >
        <a-input
          v-model:value="formState.checkPass"
          :allowClear="true"
          autocomplete="off"
          :maxlength="30"
          placeholder="确认密码"
          :showCount="true"
          type="password"
        />
      </a-form-item>
      <a-form-item label="角色" name="cp_id">
        <a-select
          v-model:value="formState.role_id"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="ruleOptions"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item label="供应商" name="role_id">
        <a-select
          v-model:value="formState.cp_id"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="cpOptions"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { inject, reactive, ref } from 'vue'

  import { createUsers } from '@/api/用户'

  const emits = defineEmits(['cancel', 'ok'])

  const formRef = ref()
  const formState = reactive({
    name: '',
    username: '',
    password: '',
    checkPass: '',
    role_id: 0,
    cp_id: 0,
  })

  const validatePass = (_rule, value) => {
    const lengthRegex = /.{8,}/
    const letterRegex = /[a-zA-Z]/
    const numberRegex = /[0-9]/
    const specialCharRegex = /[!@#$%^&*(),.?":{}|<>]/
    const lengthValid = lengthRegex.test(value)
    const hasLetter = letterRegex.test(value)
    const hasNumber = numberRegex.test(value)
    const hasSpecialChar = specialCharRegex.test(value)
    if (!lengthValid) {
      return Promise.reject('密码长度必须至少为 8 个字符')
    }
    if (!hasLetter) {
      return Promise.reject('密码必须包含至少一个字母')
    }
    if (!hasNumber) {
      return Promise.reject('密码必须包含至少一个数字')
    }
    if (!hasSpecialChar) {
      return Promise.reject('密码必须包含至少一个特殊字符')
    }
    if (formState.checkPass !== '') {
      formRef.value.validateFields('checkPass')
    }
    return Promise.resolve()
  }
  const validateCheckPass = (_rule, value) => {
    if (value === '') {
      return Promise.reject('请再次输入密码')
    } else if (value !== formState.password) {
      return Promise.reject('两次密码不一致')
    }
    return Promise.resolve()
  }

  const open = ref(false)
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createUsers(formState)

    if (code == 200) {
      message.success('创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  // 供应商
  const cpOptions = inject('cpOptions')

  // 角色
  const ruleOptions = inject('ruleOptions')

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
