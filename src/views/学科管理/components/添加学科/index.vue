<template>
  <a-modal
    v-model:open="open"
    :title="`新建${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `请输入${label}的名称!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label}名称`"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createSubjects } from '@/api/学科'

  const label = '学科'
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    name: '',
  })
  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createSubjects({
      name: formState.name,
    })
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
