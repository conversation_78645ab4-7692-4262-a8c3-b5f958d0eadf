<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><UserOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex align="center" gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.user_id"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="操作行为">
            <a-select
              v-model:value="formState.operation_type"
              allow-clear
              :options="[
                {
                  label: '创建',
                  value: '创建',
                },
                {
                  label: '修改',
                  value: '修改',
                },
                {
                  label: '删除',
                  value: '删除',
                },
              ]"
              :style="style"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="时间">
            <a-date-picker
              v-model:value="formState.date"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">用户列表</div>
    </a-flex>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    />
  </div>
</template>

<script setup>
  import { UserOutlined } from '@ant-design/icons-vue'
  import dayjs from 'dayjs'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getOperationRecords } from '@/api/操作日志'
  import { routerReplace } from '~/src/utils/routes'

  const style = { width: '140px' }
  const route = useRoute()
  const dateFormat = 'YYYY-MM-DD'
  // 搜索框label
  const label = '用户id'
  // 搜索表单
  const formState = reactive({
    user_id: route.query?.user_id,
    operation_type: route.query?.operation_type,
    date: route.query?.date ? dayjs(route.query?.date) : undefined,
  })
  // 搜索
  const handleSearch = () => handleOk()

  const handleOk = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getOperationRecords)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        userName: item.user.name,
      }
    })
  })
  const columns = ref([
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '用户id',
      dataIndex: 'user_id',
    },
    {
      title: '操作内容',
      dataIndex: 'operation_content',
    },
    {
      title: '操作用户',
      dataIndex: 'userName',
    },
    {
      title: '操作行为',
      dataIndex: 'operation_type',
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
    },
  ])
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      user_id: formState.user_id,
      operation_type: formState.operation_type,
      date: formState.date ? formState.date.format(dateFormat) : undefined,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      user_id: formState.user_id,
      operation_type: formState.operation_type,
      date: formState.date ? formState.date.format(dateFormat) : undefined,
    })
  }
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
