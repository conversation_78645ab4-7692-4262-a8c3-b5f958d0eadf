<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><PlayCircleOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.title"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="供应商">
            <a-select
              v-model:value="formState.cp_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              :style="style"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="栏目列表">
            <a-select
              v-model:value="formState.top_column_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="columnOptions"
              :style="style"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">视频列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加视频</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <a-image :src="record.cover_url" style="width: 100px" />
        </template>
        <template v-else-if="column.dataIndex === 'cp_name'">
          <a-tag color="purple">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'albums'">
          <span v-for="tag in record.albums" :key="tag">
            {{ tag.name }}
          </span>
        </template>
        <template v-else-if="column.dataIndex === 'play'">
          <a-button type="primary" @click="onPreview(record)">预览</a-button>
          <a-modal
            :destroyOnClose="true"
            :footer="null"
            :open="previewVisible"
            @cancel="handleCancel"
          >
            <video
              ref="videoRef"
              controls
              :poster="poster"
              :src="previewImage"
              style="width: 95%"
            />
          </a-modal>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <a-space :size="10">
            <a-button type="primary" @click="onEdit(record)">修改</a-button>
            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record)"
            >
              <a-button
                v-permissions="['administrator', 'admin.normal']"
                danger
                type="primary"
              >
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <a-modal
      v-model:open="open"
      :destroyOnClose="true"
      :maskClosable="false"
      okText="保存修改"
      title="编辑封面"
      @cancel="cancel"
      @ok="handleAudioOk"
    >
      <a-form ref="formRef" :model="params">
        <a-form-item
          label="标题"
          name="title"
          :rules="[{ required: true, message: '请输入标题!' }]"
        >
          <a-input v-model:value="params.title" />
        </a-form-item>
        <a-form-item label="封面">
          <Audio
            ref="AudioRef"
            @init="getImageAddress"
            @setImageAddress="handleParamsCover_url_id"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button key="back" @click="cancel">取消</a-button>
        <a-button key="submit" type="primary" @click="handleAudioOk">
          确定
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
  import { PlayCircleOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import hlsjs from 'hls.js'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getCps } from '@/api/CP'
  import { getColumns } from '@/api/栏目'
  import {
    deleteVideos,
    editVideos,
    getVideos,
    getVideosDetail,
  } from '@/api/视频'
  import { bytesToMB, secondsToMinutes } from '@/utils/index'
  import { pushWithQuery, routerReplace } from '@/utils/routes'
  import Audio from '@/views/audio'

  const style = { width: '140px' }
  const route = useRoute()
  // 搜索框label
  const label = '视频标题名称'
  // 搜索表单
  const formState = reactive({
    title: route.query?.title,
    cp_id: route.query?.cp_id ? parseInt(route.query?.cp_id) : undefined,
    top_column_id: route.query?.top_column_id
      ? parseInt(route.query?.top_column_id)
      : undefined,
  })
  // 供应商
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getCps()
    options.value = data
  }
  // 栏目
  const columnOptions = ref([])
  const handleColumnOptions = async () => {
    const { data } = await getColumns({
      parent_id: 0,
    })
    columnOptions.value = data
  }

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getVideos)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        cp_name: item.cp.name,
        file_size: bytesToMB(item.file_size),
        duration: secondsToMinutes(item.duration),
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'title',
    },
    {
      title: '供应商',
      dataIndex: 'cp_name',
    },
    {
      title: '所属专辑包',
      dataIndex: 'albums',
    },
    {
      title: '文件大小(MB)',
      dataIndex: 'file_size',
    },
    {
      title: '时长(分)',
      dataIndex: 'duration',
    },
    {
      title: '封面',
      dataIndex: 'action',
    },
    {
      title: '预览播放',
      dataIndex: 'play',
    },
    {
      title: '操作',
      dataIndex: 'operation',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      title: formState.title,
      cp_id: formState.cp_id,
      top_column_id: formState.top_column_id,
    })

    routerReplace(pagination.value, {
      title: formState.title,
      cp_id: formState.cp_id,
      top_column_id: formState.top_column_id,
    })
  }

  const onClick = () => {
    pushWithQuery('FileUploadAdmin')
  }

  // 编辑封面
  const AudioRef = ref('')
  const open = ref(false)
  const id = ref('')
  // 表格-编辑
  const onEdit = (record) => {
    open.value = true
    id.value = record.id
  }
  // 编辑封面-确定
  const handleAudioOk = async () => {
    if (AudioRef.value?.loading) {
      return message.warning('封面正在上传中')
    }
    const { code } = await editVideos(params, id.value)
    if (code == 200) {
      init()
      message.success('修改成功')
    }
    open.value = false
  }
  // 编辑封面-取消
  const cancel = () => {
    if (AudioRef.value?.loading) {
      return message.warning('封面正在上传中')
    }
    open.value = false
    params.title = ''
    params.cover_url_id = ''
  }

  // 视频预览
  const previewVisible = ref(false)
  const previewImage = ref('')
  const poster = ref('')
  const handleCancel = () => {
    previewVisible.value = false
    previewImage.value = ''
    poster.value = ''
  }
  const videoRef = ref()
  // 预览
  const onPreview = async (item) => {
    console.log(item)
    previewVisible.value = true
    poster.value = item.cover_url
    const {
      data: { video_url },
    } = await getVideosDetail(item.id)
    if (video_url.indexOf('m3u8') != -1) {
      const hls = new hlsjs()
      hls.loadSource(video_url)
      hls.attachMedia(videoRef.value)
      hls.on(hlsjs.Events.MANIFEST_PARSED, function () {
        videoRef.value.play()
      })
      return
    }
    previewImage.value = video_url
    // window.open(item.cover_url, '_blank')
  }
  // 表格-删除
  const onDelete = async (item) => {
    const { code } = await deleteVideos(item.id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  const params = reactive({
    title: '', // 标签
    play_id: '', // 阿里云返回id 或者 未来学校填写的 id
    upload_type: undefined, // 上传类型  1 阿里 2未来学校
    cp_id: undefined, // 这里 如果上传类型是未来学校 那么 cp 就需要和 未来学校 同步
    cover_url_id: '',
  })
  const handleInit = async (value) => {
    const { data } = await getVideosDetail(value)
    params.play_id = data.play_id
    params.title = data.title
    params.cp_id = data.cp_id
    params.upload_type = data.upload_type
    return data
  }
  // 设置图片初始化回调
  const getImageAddress = async () => {
    const data = await handleInit(id.value)
    AudioRef.value?.fileList.push({
      url: data.cover_url,
    })
  }
  // 设置图片上传成功回调
  const handleParamsCover_url_id = (value) => {
    params.cover_url_id = value
  }

  init()
  handleOptions()
  handleColumnOptions()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
