<template>
  <div class="container">
    <a-flex align="center" class="title">
      <EditOutlined class="pr-10" />
      <span class="font-size16">k12初中教育</span>
    </a-flex>
    <!-- <a-divider style="background-color: #ebeef5" /> -->
    <div class="container-bottom">
      <div class="box">
        <div class="m-10">
          <div class="bg">
            <a-carousel>
              <div
                v-for="(item, i) in recommendedPosition.G5"
                :key="i"
                class="cursor"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-carousel>
          </div>
          <div class="mt-50">
            <div
              v-for="(item, i) in recommendedPosition.G6"
              :key="i"
              class="area1_title cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G1"
                :key="i"
                class="cursor area1_content"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G7"
              :key="i"
              class="area1_title cursor"
              @click="item.onClick(item)"
            >
              <img :src="item.image1_url" />
            </div>
            <div
              v-for="(item, i) in recommendedPosition.G8"
              :key="i"
              class="area1_text cursor mt-10"
              @click="item.onClick(item)"
            >
              {{ item.text1 }}
            </div>
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in recommendedPosition.G2?.slice(0, 3)"
                :key="i"
                class="cursor area2_content background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
              <a-flex align="center" justify="space-between" vertical>
                <div
                  v-for="(item, i) in recommendedPosition.G2?.slice(3)"
                  :key="i"
                  class="cursor area2_content2"
                  @click="item.onClick(item)"
                >
                  <img :src="item.image1_url" />
                </div>
              </a-flex>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G9"
              :key="i"
              class="area1_text cursor mt-10"
              @click="item.onClick(item)"
            >
              {{ item.text1 }}
            </div>
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in recommendedPosition.G3?.slice(0, 2)"
                :key="i"
                class="cursor area3_content background-color-white"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
                <use-item :item="item" :type="'text1'" />
              </div>
              <a-flex align="center" vertical>
                <div
                  v-for="(item, i) in recommendedPosition.G3?.slice(2, 3)"
                  :key="i"
                  class="cursor area3_content2_top"
                  gap="10"
                  @click="item.onClick(item)"
                >
                  <img :src="item.image1_url" />
                </div>

                <a-flex
                  align="center"
                  class="area3_content2_top mt-10"
                  gap="10"
                  justify="space-between"
                >
                  <div
                    v-for="(item, i) in recommendedPosition.G3?.slice(3, 5)"
                    :key="i"
                    class="cursor area3_content2_bottom"
                    @click="item.onClick(item)"
                  >
                    <img :src="item.image1_url" />
                  </div>
                </a-flex>
              </a-flex>
            </a-flex>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G3?.slice(5)"
                :key="i"
                class="cursor area3_content2"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-flex>
          </div>

          <div class="mt-10">
            <div
              v-for="(item, i) in recommendedPosition.G10"
              :key="i"
              class="area1_text cursor mt-10"
              @click="item.onClick(item)"
            >
              {{ item.text1 }}
            </div>
            <a-flex align="center" gap="10" justify="space-between">
              <div
                v-for="(item, i) in recommendedPosition.G4?.slice(0, 4)"
                :key="i"
                class="cursor area4_content"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-flex>
            <a-flex
              align="center"
              class="mt-10"
              gap="10"
              justify="space-between"
            >
              <div
                v-for="(item, i) in recommendedPosition.G4?.slice(4)"
                :key="i"
                class="cursor area4_content2"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-flex>
          </div>
        </div>
      </div>
    </div>
    <useModal :id="id" ref="ModalRef" @init="fetchData" />
    <Release :id="page_id" />
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { clearArrays } from '@/utils/index'
  import useModal from '@/views/广告位弹窗'
  import Release from '@/views/页面发布'
  import { getAdverts } from '~/src/api/广告位'
  const route = useRoute()

  const ModalRef = ref('')
  const id = ref('')
  const onClick = (item) => {
    id.value = item.id
    ModalRef.value?.showModal()
  }

  const page_id = route.query?.id
  const recommendedPosition = reactive({})
  const fetchData = async () => {
    const { data } = await getAdverts({
      page_id,
    })
    clearArrays(recommendedPosition)
    data.map((item) => {
      if (!recommendedPosition[item.group_label]) {
        recommendedPosition[item.group_label] = []
      }
      recommendedPosition[item.group_label].push(
        Object.assign(item, {
          onClick: (x) => onClick(x),
        })
      )
    })
  }
  fetchData()
</script>

<style lang="scss" scoped>
  .container {
    img {
      width: 100%;
      height: auto;
    }
    .title {
      padding: 15px;
      border-bottom: 1px solid #ebeef5;
    }
    .container-bottom {
      margin: 15px;
      .cursor {
        border: 1px dashed #9e9e9e;
      }
      .box {
        margin: 0 auto;
        border: 2px dashed #9e9e9e;
        width: 1000px;
        background-color: #f5f5f5;
        color: black;
        .bg {
          width: 100%;
          border: 1px dashed #9e9e9e;
        }
        .area1_title,
        .area2_title,
        .area3_title,
        .area4_title {
          width: 140px;
          border: 1px dashed #9e9e9e;
        }
        .area1_text {
          height: 30px;
          line-height: 30px;
          padding-left: 10px;
        }
      }
    }
  }
</style>
