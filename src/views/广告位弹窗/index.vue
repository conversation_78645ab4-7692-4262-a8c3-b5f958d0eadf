<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="广告位修改"
    @ok="handleOk"
  >
    <a-form-item label="标题1" name="text1">
      <a-input v-model:value="params.text1" />
    </a-form-item>
    <a-form-item label="标题2" name="text2">
      <a-input v-model:value="params.text2" />
    </a-form-item>
    <a-form-item label="链接地址" name="text2">
      <a-input v-model:value="params.url" />
    </a-form-item>
    <a-form-item label="图片" name="image1">
      <Audio ref="AudioRef" @init="init" @setImageAddress="setImageAddress" />
    </a-form-item>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { onMounted, reactive, ref, toRefs } from 'vue'

  import { editAdverts, getAdvertsDetail } from '@/api/广告位'
  import Audio from '@/views/audio'
  const props = defineProps(['id'])
  const emits = defineEmits(['init'])
  const { id } = toRefs(props)
  const params = reactive({
    image1: '',
    text1: '',
    text2: '',
    url: '',
  })
  const AudioRef = ref('')
  const init = async () => {
    const { data } = await getAdvertsDetail(id.value)
    console.log(data)
    Object.assign(params, data)
    AudioRef.value?.fileList.push({
      url: data.image1_url,
    })
  }
  const setImageAddress = (value) => {
    params.image1 = value
  }

  const open = ref(false)
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    const { code } = await editAdverts(params, id.value)
    if (code == 200) {
      emits('init')
      open.value = false
      message.success('修改成功')
    }
  }
  onMounted(() => {
    id.value && init()
  })
  defineExpose({
    showModal,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
