<template>
  <div>
    <a-flex align="center" justify="space-between">
      <a-flex align="center" gap="small" justify="center">
        <PlayCircleOutlined />
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="pb-10" gap="middle" justify="flex-start">
      <DefaultDivider />
      <div class="font-bold">视频上传列表</div>
    </a-flex>
    <a-flex class="p-10" gap="10">
      <a-button type="primary" @click="onClick">添加视频</a-button>
    </a-flex>

    <a-modal
      v-model:open="open"
      :maskClosable="false"
      title="添加视频"
      width="1000px"
      @cancel="handleCancel"
      @ok="handleOk"
    >
      <template #footer>
        <a-button v-show="show" key="submit" type="primary" @click="handleOk">
          {{ VideoRef?.formState?.cp_id == 2 ? '创建视频' : '开始上传' }}
        </a-button>
      </template>
      <Video ref="VideoRef" :show="show" />
    </a-modal>
    <a-table :columns="columns" :data-source="dataSource" tableLayout="fixed">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'percent'">
          <template v-if="record.percent < 100">
            <a-progress
              :percent="record.percent"
              size="small"
              style="width: 200px"
            />
          </template>
          <template v-else>上传完成</template>
        </template>
        <template v-else-if="column.dataIndex === 'size'">
          {{ bytesToMB(record.size) }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { PlayCircleOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { ref } from 'vue'
  import { onBeforeRouteLeave, useRoute } from 'vue-router'

  import { bytesToMB } from '@/utils/index'
  import Video from '@/views/video'

  const route = useRoute()
  //弹窗
  const VideoRef = ref('')
  const open = ref(false)
  const show = ref(true)
  const onClick = () => {
    open.value = true
  }
  // 取消
  const handleCancel = () => {
    VideoRef.value?.resetFields()
    show.value = true
  }
  const handleOk = async () => {
    await VideoRef.value?.onSubmit()
    VideoRef.value?.fileList.forEach((item) => {
      dataSource.value.push(item)
    })
    console.log(dataSource.value)
    show.value = false
    open.value = false
  }

  const dataSource = ref([])
  const columns = [
    {
      title: '文件名称',
      dataIndex: 'name',
    },
    {
      title: '文件大小(MB)',
      dataIndex: 'size',
    },
    {
      title: '格式',
      dataIndex: 'type',
    },
    {
      title: '上传状态',
      dataIndex: 'percent',
    },
  ]
  // 与 beforeRouteLeave 相同，无法访问 `this`
  onBeforeRouteLeave(() => {
    console.log(VideoRef.value.loading)
    if (VideoRef.value?.loading) {
      message.warning('文件正在上传中,请勿离开当前页面')
      return false
    }
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
