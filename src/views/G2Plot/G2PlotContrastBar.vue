<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { BidirectionalBar } from '@antv/g2plot'
  import {
    onMounted,
    onUnmounted,
    ref,
    toRefs,
    watch,
    // inject
  } from 'vue'

  const props = defineProps(['data', 'option', 'labelTitle'])
  const { data, option } = toRefs(props)
  console.log(data)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new BidirectionalBar(useRef.value, {
      data: data.value,
      xField: 'title', // title
      interactions: [{ type: 'active-region' }],
      yField: ['上月', '当月'],

      appendPadding: [50, 50, 0, 50],
      xAxis: {
        position: 'bottom',
      },
      label: {
        position: 'right', // 'top' | 'bottom' | 'middle' | 'left' | 'right'
        // 配置样式
        // content: (val) => `${val.title}${labelTitle?.value ?? ''}`,
        style: {
          fill: '#000000',
          opacity: 1,
        },
      },
      tooltip: {
        shared: true,
        showMarkers: false,
      },
      ...option?.value,
    })
    G2Plot.render()
  }

  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onChangeSize = (width, height) => {
    if (G2Plot.chart.height !== height) G2Plot.changeSize(width, height)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })

  watch(
    () => data,
    () => {
      onChangeData()
      onChangeSize(undefined, data.value.length * 30)
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
