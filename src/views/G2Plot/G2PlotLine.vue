<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { Line } from '@antv/g2plot'
  import {
    onMounted,
    onUnmounted,
    ref,
    toRefs,
    watch,
    // inject
  } from 'vue'

  const props = defineProps(['data', 'option'])
  const { data, option } = toRefs(props)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new Line(useRef.value, {
      data: data.value,
      xField: 'title',
      yField: 'value',

      meta: {
        title: {
          alias: '日期',
        },
        value: {
          alias: '数量',
        },
      },

      smooth: true,
      ...option?.value,
    })
    G2Plot.render()
  }

  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })
  watch(
    () => data,
    () => {
      onChangeData()
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
