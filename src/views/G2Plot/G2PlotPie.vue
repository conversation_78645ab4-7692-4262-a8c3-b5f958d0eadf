<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { G2, Pie } from '@antv/g2plot'
  import { onMounted, onUnmounted, ref, toRefs, watch } from 'vue'
  const G = G2.getEngine('canvas')
  const props = defineProps(['data', 'option'])
  const { option, data } = toRefs(props)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new Pie(useRef.value, {
      appendPadding: 10,
      data: data.value,
      angleField: 'value',
      colorField: 'type',
      radius: 0.7,
      legend: false,
      label: {
        type: 'spider',
        labelHeight: 40,
        formatter: (data, mappingData) => {
          const group = new G.Group({})
          group.addShape({
            type: 'circle',
            attrs: {
              x: 0,
              y: 0,
              width: 40,
              height: 50,
              r: 5,
              fill: mappingData.color,
            },
          })
          group.addShape({
            type: 'text',
            attrs: {
              x: 10,
              y: 8,
              text: `${data.type}`,
              fill: mappingData.color,
            },
          })
          group.addShape({
            type: 'text',
            attrs: {
              x: 0,
              y: 25,
              text: `${data.value}次 ${(data.percent * 100).toFixed(0)}%`,
              fill: 'rgba(0, 0, 0, 0.65)',
              fontWeight: 700,
            },
          })
          return group
        },
      },
      interactions: [{ type: 'element-selected' }, { type: 'element-active' }],
      ...option?.value,
    })
    G2Plot.render()
  }
  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })
  watch(
    () => data,
    () => {
      onChangeData()
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
