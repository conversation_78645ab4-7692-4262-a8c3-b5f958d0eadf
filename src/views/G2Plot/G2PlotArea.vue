<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { Area } from '@antv/g2plot'
  import { onMounted, onUnmounted, ref, toRefs, watch } from 'vue'

  const props = defineProps(['data', 'option'])
  const { option, data } = toRefs(props)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new Area(useRef.value, {
      data: data.value,
      xField: 'title',
      yField: 'value',
      meta: {
        title: {
          alias: '日期',
        },
        value: {
          alias: '数量',
        },
      },
      xAxis: {
        title: null,
        label: null,
      },
      yAxis: {
        title: null,
        label: null,
      },
      ...option?.value,
    })
    G2Plot.render()
  }
  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })
  watch(
    () => data,
    () => {
      onChangeData()
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
