<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { Column } from '@antv/g2plot'
  import {
    onMounted,
    onUnmounted,
    ref,
    toRefs,
    watch,
    // inject
  } from 'vue'

  const props = defineProps(['data', 'option'])
  const { option, data } = toRefs(props)
  const emits = defineEmits(['click'])
  const useRef = ref(null)
  let G2Plot = null
  const hoverData = ref('')
  const createChart = () => {
    G2Plot = new Column(useRef.value, {
      data: data.value,
      xField: 'title',
      yField: 'value',
      seriesField: 'title',
      meta: {
        title: {
          alias: '月份',
        },
        value: {
          alias: '数量',
        },
      },
      xAxis: {
        // 设置默认不自动隐藏，或
        // title: {
        //   autoRotate: 'false',
        //   rotation: 45,
        // },
        label: {
          autoRotate: true,
          rotate: 75,
          autoHide: false,
          offset: 10,
          offsetX: -10,
          style: {
            fontWeight: 'bold',
          },
        },
      },
      yAxis: {
        // 坐标轴刻度数量
        // tickCount: 8,
      },
      label: {
        // 可手动配置 label 数据标签位置
        position: 'middle', // 'top' | 'bottom' | 'middle' | 'left' | 'right'
        // 配置样式
        content: (val) => `${val.value}`,
        style: {
          fill: '#000000',
          opacity: 1,
        },
      },
      // seriesField: 'type',
      // isGroup: true,
      //  legend: { layout: 'horizontal', position: 'top' },
      // columnStyle: {
      //   radius: [4, 4, 0, 0],
      // },
      // autoFit: true,
      // width: 1200,
      ...option?.value,
    })

    G2Plot.on('tooltip:change', (ev) => {
      hoverData.value = ev.data.items[0].data
    })
    G2Plot.on('plot:click', () => {
      emits('click', hoverData)
    })
    G2Plot.render()
  }

  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }

  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })

  watch(
    () => data,
    () => {
      onChangeData()
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
