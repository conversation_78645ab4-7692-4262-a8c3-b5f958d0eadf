<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { Liquid } from '@antv/g2plot'
  import { onMounted, onUnmounted,ref, toRefs, watch } from 'vue'

  const props = defineProps(['percent', 'option'])
  const { percent, option } = toRefs(props)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new Liquid(useRef.value, {
      autoFit: true,
      height: 100,
      width: 100,
      percent: percent.value,
      // outline: {
      //   border: 4,
      //   distance: 8,
      // },
      wave: {
        count: 4,
        length: 128,
      },
      statistic: {
        content: {
          style: {
            fontSize: 12,
          },
        },
      },
      // color: ['#5B8FF9', '#E8EDF3'],
      ...option?.value,
    })
    G2Plot.render()
  }
  const onChangeData = () => {
    G2Plot.changeData(percent.value)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })
  watch(
    () => percent,
    () => {
      onChangeData()
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
