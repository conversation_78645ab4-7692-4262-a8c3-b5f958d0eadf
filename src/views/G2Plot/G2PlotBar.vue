<template>
  <div ref="useRef"></div>
</template>

<script setup>
  import { Bar } from '@antv/g2plot'
  import {
    onMounted,
    onUnmounted,
    ref,
    toRefs,
    watch,
    // inject
  } from 'vue'

  const props = defineProps(['data', 'option', 'labelTitle'])
  const { data, labelTitle, option } = toRefs(props)
  console.log(data)
  const useRef = ref(null)
  let G2Plot = null

  const createChart = () => {
    G2Plot = new Bar(useRef.value, {
      data: data.value,
      xField: 'value', // title
      yField: 'title',
      seriesField: 'title',
      meta: {
        title: {
          alias: '渠道',
        },
        value: {
          alias: '数量',
        },
      },
      appendPadding: [0, 50, 0, 0],
      xAxis: {
        // 坐标轴刻度数量
        tickCount: 8,
      },
      label: {
        position: 'right', // 'top' | 'bottom' | 'middle' | 'left' | 'right'
        // 配置样式
        content: (val) => `${val.value}${labelTitle?.value ?? ''}`,
        style: {
          fill: '#000000',
          opacity: 1,
        },
      },
      barBackground: {
        style: {
          fill: 'rgba(0,0,0,0.1)',
        },
      },
      // barWidthRatio: 0.8,
      minBarWidth: 20,
      maxBarWidth: 20,
      // scrollbar: {
      //   type: 'vertical',
      // },
      ...option?.value,
    })
    G2Plot.render()
  }

  const onChangeData = () => {
    G2Plot.changeData(data.value)
  }
  const onChangeSize = (width, height) => {
    if (G2Plot.chart.height !== height) G2Plot.changeSize(width, height)
  }
  const onDestroy = () => {
    G2Plot.destroy()
  }
  onMounted(() => {
    createChart()
  })
  onUnmounted(() => {
    onDestroy()
  })

  watch(
    () => data,
    () => {
      onChangeData()
      onChangeSize(undefined, data.value.length * 30)
    },
    { deep: true, flush: 'post' }
  )
</script>

<style lang="less" scoped></style>
