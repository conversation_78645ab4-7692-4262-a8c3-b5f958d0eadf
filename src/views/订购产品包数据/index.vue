<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><vab-icon :icon="route.meta.icon" /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <!-- <a-form-item label="订购类型">
            <a-select
              v-model:value="formState.type"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="[
                {
                  name: '外来订购',
                  id: 1,
                },
                {
                  name: '续订',
                  id: 2,
                },
                {
                  name: '手动订购',
                  id: 3,
                },
              ]"
              :style="{ width: '200px' }"
              @change="onChange"
            />
          </a-form-item> -->
          <a-form-item label="订购状态">
            <a-select
              v-model:value="formState.status"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="[
                {
                  name: '订购',
                  id: 1,
                },
                {
                  name: '退订',
                  id: 0,
                },
              ]"
              :style="{ width: '200px' }"
              @change="onChange"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">订购列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10" justify="flex-end">
      <a-button type="primary" @click="onClick">导出全部数据</a-button>
    </a-flex>
    <a-tabs v-model:activeKey="activeKey" @change="handleTabChange">
      <a-tab-pane key="1" tab="不含兑换">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          rowKey="id"
          tableLayout="fixed"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, text }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="text == '订购' ? 'green' : 'red'">
                {{ text }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="2" tab="含兑换">
        <a-table
          :columns="columns2"
          :data-source="dataSource2"
          :loading="loading2"
          :pagination="pagination2"
          rowKey="id"
          tableLayout="fixed"
          @change="handleTableChange2"
        >
          <template #bodyCell="{ column, text }">
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="text == '订购' ? 'green' : 'red'">
                {{ text }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getDcoosMessages, getProducts } from '@/api/产品包'
  import VabIcon from '@/layout/vab-icon'
  import { exportExcel } from '@/utils/excel'

  const activeKey = ref('1')
  const route = useRoute()
  // 搜索表单
  const formState = reactive({
    type: undefined,
    status: undefined,
  })
  const onChange = () => {
    handleSearch()
  }
  // 产品包
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getProducts()
    options.value = data
  }
  handleOptions()

  const handleTabChange = () => {
    handleSearch()
  }
  const onClick = async () => {
    message.success('正在导出')
    let mock = []
    const is_point = activeKey.value == '1' ? 0 : 1
    const deno = 50
    for (let i = 1; i <= Math.ceil(total.value / deno); i++) {
      const { data } = await getDcoosMessages({
        page: i,
        pageSize: deno,
        is_point,
        type: formState.type,
        status: formState.status,
      })
      mock = [...mock, ...data.map((item)=>{
        return {
          ...item,
          status: item.status == 1 ? '订购' : '退订',
        }
      })]
    }
    message.success('导出完成')
    exportExcel(
      [
        '手机号码',
        '订购状态',
        activeKey.value == '1'
          ? '产品名称（不含兑换）'
          : '产品名称（兑换产品）',
        '订购开始时间',
        '订购到期时间',
        '发生时间',
      ],
      [
        'client_id',
        'status',
        'product_name',
        'start_time',
        'end_time',
        'created_at',
      ],
      mock,
      '订购数据'
    )
  }
  // 搜索
  const handleSearch = () => {
    if (activeKey.value === '1') {
      pagination.value.current = 1
      init()
    } else {
      pagination2.value.current = 1
      init2()
    }
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getDcoosMessages)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        status: item.status == 1 ? '订购' : '退订',
      }
    })
  })
  const columns = [
    {
      title: '手机号码',
      dataIndex: 'client_id',
    },
    {
      title: '订购状态',
      dataIndex: 'status',
    },
    {
      title: '产品名称（不含兑换）',
      dataIndex: 'product_name',
    },
    {
      title: '订购开始时间',
      dataIndex: 'start_time',
    },
    {
      title: '订购到期时间',
      dataIndex: 'end_time',
    },
    {
      title: '发生时间',
      dataIndex: 'created_at',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })
  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      is_point: 0,
      type: formState.type,
      status: formState.status,
    })
  }
  init()
  const {
    data: data2,
    run: run2,
    total: total2,
    loading: loading2,
    current: current2,
    pageSize: pageSize2,
  } = usePagination(getDcoosMessages)

  const dataSource2 = computed(() => {
    if (!data2.value) return []
    return data2.value.data.map((item) => {
      return {
        ...item,
        status: item.status == 1 ? '订购' : '退订',
      }
    })
  })
  const columns2 = [
    {
      title: '手机号码',
      dataIndex: 'client_id',
    },
    {
      title: '订购状态',
      dataIndex: 'status',
    },
    {
      title: '产品名称（兑换产品）',
      dataIndex: 'product_name',
    },
    {
      title: '订购开始时间',
      dataIndex: 'start_time',
    },
    {
      title: '订购到期时间',
      dataIndex: 'end_time',
    },
    {
      title: '发生时间',
      dataIndex: 'created_at',
    },
  ]
  const pagination2 = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current2.value,
      pageSize: pageSize2.value,
      total: total2.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })
  const handleTableChange2 = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init2()
  }
  const init2 = () => {
    run2({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      is_point: 1,
      type: formState.type,
      status: formState.status,
    })
  }

  init2()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
