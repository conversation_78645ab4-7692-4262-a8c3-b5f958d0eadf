<template>
  <a-modal
    v-model:open="open"
    title="新建学科"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        label="学科名称"
        name="name"
        :rules="[{ required: true, message: '学科名称不能为空' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入学科名称" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createLiteracySubjects } from '@/api/听写跟读/学科'

  const emits = defineEmits(['cancel', 'ok'])

  const formRef = ref()
  const formState = reactive({
    name: '',
  })

  const open = ref(false)
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createLiteracySubjects(formState)

    if (code == 200) {
      message.success('创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped></style>
