<template>
  <a-row justify="space-between">
    <a-col :span="5">
      <a-flex align="center" class="py-10" gap="10">
        <div><DefaultDivider /></div>
        <div class="font-bold">栏目列表</div>
      </a-flex>

      <a-form ref="formRef" layout="vertical" :model="formState">
        <a-form-item
          label="学段"
          name="literacy_grade_id"
          :rules="[{ required: true, message: `请输入书籍!` }]"
        >
          <a-select
            v-model:value="formState.literacy_grade_id"
            allow-clear
            :fieldNames="{
              label: 'name',
              value: 'id',
            }"
            :options="options"
            @change="onChange"
          />
        </a-form-item>
        <a-form-item
          label="书籍"
          name="literacy_book_id"
          :rules="[{ required: true, message: `请输入书籍!` }]"
        >
          <VabSelectFetch
            :disabled="!formState.literacy_grade_id"
            :formName="'literacy_book_id'"
            :formState="formState"
            :options="options2"
            style="width: 100%"
            @change="onChange2"
            @focus="handleFocus"
            @search="handleMainSearch"
          />
        </a-form-item>
      </a-form>
      <a-input-search v-model:value="searchValue" style="margin-bottom: 8px" />
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :autoExpandParent="autoExpandParent"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
        @expand="onExpand"
        @select="onSelect"
      >
        <template #title="{ id, name, index, children }">
          <a-dropdown :trigger="['contextmenu']">
            <span v-if="name.indexOf(searchValue) > -1">
              {{ index }} {{ name.substring(0, name.indexOf(searchValue)) }}
              <span style="color: #f50">{{ searchValue }}</span>
              {{
                name.substring(name.indexOf(searchValue) + searchValue.length)
              }}({{ children.length }})
            </span>
            <span v-else>{{ index }} {{ name }}({{ children.length }})</span>
            <template #overlay>
              <a-menu @click="({ key }) => onContextMenuClick(key, id)">
                <a-menu-item key="1">编辑</a-menu-item>
                <a-menu-item key="2">删除</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
        <template #icon="{ code }">
          <template v-if="['unit', 'chapter'].includes(code)">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-col>
    <a-col :span="18">
      <!-- <a-form>
        <a-flex gap="10">
          <a-form-item label="书籍名称">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入书籍名称快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form> -->
      <a-divider style="height: 3px; background-color: #eeeeee" />
      <a-flex align="center" class="py-10" gap="10">
        <DefaultDivider />
        <div class="font-bold">书籍英语跟读列表</div>
      </a-flex>
      <a-flex class="py-10" gap="10">
        <a-button
          :disabled="!formState.literacy_book_id"
          type="primary"
          @click="onClick"
        >
          添加书籍单元章节
        </a-button>
        <a-button
          :disabled="!literacy_book_unit_id"
          type="primary"
          @click="onClick2"
        >
          添加书籍英语跟读
        </a-button>
      </a-flex>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        rowKey="id"
        tableLayout="fixed"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="['name'].includes(column.dataIndex)">
            <a-tag color="blue">
              {{ text }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space :size="10">
              <a-button type="primary" @click="englishEdit(record.id)">
                编辑
              </a-button>
              <a-popconfirm
                cancel-text="否"
                ok-text="是"
                title="是否确定删除?"
                @confirm="englishDelete(record.id)"
              >
                <a-button danger type="primary">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-col>

    <CreateEnglishModal ref="createEnglishModalRef" @ok="handleSearch" />
    <CreateUnitModal ref="createUnitModalRef" @ok="getTreeData" />
  </a-row>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { computed, reactive, ref, watch } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getLiteracyBooks } from '@/api/听写跟读/书籍'
  import {
    deleteLiteracyBookUnits,
    getLiteracyBookUnits,
  } from '@/api/听写跟读/书籍单元章节'
  import {
    deleteLiteracyBookEnglishFollows,
    getLiteracyBookEnglishFollows,
  } from '@/api/听写跟读/书籍英语跟读'
  import { getLiteracyGrades } from '@/api/听写跟读/学段'
  import VabSelectFetch from '@/components/VabSelect/fetch'
  import { debounceTime, select_pageSize } from '@/config'
  import { modal } from '@/utils/modal'
  import { routerReplaceNoPagi } from '~/src/utils/routes'

  import CreateUnitModal from '../书籍单元章节新建'
  import CreateEnglishModal from './components/书籍英语跟读新建'
  const route = useRoute()

  const searchValue = ref('')
  const formRef = ref()
  const formState = reactive({
    name: '',
    literacy_grade_id: route.query?.literacy_grade_id
      ? parseInt(route.query?.literacy_grade_id)
      : undefined,
    literacy_book_id: route.query?.literacy_book_id
      ? parseInt(route.query?.literacy_book_id)
      : undefined,
    literacy_subject_id: 3,
  })

  // 学段
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracyGrades({
      parent_id: 0,
    })
    options.value = data
  }
  handleOptions()
  // 书籍
  const options2 = ref([])
  const handleFocus = () => handleMainSearch()
  const handleMainSearch = debounce(async (name) => {
    const { data } = await getLiteracyBooks({
      page: 1,
      pageSize: select_pageSize,
      literacy_grade_id: formState.literacy_grade_id,
      literacy_subject_id: formState.literacy_subject_id,
      name,
    })
    options2.value = data
  }, debounceTime)

  // 学段变动事件
  const onChange = async () => {
    formState.literacy_book_id = undefined
    options2.value = []
    onChange2()
  }
  // 书籍变动事件
  const onChange2 = async () => {
    treeData.value = []
    literacy_book_unit_id.value = undefined
    expandedKeys.value = []
    selectedKeys.value = []
    getTreeData()
  }

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  // 展开指定的树节点
  const expandedKeys = ref(
    route.query?.expandedKeys
      ? route.query?.expandedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  const selectedKeys = ref(
    route.query?.selectedKeys
      ? route.query?.selectedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  // treeNodes 数据
  const treeData = ref([])
  const getParentKey = (id, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some((item) => item.id === id)) {
          parentKey = node.id
        } else if (getParentKey(id, node.children)) {
          parentKey = getParentKey(id, node.children)
        }
      }
    }
    return parentKey
  }
  // 树-所有项放到同级
  const dataList = ref([])
  const generateList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i]
      dataList.value.push({
        name: node.name,
        id: node.id,
      })
      if (node.children) {
        generateList(node.children)
      }
    }
  }

  // 是否自动展开
  const autoExpandParent = ref(true)
  // 树-展开事件
  const onExpand = (keys) => {
    expandedKeys.value = keys
    autoExpandParent.value = false
  }
  // 监听搜索
  watch(searchValue, (value) => {
    generateList(treeData.value)
    const expanded = dataList.value
      .map((item) => {
        if (item.name.indexOf(value) > -1) {
          return getParentKey(item.id, treeData.value)
        }
        return null
      })
      .filter((item, i, self) => {
        return item && self.indexOf(item) === i
      })
    expandedKeys.value = expanded
    searchValue.value = value
    autoExpandParent.value = true
  })

  // 树-右键菜单
  const onContextMenuClick = (key, id) => {
    switch (key) {
      case '1':
        unitEdit(id)
        break
      case '2':
        modal(() => {
          unitDelete(id)
        })

        break
      default:
        break
    }
  }

  // 表格
  const { data, run, total, loading, current, pageSize } = usePagination(
    getLiteracyBookEnglishFollows
  )

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      literacy_book_id: formState.literacy_book_id,
      literacy_book_unit_id: literacy_book_unit_id.value,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplaceNoPagi({
      literacy_grade_id: formState.literacy_grade_id,
      literacy_book_id: formState.literacy_book_id,
      literacy_book_unit_id: literacy_book_unit_id.value,
      expandedKeys: expandedKeys.value.join(','),
      selectedKeys: selectedKeys.value.join(','),
    })
  }

  const literacy_book_unit_id = ref(
    route.query?.literacy_book_unit_id
      ? parseInt(route.query?.literacy_book_unit_id)
      : undefined
  )
  // 点击树节点触发
  const onSelect = async (item, { node: { dataRef } }) => {
    await formRef.value?.validate()
    console.log(item)
    if (['unit', 'chapter'].includes(dataRef.code)) return
    if (item.length == 0) return
    console.log(dataRef)
    if (!expandedKeys.value.includes(dataRef.id))
      expandedKeys.value.push(dataRef.id)
    selectedKeys.value = [dataRef.id]
    literacy_book_unit_id.value = dataRef.id
    pagination.value.current = 1
    init()
  }

  const getTreeData = async () => {
    await formRef.value?.validate()
    const { data } = await getLiteracyBookUnits({
      literacy_book_id: formState.literacy_book_id,
      parent_id: 0,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    treeData.value = data
  }

  const englishDelete = async (id) => {
    const { code } = await deleteLiteracyBookEnglishFollows(id)
    if (code == 200) {
      handleSearch()
      message.success('删除成功')
    }
  }

  // 书籍英语跟读新建
  const createEnglishModalRef = ref()
  const onClick2 = () => {
    createEnglishModalRef.value.formState.literacy_subject_id =
      formState.literacy_subject_id
    createEnglishModalRef.value.formState.literacy_grade_id =
      formState.literacy_grade_id
    createEnglishModalRef.value.formState.literacy_book_id =
      formState.literacy_book_id
    createEnglishModalRef.value.formState.literacy_book_unit_id =
      literacy_book_unit_id.value
    createEnglishModalRef.value?.showModal()
  }
  // 书籍英语跟读编辑
  const englishEdit = (id) => {
    createEnglishModalRef.value.setId(id)
    createEnglishModalRef.value?.showModal()
  }

  // 新建书籍
  const createUnitModalRef = ref()
  const onClick = () => {
    createUnitModalRef.value.formState.literacy_subject_id =
      formState.literacy_subject_id
    createUnitModalRef.value.formState.literacy_grade_id =
      formState.literacy_grade_id
    createUnitModalRef.value.formState.literacy_book_id =
      formState.literacy_book_id
    createUnitModalRef.value?.showModal()
  }
  // 编辑-弹窗
  const unitEdit = (id) => {
    createUnitModalRef.value.setId(id)
    createUnitModalRef.value?.showModal()
  }
  const unitDelete = async (id) => {
    const { code } = await deleteLiteracyBookUnits(id)
    if (code == 200) {
      getTreeData()
      message.success('删除成功')
    }
  }

  if (formState.literacy_book_id) {
    handleMainSearch()
    getTreeData()
    literacy_book_unit_id.value && init()
  }
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
