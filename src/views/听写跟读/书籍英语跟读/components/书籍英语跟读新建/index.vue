<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? `编辑英语跟读` : `新建英语跟读`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`字幕`"
        name="lyric"
        :rules="[{ required: true, message: `请输入字幕!` }]"
      >
        <a-textarea
          v-model:value="formState.lyric"
          :autosize="{ minRows: 4, maxRows: 10 }"
          :placeholder="`请输入字幕`"
        />
      </a-form-item>
      <a-form-item :label="`音频文件`">
        <a-upload
          v-model:file-list="fileList"
          accept=".wav"
          :before-upload="beforeUpload"
          :customRequest="customRequest"
          :max-count="1"
          @remove="fileRemove"
        >
          <a-button>
            <plus-outlined />
            上传
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item
        :label="`音频文件`"
        name="lyric_listen"
        :rules="[{ required: true, message: `请输入音频文件!` }]"
      >
        <a-flex gap="10">
          <a-input
            v-model:value="formState.lyric_listen"
            :disabled="true"
            :placeholder="`请输入音频文件`"
          />
          <a-button type="primary" @click="onAudioPlay">播放音频</a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍"
        name="literacy_book_id"
        :rules="[{ required: true, message: '绑定书籍不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_id'"
          :formState="formState"
          :options="options"
          @search="handleOptions"
        />
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍单元章节"
        name="literacy_book_unit_id"
        :rules="[{ required: true, message: '绑定书籍单元章节不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_unit_id'"
          :formState="formState"
          :options="options2"
          @search="handleOptions2"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getLiteracyBooks } from '@/api/听写跟读/书籍'
  import { getLiteracyBookUnits } from '@/api/听写跟读/书籍单元章节'
  import {
    createLiteracyBookEnglishFollows,
    editLiteracyBookEnglishFollows,
    getLiteracyBookEnglishFollowsDetail,
  } from '@/api/听写跟读/书籍英语跟读'
  import VabSelectFetch from '@/components/VabSelect/fetch'
  import { uploadToOSS } from '@/utils/ossUploader'

  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    lyric: '',
    lyric_listen: '',
    literacy_book_id: undefined,
    literacy_book_unit_id: undefined,
    literacy_grade_id: undefined,
    literacy_subject_id: undefined,
  })

  const options = ref([])
  const options2 = ref([])

  const handleOptions = async () => {
    const { data } = await getLiteracyBooks({
      literacy_grade_id: formState.literacy_grade_id,
      literacy_subject_id: formState.literacy_subject_id,
    })
    options.value = data
  }
  const handleOptions2 = async () => {
    const { data } = await getLiteracyBookUnits({
      literacy_book_id: formState.literacy_book_id,
    })
    options2.value = data
  }

  // 字幕
  const audio = ref()
  const fileList = ref([])
  // 上传前事件
  const beforeUpload = async (file) => {
    const isJpgOrPng = ['audio/wav'].includes(file.type)
    if (!isJpgOrPng) {
      message.error('上传文件格式错误')
    }
    return isJpgOrPng
  }
  // 删除事件
  const fileRemove = (file) => {
    const index = fileList.value.indexOf(file)
    const newFileList = fileList.value.slice()
    newFileList.splice(index, 1)
    fileList.value = newFileList
  }
  // 上传行为事件
  const customRequest = async (options) => {
    audio.value = new Audio(
      URL.createObjectURL(fileList.value[0].originFileObj)
    )
    audio.value.load()
    uploadToOSS(
      fileList.value,
      (result, file) => {
        formState.lyric_listen = result.name
        message.success('上传成功')
        options.onSuccess(result, file)
        console.log('上传成功:', result)
      },
      (error) => {
        console.error('上传失败:', error)
        options.onError('', error)
      }
    )
  }
  const onAudioPlay = () => {
    if (!formState.lyric_listen || !audio.value)
      return message.error(`音频文件为空`)
    try {
      audio.value.play()
    } catch (error) {
      console.error('播放音频时出错:', error)
      message.error(`播放音频时出错: ${error.message}`)
    }
  }

  // 弹窗
  const open = ref(false)
  // 取消
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    formState.literacy_book_id = undefined
    formState.literacy_book_unit_id = undefined
    fileRemove(fileList.value?.[0])
    audio.value.src = ''
    open.value = false
    emits('cancel')
  }
  // 显示
  const showModal = async () => {
    open.value = true
    handleOptions()
    handleOptions2()
    id.value && (await init())
  }
  // 提交
  const handleOk = async () => {
    await formRef.value?.validate()

    const { code } = id.value
      ? await editLiteracyBookEnglishFollows(formState, id.value)
      : await createLiteracyBookEnglishFollows(formState)

    if (code == 200) {
      emits('ok')
      message.success(id.value ? '编辑成功' : '创建成功')
    }
    handleCancel()
  }

  // 编辑初始化
  const init = async () => {
    const { data } = await getLiteracyBookEnglishFollowsDetail(id.value)
    audio.value = new Audio(data.lyric_listen_url)
    audio.value.load()
    Object.assign(formState, data)
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
