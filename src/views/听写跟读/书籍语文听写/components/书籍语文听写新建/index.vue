<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? `编辑${label}` : `新建${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`词汇文本`"
        name="word"
        :rules="[{ required: true, message: `请输入词汇的文本!` }]"
      >
        <a-input
          v-model:value="formState.word"
          :placeholder="`请输入词汇文本`"
        />
      </a-form-item>
      <a-form-item
        :label="`词组文本`"
        name="word_group"
        :rules="[{ required: true, message: `请输入词组的文本!` }]"
      >
        <a-input
          v-model:value="formState.word_group"
          :placeholder="`请输入词组文本`"
        />
      </a-form-item>
      <a-form-item
        :label="`播报文本`"
        name="listen_text"
        :rules="[{ required: true, message: `请输入播报的文本!` }]"
      >
        <a-flex align="center" gap="10" justify="center">
          <a-textarea
            v-model:value="formState.listen_text"
            :autosize="{ minRows: 4, maxRows: 10 }"
            :placeholder="`请输入播报文本`"
          />
          <a-button type="primary" @click="onClick">获取拼音/音频文件</a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        :label="`拼音文本`"
        name="pinyin"
        :rules="[{ required: true, message: `请输入拼音的文本!` }]"
      >
        <a-input
          v-model:value="formState.pinyin"
          :placeholder="`请输入拼音文本`"
        />
      </a-form-item>
      <a-form-item
        :label="`音频文件`"
        name="listen"
        :rules="[{ required: true, message: `请输入音频文件!` }]"
      >
        <a-flex gap="10">
          <a-input
            v-model:value="formState.listen"
            :disabled="true"
            :placeholder="`请输入音频文件`"
          />
          <a-button type="primary" @click="onAudioPlay">播放音频</a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍"
        name="literacy_book_id"
        :rules="[{ required: true, message: '绑定书籍不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_id'"
          :formState="formState"
          :options="options"
          @search="handleOptions"
        />
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍单元章节"
        name="literacy_book_unit_id"
        :rules="[{ required: true, message: '绑定书籍单元章节不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_unit_id'"
          :formState="formState"
          :options="options2"
          @search="handleOptions2"
        />
      </a-form-item>
      <a-form-item label="类型">
        <a-select
          v-model:value="formState.type"
          allow-clear
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="[
            { name: '字词表', id: 0 },
            { name: '词语表', id: 1 },
            { name: '写字表', id: 2 },
            { name: '识字表', id: 3 },
          ]"
          style="width: 140px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getLiteracyBooks } from '@/api/听写跟读/书籍'
  import { getLiteracyBookUnits } from '@/api/听写跟读/书籍单元章节'
  import {
    createLiteracyBookChineseWords,
    editLiteracyBookChineseWords,
    getLiteracyBookChineseWordsDetail,
  } from '@/api/听写跟读/书籍语文听写'
  import { getLiteracyBookChineseWordsPinyin } from '@/api/听写跟读/听写字词拼音获取'
  import { createNlsSpeechSynthesis } from '@/api/听写跟读/语音合成'
  import VabSelectFetch from '@/components/VabSelect/fetch'
  import { uploadToOSS } from '@/utils/ossUploader'
  const label = '语文听写'
  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    word: '',
    word_group: '',
    listen_text: '',
    pinyin: '',
    literacy_book_id: undefined,
    literacy_book_unit_id: undefined,
    listen: '',
    type: 0,
    literacy_grade_id: undefined,
    literacy_subject_id: undefined,
  })

  const options = ref([])
  const options2 = ref([])

  const handleOptions = async () => {
    const { data } = await getLiteracyBooks({
      literacy_grade_id: formState.literacy_grade_id,
      literacy_subject_id: formState.literacy_subject_id,
    })
    options.value = data
  }
  const handleOptions2 = async () => {
    const { data } = await getLiteracyBookUnits({
      literacy_book_id: formState.literacy_book_id,
    })
    options2.value = data
  }

  const audio = ref()
  const getPinyinData = async () => {
    const {
      data: { pinyin },
    } = await getLiteracyBookChineseWordsPinyin({
      word: formState.word,
      word_group: formState.word_group,
    })
    formState.pinyin = pinyin.join(' ')
  }
  const getListenData = async () => {
    const audioBlob = await createNlsSpeechSynthesis({
      text: formState.listen_text.replace(/\n| {2}/g, ''),
      template_id: 1,
    })
    audio.value = new Audio(URL.createObjectURL(audioBlob))
    audio.value.load()
    const file = new File([audioBlob], 'audio.wav', { type: 'audio/wav' })

    uploadToOSS(
      [{ originFileObj: file }],
      (result) => {
        formState.listen = result.name
        message.success('上传成功')
        console.log('上传成功:', result)
      },
      (error) => {
        console.error('上传失败:', error)
      }
    )
  }

  const validateAndFetchData = async () => {
    if (!formState.word || !formState.word_group || !formState.listen_text) {
      return message.error(`词汇文本和词组文本和播报文本不能为空`)
    }
    await Promise.all([getPinyinData(), getListenData()])
  }

  const onClick = validateAndFetchData

  const onAudioPlay = () => {
    if (!formState.listen || !audio.value) return message.error(`音频文件为空`)
    try {
      audio.value.play()
    } catch (error) {
      console.error('播放音频时出错:', error)
      message.error(`播放音频时出错: ${error.message}`)
    }
  }

  const open = ref(false)
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    formState.literacy_book_id = undefined
    formState.literacy_book_unit_id = undefined
    formState.type = 0
    audio.value.src = ''
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    open.value = true
    handleOptions()
    handleOptions2()
    id.value && (await init())
  }
  const handleOk = async () => {
    await formRef.value?.validate()

    const { code } = id.value
      ? await editLiteracyBookChineseWords(
          {
            ...formState,
            listen_text: formState.listen_text.replace(/\n| {2}/g, ''),
          },
          id.value
        )
      : await createLiteracyBookChineseWords({
          ...formState,
          listen_text: formState.listen_text.replace(/\n| {2}/g, ''),
        })

    if (code == 200) {
      emits('ok')
      message.success(id.value ? '编辑成功' : '创建成功')
    }
    handleCancel()
  }

  const init = async () => {
    const { data } = await getLiteracyBookChineseWordsDetail(id.value)
    audio.value = new Audio(data.listen_url)
    audio.value.load()
    Object.assign(formState, data)
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
