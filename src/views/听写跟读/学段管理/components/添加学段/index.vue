<template>
  <a-modal
    v-model:open="open"
    :title="`新建${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `请输入${label}的名称!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label}名称`"
        />
      </a-form-item>
      <a-form-item
        :label="label2"
        name="parent_id"
        :rules="[{ required: true, message: `请输入${label2}!` }]"
      >
        <a-cascader
          v-model:value="formState.parent_id"
          change-on-select
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          :placeholder="`请输入${label2}`"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createLiteracyGrades, getLiteracyGrades } from '@/api/听写跟读/学段'

  const label = '学段'
  const label2 = '绑定父级学段'
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    name: '',
    code: '',
    parent_id: [0],
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracyGrades({
      parent_id: 0,
    })
    options.value = data.map((item) => {
      return {
        name: item.name,
        id: item.id,
        children: item.children.map((x) => {
          return {
            name: x.name,
            id: x.id,
          }
        }),
      }
    })
    options.value.unshift({
      name: '顶级学段',
      id: 0,
    })
  }

  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    handleOptions()
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createLiteracyGrades({
      name: formState.name,
      code:
        formState.parent_id[0] == 0
          ? 'grade'
          : formState.parent_id.length == 1
          ? 'level'
          : 'semester',
      parent_id: formState.parent_id[formState.parent_id.length - 1],
    })
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
