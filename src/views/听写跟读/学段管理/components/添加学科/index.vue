<template>
  <a-modal
    v-model:open="open"
    :title="`添加${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item :label="`关联${label}`" name="literacy_subjects">
        <a-select
          v-model:value="formState.literacy_subjects"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          mode="tags"
          :options="options"
          :placeholder="`请选择${label}`"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import {
    editLiteracyGrades,
    getLiteracyGradesDetail,
  } from '@/api/听写跟读/学段'
  import { getLiteracySubjects } from '@/api/听写跟读/学科'
  import { uniqueArrays } from '@/utils'

  const label = '学科'
  const emits = defineEmits(['cancel', 'ok'])
  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    key: undefined,
    name: '',
    literacy_subjects: [],
    copySubjects: [],
  })
  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    await fetch()
    open.value = true
  }
  // 加载数据
  const fetch = async () => {
    const { data } = await getLiteracyGradesDetail(id.value)
    formState.literacy_subjects = data.literacy_subjects.map((item) => item.id)
    formState.copySubjects = formState.literacy_subjects
    formState.name = data.name
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
      formState.literacy_subjects,
      formState.copySubjects
    )
    const { code } = await editLiteracyGrades(
      {
        name: formState.name,
        literacy_subjects: {
          sync: uniqueToFirstArray.map((item, i) => {
            return {
              id: item,
              orderby: uniqueToFirstArray.length - 1 - i,
            }
          }),
          del: uniqueToSecondArray.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracySubjects()
    options.value = data
  }
  handleOptions()

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
