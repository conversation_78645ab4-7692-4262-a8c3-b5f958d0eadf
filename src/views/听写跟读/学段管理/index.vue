<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ShoppingOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">学段列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加学段</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name'].includes(column.dataIndex)">
          <div>
            <a-input
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
            />
            <template v-else>
              <a-tag color="orange">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-if="['literacy_subjects'].includes(column.dataIndex)">
          <a-tag v-for="(item, i) in text" :key="i" color="orange">
            {{ item.name }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div>
            <span v-if="editableData[record.id]">
              <a-space :size="10">
                <a-button type="primary" @click="save(record.id)">
                  保存
                </a-button>
                <a-popconfirm title="确定取消?" @confirm="cancel(record.id)">
                  <a-button>取消</a-button>
                </a-popconfirm>
              </a-space>
            </span>
            <span v-else>
              <a-space :size="10">
                <template v-if="record.code === 'grade'">
                  <a-button type="primary" @click="showModal(record.id)">
                    新增学科
                  </a-button>
                  <a-button type="primary" @click="edit(record.id)">
                    编辑
                  </a-button>
                </template>
                <a-popconfirm
                  cancel-text="否"
                  ok-text="是"
                  title="是否确定删除?"
                  @confirm="onDelete(record.id)"
                >
                  <a-button danger type="primary">删除</a-button>
                </a-popconfirm>
              </a-space>
            </span>
          </div>
        </template>
      </template>
    </a-table>
    <CreateGradesModal ref="createGradesModalRef" @ok="handleOk" />
    <CreateSubjectsModal ref="createSubjectsModalRef" @ok="handleOk" />
  </div>
</template>

<script setup>
  import { ShoppingOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import {
    deleteLiteracyGrades,
    editLiteracyGrades,
    getLiteracyGrades,
  } from '@/api/听写跟读/学段'
  import { setEmptyChildrenToUndefined } from '@/utils/index'
  import { routerReplace } from '~/src/utils/routes'

  import CreateGradesModal from './components/添加学段'
  import CreateSubjectsModal from './components/添加学科'

  const route = useRoute()
  // 搜索框label
  const label = '学段名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
  })

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getLiteracyGrades)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        children: setEmptyChildrenToUndefined(item.children),
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '关联学科',
      dataIndex: 'literacy_subjects',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      parent_id: 0,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
    })
  }

  // 编辑表格
  const editableData = reactive({})
  // 编辑表格
  const edit = (id) => {
    editableData[id] = cloneDeep(
      dataSource.value.filter((item) => id === item.id)[0]
    )
  }
  // 保存表格
  const save = async (id) => {
    console.log(id)
    const { code } = await editLiteracyGrades(
      {
        name: editableData[id].name,
      },
      id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
      delete editableData[id]
    }
  }
  // 取消编辑
  const cancel = (id) => {
    delete editableData[id]
  }
  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteLiteracyGrades(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  // 添加学段
  const createGradesModalRef = ref()
  const onClick = () => {
    createGradesModalRef.value?.showModal()
  }

  // 添加学科
  const createSubjectsModalRef = ref()
  const showModal = async (id) => {
    createSubjectsModalRef.value.setId(id)
    createSubjectsModalRef.value?.showModal()
  }
  const handleOk = () => {
    init()
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
