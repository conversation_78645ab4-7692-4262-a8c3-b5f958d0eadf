<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学科">
            <a-select
              v-model:value="formState.literacy_subject_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">书籍列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加书籍</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template
          v-if="
            [
              'name',
              'literacy_grade_name',
              'literacy_grade_level_name',
              'literacy_grade_semester_name',
              'literacy_subject_name',
            ].includes(column.dataIndex)
          "
        >
          <a-tag color="blue">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'cover_url'">
          <a-image :src="record.cover_url" style="width: 100px" />
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>
            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <CreateModal ref="createModalRef" @ok="init" />
    <EditModal ref="editModalRef" @ok="init" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { deleteLiteracyBooks, getLiteracyBooks } from '@/api/听写跟读/书籍'
  import { getLiteracySubjects } from '@/api/听写跟读/学科'
  import { routerReplace } from '~/src/utils/routes'

  import CreateModal from './components/新建书籍'
  import EditModal from './components/编辑书籍'

  const route = useRoute()
  // 搜索框label
  const label = '书籍名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    literacy_subject_id: route.query?.literacy_subject_id
      ? parseInt(route.query?.literacy_subject_id)
      : undefined,
  })

  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracySubjects()
    options.value = data
  }
  handleOptions()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getLiteracyBooks)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        literacy_grade_name: item.literacy_grade.name,
        literacy_grade_level_name: item.literacy_grade_level.name,
        literacy_grade_semester_name: item.literacy_grade_semester.name,
        literacy_subject_name: item.literacy_subject.name,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '学段',
      dataIndex: 'literacy_grade_name',
    },
    {
      title: '年级',
      dataIndex: 'literacy_grade_level_name',
    },
    {
      title: '学期',
      dataIndex: 'literacy_grade_semester_name',
    },
    {
      title: '学科',
      dataIndex: 'literacy_subject_name',
    },
    {
      title: '排序权重',
      dataIndex: 'orderby',
    },
    {
      title: '预览',
      dataIndex: 'cover_url',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      literacy_subject_id: formState.literacy_subject_id,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
      literacy_subject_id: formState.literacy_subject_id,
    })
  }

  const onDelete = async (id) => {
    const { code } = await deleteLiteracyBooks(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  // 编辑
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 新建书籍
  const createModalRef = ref()
  const onClick = () => {
    createModalRef.value?.showModal()
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
