<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="新建书籍"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item
        label="书籍名称"
        name="name"
        :rules="[{ required: true, message: '书籍名称不能为空' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入书籍名称" />
      </a-form-item>
      <a-form-item
        label="封面"
        name="cover"
        :rules="[{ required: true, message: '封面不能为空' }]"
      >
        <Audio ref="AudioRef" @setImageAddress="handleParamsCover_url_id" />
      </a-form-item>
      <a-form-item
        :label="label"
        name="parent_id"
        :rules="[{ required: true, message: `请输入${label}!` }]"
      >
        <a-cascader
          v-model:value="formState.parent_id"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          :placeholder="`请输入${label}`"
        />
      </a-form-item>
      <a-form-item
        label="学科"
        name="literacy_subject_id"
        :rules="[{ required: true, message: '学科不能为空' }]"
      >
        <a-select
          v-model:value="formState.literacy_subject_id"
          allow-clear
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options2"
          style="width: 140px"
        />
      </a-form-item>
      <a-form-item
        label="排序权重"
        name="orderby"
        :rules="[
          { required: true, type: 'number', message: '排序权重不能为空' },
        ]"
      >
        <a-input-number v-model:value="formState.orderby" :min="0" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createLiteracyBooks } from '@/api/听写跟读/书籍'
  import { getLiteracyGrades } from '@/api/听写跟读/学段'
  import { getLiteracySubjects } from '@/api/听写跟读/学科'
  import Audio from '@/views/audio'
  const label = '学段'
  const emits = defineEmits(['cancel', 'ok'])
  const AudioRef = ref('')
  const formRef = ref()
  const formState = reactive({
    name: '',
    cover: '',
    parent_id: undefined,
    literacy_grade_id: undefined,
    literacy_grade_level_id: undefined,
    literacy_grade_semester_id: undefined,
    literacy_subject_id: undefined,
    orderby: 0,
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracyGrades({
      parent_id: 0,
    })
    options.value = data
  }

  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getLiteracySubjects({
      parent_id: 0,
    })
    options2.value = data
  }

  const open = ref(false)
  const showModal = () => {
    handleOptions()
    handleOptions2()
    open.value = true
  }
  const handleOk = async () => {
    if (AudioRef.value?.loading) {
      return message.warning('封面正在上传中')
    }
    await formRef.value?.validate()
    if (formState.parent_id?.length != 3)
      return message.warning('学段未关联到学期')
    const { code } = await createLiteracyBooks({
      ...formState,
      literacy_grade_id: formState.parent_id[0],
      literacy_grade_level_id: formState.parent_id[1],
      literacy_grade_semester_id: formState.parent_id[2],
    })

    if (code == 200) {
      message.success('创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  // 设置图片上传成功回调
  const handleParamsCover_url_id = (value) => {
    formState.cover = value
  }

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped></style>
