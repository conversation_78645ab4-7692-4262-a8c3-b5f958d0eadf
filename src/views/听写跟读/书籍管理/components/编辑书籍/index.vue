<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="编辑封面"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 3 }" :model="formState">
      <a-form-item
        label="标题"
        name="name"
        :rules="[{ required: true, message: '请输入标题!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item
        label="封面"
        name="cover"
        :rules="[{ required: true, message: '封面不能为空' }]"
      >
        <Audio ref="AudioRef" @setImageAddress="handleParamsCover_url_id" />
      </a-form-item>
      <a-form-item
        label="排序权重"
        name="orderby"
        :rules="[
          { required: true, type: 'number', message: '排序权重不能为空' },
        ]"
      >
        <a-input-number v-model:value="formState.orderby" :min="0" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import {
    editLiteracyBooks,
    getLiteracyBooksDetail,
  } from '@/api/听写跟读/书籍'
  import Audio from '@/views/audio'

  const emits = defineEmits(['cancel', 'ok'])
  const AudioRef = ref('')
  const id = ref()
  const formRef = ref()
  const formState = reactive({
    name: '',
    cover: '',
    orderby: 0,
  })
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    open.value = true
    await fetch()
  }
  // 关闭弹窗
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  // 加载数据
  const fetch = async () => {
    const { data } = await getLiteracyBooksDetail(id.value)
    formState.name = data.name
    formState.cover = data.cover
    getImageAddress(data)
  }
  // 提交
  const handleOk = async () => {
    if (AudioRef.value?.loading) {
      return message.warning('封面正在上传中')
    }
    await formRef.value?.validate()
    const { code } = await editLiteracyBooks(formState, id.value)
    if (code == 200) {
      emits('ok')
      message.success('修改成功')
    }
    handleCancel()
  }

  // 设置图片初始化回调
  const getImageAddress = async (data) => {
    AudioRef.value?.fileList.push({
      url: data.cover_url,
    })
  }
  // 设置图片上传成功回调
  const handleParamsCover_url_id = (value) => {
    formState.cover = value
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
