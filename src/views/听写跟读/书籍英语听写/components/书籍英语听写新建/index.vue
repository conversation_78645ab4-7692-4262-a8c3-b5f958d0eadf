<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? `编辑英语听写` : `新建英语听写`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`中文文本`"
        name="sub_word"
        :rules="[{ required: true, message: `请输入中文文本!` }]"
      >
        <a-input
          v-model:value="formState.sub_word"
          :placeholder="`请输入中文文本`"
        />
      </a-form-item>
      <a-form-item
        :label="`中文文本(阿里云代码复制区)`"
        name="sub_word_listen_text"
        :rules="[
          { required: true, message: `请输入中文文本(阿里云代码复制区)!` },
        ]"
      >
        <a-flex gap="10" vertical>
          <a-textarea
            v-model:value="formState.sub_word_listen_text"
            :autosize="{ minRows: 4, maxRows: 10 }"
            :placeholder="`请输入中文文本(阿里云代码复制区)`"
          />
          <a-button type="primary" @click="onClick">获取拼音/音频文件</a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        :label="`音频文件`"
        name="sub_word_listen"
        :rules="[{ required: true, message: `请输入音频文件!` }]"
      >
        <a-flex gap="10">
          <a-input
            v-model:value="formState.sub_word_listen"
            :disabled="true"
            :placeholder="`请输入音频文件`"
          />
          <a-button type="primary" @click="onAudioPlay">播放音频</a-button>
        </a-flex>
      </a-form-item>

      <a-form-item
        :label="`英文文本`"
        name="word"
        :rules="[{ required: true, message: `请输入英文文本!` }]"
      >
        <a-input
          v-model:value="formState.word"
          :placeholder="`请输入英文文本`"
        />
      </a-form-item>
      <a-form-item
        :label="`英文文本(阿里云代码复制区)`"
        name="listen_text"
        :rules="[
          { required: true, message: `请输入英文文本(阿里云代码复制区)!` },
        ]"
      >
        <a-flex gap="10" vertical>
          <a-textarea
            v-model:value="formState.listen_text"
            :autosize="{ minRows: 4, maxRows: 10 }"
            :placeholder="`请输入英文文本(阿里云代码复制区)`"
          />
          <a-button type="primary" @click="onClick2">
            获取拼音/音频文件
          </a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        :label="`英文含义`"
        name="meaning"
        :rules="[{ required: true, message: `请输入英文含义!` }]"
      >
        <a-input
          v-model:value="formState.meaning"
          :placeholder="`请输入英文含义`"
        />
      </a-form-item>
      <a-form-item
        :label="`音频文件`"
        name="listen"
        :rules="[{ required: true, message: `请输入音频文件!` }]"
      >
        <a-flex gap="10">
          <a-input
            v-model:value="formState.listen"
            :disabled="true"
            :placeholder="`请输入音频文件`"
          />
          <a-button type="primary" @click="onAudioPlay2">播放音频</a-button>
        </a-flex>
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍"
        name="literacy_book_id"
        :rules="[{ required: true, message: '绑定书籍不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_id'"
          :formState="formState"
          :options="options"
          @search="handleOptions"
        />
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍单元章节"
        name="literacy_book_unit_id"
        :rules="[{ required: true, message: '绑定书籍单元章节不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_unit_id'"
          :formState="formState"
          :options="options2"
          @search="handleOptions2"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getLiteracyBooks } from '@/api/听写跟读/书籍'
  import { getLiteracyBookUnits } from '@/api/听写跟读/书籍单元章节'
  import {
    createLiteracyBookEnglishWords,
    editLiteracyBookEnglishWords,
    getLiteracyBookEnglishWordsDetail,
  } from '@/api/听写跟读/书籍英语听写'
  import { createNlsSpeechSynthesis } from '@/api/听写跟读/语音合成'
  import VabSelectFetch from '@/components/VabSelect/fetch'
  import { uploadToOSS } from '@/utils/ossUploader'

  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    word: '',
    listen: '',
    listen_text: '',
    sub_word: '',
    sub_word_listen: '',
    sub_word_listen_text: '',
    meaning: '',
    literacy_book_id: undefined,
    literacy_book_unit_id: undefined,

    literacy_grade_id: undefined,
    literacy_subject_id: undefined,
  })

  const options = ref([])
  const options2 = ref([])

  const handleOptions = async () => {
    const { data } = await getLiteracyBooks({
      literacy_grade_id: formState.literacy_grade_id,
      literacy_subject_id: formState.literacy_subject_id,
    })
    options.value = data
  }
  const handleOptions2 = async () => {
    const { data } = await getLiteracyBookUnits({
      literacy_book_id: formState.literacy_book_id,
    })
    options2.value = data
  }

  // 中文文本
  const audio = ref()
  const getListenData = async () => {
    const audioBlob = await createNlsSpeechSynthesis({
      text: formState.sub_word_listen_text,
      template_id: 1,
    })
    audio.value = new Audio(URL.createObjectURL(audioBlob))
    audio.value.load()
    const file = new File([audioBlob], 'audio.wav', { type: 'audio/wav' })
    uploadToOSS(
      [{ originFileObj: file }],
      (result) => {
        formState.sub_word_listen = result.name
        message.success('上传成功')
        console.log('上传成功:', result)
      },
      (error) => {
        console.error('上传失败:', error)
      }
    )
  }

  const validateAndFetchData = async () => {
    if (!formState.sub_word_listen_text) {
      return message.error(`中文文本(阿里云代码复制区)`)
    }
    getListenData()
  }
  const onClick = validateAndFetchData
  const onAudioPlay = () => {
    if (!formState.sub_word_listen || !audio.value)
      return message.error(`音频文件为空`)
    try {
      audio.value.play()
    } catch (error) {
      console.error('播放音频时出错:', error)
      message.error(`播放音频时出错: ${error.message}`)
    }
  }

  // 英文文本
  const audio2 = ref()
  const getListenData2 = async () => {
    const audioBlob = await createNlsSpeechSynthesis({
      text: formState.listen_text,
      template_id: 2,
    })
    audio2.value = new Audio(URL.createObjectURL(audioBlob))
    audio2.value.load()
    const file = new File([audioBlob], 'audio.wav', { type: 'audio/wav' })
    uploadToOSS(
      [{ originFileObj: file }],
      (result) => {
        formState.listen = result.name
        message.success('上传成功')
        console.log('上传成功:', result)
      },
      (error) => {
        console.error('上传失败:', error)
      }
    )
  }
  const validateAndFetchData2 = async () => {
    if (!formState.listen_text) {
      return message.error(`英文文本(阿里云代码复制区)为空`)
    }
    getListenData2()
  }
  const onClick2 = validateAndFetchData2
  const onAudioPlay2 = () => {
    if (!formState.listen || !audio2.value) return message.error(`音频文件为空`)
    try {
      audio2.value.play()
    } catch (error) {
      console.error('播放音频时出错:', error)
      message.error(`播放音频时出错: ${error.message}`)
    }
  }

  // 弹窗
  const open = ref(false)
  // 取消
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    formState.literacy_book_id = undefined
    formState.literacy_book_unit_id = undefined
    audio.value.src = ''
    audio2.value.src = ''
    open.value = false
    emits('cancel')
  }
  // 显示
  const showModal = async () => {
    open.value = true
    if (!id.value) {
      handleOptions()
      handleOptions2()
    }
    id.value && (await init())
  }
  // 提交
  const handleOk = async () => {
    await formRef.value?.validate()

    const { code } = id.value
      ? await editLiteracyBookEnglishWords(formState, id.value)
      : await createLiteracyBookEnglishWords(formState)

    if (code == 200) {
      emits('ok')
      message.success(id.value ? '编辑成功' : '创建成功')
    }
    handleCancel()
  }

  // 编辑初始化
  const init = async () => {
    const { data } = await getLiteracyBookEnglishWordsDetail(id.value)
    audio.value = new Audio(data.sub_word_listen_url)
    audio.value.load()
    audio2.value = new Audio(data.listen_url)
    audio2.value.load()
    Object.assign(formState, data)
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
