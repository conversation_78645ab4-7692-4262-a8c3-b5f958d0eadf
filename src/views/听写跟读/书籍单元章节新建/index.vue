<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? `编辑${label}` : `新建${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `请输入${label}的名称!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label}名称`"
        />
      </a-form-item>
      <a-form-item
        v-if="!id"
        :label="label2"
        name="parent_id"
        :rules="[{ required: true, message: `请输入${label2}!` }]"
      >
        <a-cascader
          v-model:value="formState.parent_id"
          change-on-select
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          :placeholder="`请输入${label2}`"
        />
      </a-form-item>
      <a-form-item
        v-if="!id"
        label="绑定书籍"
        name="literacy_book_id"
        :rules="[{ required: true, message: '绑定书籍不能为空' }]"
      >
        <VabSelectFetch
          :disabled="true"
          :formName="'literacy_book_id'"
          :formState="formState"
          :options="options2"
          @search="handleOptions2"
        />
      </a-form-item>
      <a-form-item
        label="排序权重"
        name="orderby"
        :rules="[
          { required: true, type: 'number', message: '排序权重不能为空' },
        ]"
      >
        <a-input-number v-model:value="formState.orderby" :min="0" />
      </a-form-item>

      <a-form-item label="自定义前缀序号" name="index">
        <a-input v-model:value="formState.index" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getLiteracyBooks } from '@/api/听写跟读/书籍'
  import {
    createLiteracyBookUnits,
    editLiteracyBookUnits,
    getLiteracyBookUnits,
    getLiteracyBookUnitsDetail,
  } from '@/api/听写跟读/书籍单元章节'
  import VabSelectFetch from '@/components/VabSelect/fetch'
  const label = '书籍单元章节'
  const label2 = '绑定父级书籍单元章节'
  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    name: '',
    code: '',
    parent_id: [0],
    literacy_book_id: undefined,
    orderby: 0,
    index: '',
    literacy_grade_id: undefined,
    literacy_subject_id: undefined,
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getLiteracyBookUnits({
      literacy_book_id: formState.literacy_book_id,
      parent_id: 0,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    options.value = data.map((item) => {
      return {
        name: item.name,
        id: item.id,
        children: item.children.map((x) => {
          return {
            name: x.name,
            id: x.id,
          }
        }),
      }
    })
    options.value.unshift({
      name: '顶级书籍单元章节',
      id: 0,
    })
  }

  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getLiteracyBooks({
      literacy_grade_id: formState.literacy_grade_id,
      literacy_subject_id: formState.literacy_subject_id,
    })
    options2.value = data
  }

  const open = ref(false)
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    formState.parent_id = [0]
    formState.literacy_book_id = undefined
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    open.value = true
    handleOptions()
    handleOptions2()
    id.value && (await init())
  }
  const handleOk = async () => {
    await formRef.value?.validate()

    const { code } = id.value
      ? await editLiteracyBookUnits(formState, id.value)
      : await createLiteracyBookUnits({
          ...formState,
          code:
            formState.parent_id[0] == 0
              ? 'unit'
              : formState.parent_id.length == 1
              ? 'chapter'
              : 'section',
          parent_id: formState.parent_id[formState.parent_id.length - 1],
        })
    if (code == 200) {
      emits('ok')
      message.success(id.value ? '编辑成功' : '创建成功')
    }
    handleCancel()
  }

  const init = async () => {
    const { data } = await getLiteracyBookUnitsDetail(id.value)
    Object.assign(formState, data)
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
