<template>
 
    <a-list :data-source="data" item-layout="horizontal" size="small">
      <template #renderItem="{ item, index }">
        <a-list-item>
          <a-list-item-meta>
            <template #title>
              <a-flex align="middle" justify="space-between">
                <div class="black65">
                  {{ item.title }}
                </div>
                <div class="black65">{{ item.value }}</div>
              </a-flex>
            </template>
            <template #avatar>
              <div :class="index < 3 ? 'index black' : 'index grey'">
                {{ index + 1 }}
              </div>
            </template>
          </a-list-item-meta>
        </a-list-item>
      </template>
    </a-list>

</template>

<script setup>
  import { toRefs } from 'vue'

  const props = defineProps(['data'])

  const { data } = toRefs(props)
</script>

<style lang="scss" scoped>
  .index {
    width: 20px;
    height: 20px;
    border-radius: 30px;
    text-align: center;
  }
  .black {
    background-color: #314659;
    color: #fff;
  }
  .black65 {
    color: rgba(0, 0, 0, 0.65);
  }
  .grey {
    color: rgba(0, 0, 0, 0.65);
    background-color: #f5f5f5;
  }
</style>
