<template>
  <div style="padding: 0px 0px 20px 20px">
    <a-tabs
      v-model:activeKey="activeKey"
      :animated="true"
      :destroyInactiveTabPane="true"
      :tabBarGutter="30"
      @change="onTabChange"
    >
      <a-tab-pane
        v-for="(item, i) in 2"
        :key="`${i + 1}`"
        :tab="i + 1 == 1 ? '用户数' : '访问次数'"
      >
        <a-row :gutter="gutter">
          <a-col :span="18">
            <h4>游览趋势</h4>
            <G2PlotColumn
              ref="columnRef"
              :data="columnData"
              @click="onPlotClick"
            />
          </a-col>
          <a-col :span="6" style="border-left: 1px solid #e8e8e8">
            <h4>页面排行</h4>
            <ListRef :data="data" />
          </a-col>
        </a-row>
      </a-tab-pane>
      <template #rightExtra>
        <a-flex gap="10">
          <a-button @click="onClick('columnData')">导出柱状图</a-button>
          <a-button :disabled="data.length == 0" @click="onClick('data')">
            导出排名
          </a-button>
          <a-button @click="onClick('hour')">昨日</a-button>
          <a-button @click="onClick('day')">本周</a-button>
          <a-button @click="onClick('week')">本月</a-button>
          <a-button @click="onClick('month')">全年</a-button>
          <a-range-picker
            v-model:value="time"
            :disabled-date="disabledDate"
            :presets="rangePresets"
            @calendarChange="onCalendarChange"
            @change="onTimeChange"
          />
        </a-flex>
      </template>
    </a-tabs>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs'
  import moment from 'moment'
  import { ref, toRefs } from 'vue'

  import { getPageLog, getWebLogBarChart } from '@/api/分析'
  import { dateFormat } from '@/config'
  import {
    // greaterThanToday,
    newTime,
    // todayToLastnDay,
    yesterday,
  } from '@/utils/disabledDate'
  import { exportExcel } from '@/utils/excel'
  import G2PlotColumn from '@/views/G2Plot/G2PlotColumn'

  import ListRef from './components/list.vue'

  const gutter = 32
  const props = defineProps(['data'])
  const { data } = toRefs(props)
  const emits = defineEmits(['sendData'])

  const type = ref('day')
  const activeKey = ref('1')
  // 面板切换回调
  const onTabChange = (activeKey) => {
    getWebLogBarChartData()
    console.log(activeKey)
  }
  // 月发行趋势
  const columnData = ref([])
  // 获取图标数据
  const getWebLogBarChartData = async () => {
    const { data } = await getWebLogBarChart({
      time_type: type.value,
      date:
        type.value == 'day'
          ? dayjs(time.value?.[1]).format(dateFormat)
          : type.value == 'hour'
          ? yesterday.format(dateFormat)
          : newTime.format(dateFormat),
      date_range:
        type.value == 'day'
          ? time.value
            ? {
                start_date: dayjs(time.value?.[0]).format(dateFormat),
                end_date: dayjs(time.value?.[1]).format(dateFormat),
              }
            : undefined
          : undefined,
    })
    columnData.value = data.map((item) => {
      return {
        title: item.name + (type.value == 'hour' ? '时' : ''),
        value: activeKey.value == '1' ? item.uv : item.pv,
        ...item,
      }
    })
    getPageLogData(data[0].page_rank_search.value)
  }

  const listValue = ref('')
  // 获取list数据
  const getPageLogData = async (value) => {
    listValue.value = value
    const { data } = await getPageLog({
      time_type: type.value,
      value,
    })
    emits(
      'sendData',
      data
        .map((item) => {
          return {
            title: item.name,
            type: item.name,
            value: activeKey.value == '1' ? item.uv : item.pv,
            ...item,
          }
        })
        .sort(
          (a, b) =>
            b[activeKey.value == '1' ? 'uv' : 'pv'] -
            a[activeKey.value == '1' ? 'uv' : 'pv']
        )
    )
  }
  // 单击图表区域事件
  const onPlotClick = (data) => {
    getPageLogData(data.value.page_rank_search.value)
  }

  // 时间选择框
  const time = ref([
    dayjs(moment().subtract(10, 'days').format(dateFormat)),
    newTime,
  ])
  const rangePresets = [
    {
      label: 'Last 30 Days',
      value: [dayjs().add(-30, 'd'), dayjs()],
    },
    {
      label: 'Last 90 Days',
      value: [dayjs().add(-90, 'd'), dayjs()],
    },
  ]
  // 日期选择回调
  const onTimeChange = () => {
    type.value = 'day'
    getWebLogBarChartData()
  }
  const dates = ref()
  const onCalendarChange = (val) => {
    dates.value = val
  }
  // 不可选择日期范围
  const disabledDate = (current) => {
    if (!dates.value || dates.value.length === 0) {
      return false
    }
    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 90
    const tooEarly = dates.value[1] && dates.value[1].diff(current, 'days') > 90
    return tooEarly || tooLate
  }

  const getDateRange = () => {
    let title = ''
    switch (type.value) {
      case 'hour':
        title = yesterday.format(dateFormat)
        break
      case 'day':
        title = `${dayjs(time.value?.[0]).format(dateFormat)}-${dayjs(
          time.value?.[1]
        ).format(dateFormat)}`
        break
      case 'week':
        title = yesterday.format('YYYY-MM')
        break
      case 'month':
        title = yesterday.format('YYYY')
        break
      default:
        break
    }
    return title
  }
  const getListValueTitle = () => {
    let title = ''
    switch (type.value) {
      case 'hour':
        title = listValue.value
        break
      case 'day':
        title = listValue.value
        break
      case 'week':
        title = `${listValue.value}-${dayjs(
          moment(listValue.value).add(6, 'days').format(dateFormat)
        ).format('YYYY-MM-DD')}`
        break
      case 'month':
        title = dayjs(listValue.value).format('YYYY-MM')
        break
      default:
        break
    }
    return title
  }

  const onClick = (value) => {
    if (value == 'columnData') {
      console.log(columnData.value)
      exportExcel(
        ['时间', 'pv', 'uv'],
        ['name', 'pv', 'uv'],
        columnData.value,
        '访问数据',
        getDateRange()
      )
      return
    }
    if (value == 'data') {
      exportExcel(
        ['时间', 'pv', 'uv'],
        ['name', 'pv', 'uv'],
        data.value,
        '访问排名',
        getListValueTitle()
      )
      return
    }
    type.value = value
    if (type.value == 'day')
      time.value = [
        dayjs(moment().subtract(7, 'days').format(dateFormat)),
        newTime,
      ]
    getWebLogBarChartData()
  }

  const init = () => {
    getWebLogBarChartData()
    // getPageLogData
  }

  init()
</script>

<style lang="scss" scoped>
  h4 {
    margin-bottom: 16px;
    margin-top: 24px;
  }
</style>
