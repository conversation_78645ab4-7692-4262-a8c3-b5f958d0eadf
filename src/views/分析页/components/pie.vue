<template>
  <div>
    <G2PlotPie ref="columnRef" :data="data" />
  </div>
</template>

<script setup>
  import { onMounted, toRefs } from 'vue'

  import G2PlotPie from '@/views/G2Plot/G2PlotPie'

  const props = defineProps(['data'])
  const { data } = toRefs(props)
  // 月发行趋势
  // const data = ref([
  //   { type: '1号页面', value: 10 },
  //   { type: '2号页面', value: 20 },
  //   { type: '3号页面', value: 30 },
  //   { type: '4号页面', value: 5 },
  // ])
  const init = () => {
    console.log(1)
  }

  onMounted(() => {
    init()
  })
</script>

<style lang="scss" scoped>
  .index {
    width: 20px;
    height: 20px;
    border-radius: 30px;
    text-align: center;
  }
  .black {
    background-color: #314659;
    color: #fff;
  }
  .black65 {
    color: rgba(0, 0, 0, 0.65);
  }
  .grey {
    color: rgba(0, 0, 0, 0.65);
    background-color: #f5f5f5;
  }
</style>
