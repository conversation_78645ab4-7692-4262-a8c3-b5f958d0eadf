<template>
  <div style="background: #f5f5f5; padding: 20px">
    <a-row align="middle" class="mb-20" :gutter="20" justify="space-between">
      <template v-for="(item, i) in dataSource" :key="i">
        <a-col class="item" :span="6">
          <a-card>
            <a-flex vertical>
              <div>
                <a-flex align="center" justify="space-between">
                  <span class="grey">{{ item.title }}</span>
                  <a-tooltip>
                    <template #title>
                      <span>{{ item.tooltip }}</span>
                    </template>
                    <ExclamationCircleOutlined class="grey" />
                  </a-tooltip>
                </a-flex>
              </div>
              <count-to
                class="font-size28"
                :decimals="state.decimals"
                :duration="state.duration"
                :endVal="i != 3 ? item.total : item.continue_user_count"
                :startVal="state.startVal"
                :suffix="state.suffix"
              />
              <div style="width: 100%; height: 60px">
                <template v-if="[0].includes(i)">
                  <G2PlotArea
                    ref="areaRef"
                    :data="areaData"
                    :option="{ height: 60, autoFit: true }"
                  />
                </template>
                <template v-else-if="[1].includes(i)">
                  <G2PlotArea
                    ref="areaRef2"
                    :data="areaData2"
                    :option="{ height: 60, autoFit: true }"
                  />
                </template>
              </div>
              <div style="height: 60px">
                <template v-if="[0, 1].includes(i)">
                  <a-flex gap="50">
                    <template v-for="(x, j) in 2" :key="j">
                      <a-statistic
                        :precision="2"
                        suffix="%"
                        :title="j == 0 ? '日同比' : '周同比'"
                        :value="j == 0 ? item.day_rate : item.week_rate"
                        :value-style="{
                          color:
                            j == 0
                              ? item.day_rate < 0
                                ? '#cf1322'
                                : '#3f8600'
                              : item.week_rate < 0
                              ? '#cf1322'
                              : '#3f8600',
                        }"
                      >
                        <template #prefix>
                          <template v-if="j == 0">
                            <template v-if="item.day_rate < 0">
                              <ArrowDownOutlined />
                            </template>
                            <template v-else>
                              <ArrowUpOutlined />
                            </template>
                          </template>
                          <template v-else>
                            <template v-if="item.week_rate < 0">
                              <ArrowDownOutlined />
                            </template>
                            <template v-else>
                              <ArrowUpOutlined />
                            </template>
                          </template>
                        </template>
                      </a-statistic>
                    </template>
                  </a-flex>
                </template>
                <template v-else-if="i == 2"></template>
                <template v-else-if="i == 3"></template>
              </div>
              <template v-if="[0, 1].includes(i)">
                <div class="title">
                  {{ item.DayTitle }}:
                  <span>{{ item.yesterday }}</span>
                </div>
              </template>
              <template v-else>
                <div class="title">
                  {{ item.DayTitle }}
                  <span>{{ item.success_rate }}</span>
                </div>
              </template>
            </a-flex>
          </a-card>
        </a-col>
      </template>
    </a-row>
    <a-card class="mb-20">
      <div style="overflow: hidden">
        <ColumnArea :data="data" @sendData="onSendData" />
      </div>
    </a-card>
    <a-card>
      <template #title>
        <h4 style="padding: 16px 0">页面占比</h4>
      </template>
      <G2Pie :data="data" />
    </a-card>
  </div>
</template>

<script setup>
  import {
    ArrowDownOutlined,
    ArrowUpOutlined,
    ExclamationCircleOutlined,
  } from '@ant-design/icons-vue'
  import dayjs from 'dayjs'
  import moment from 'moment'
  import { reactive, ref } from 'vue'
  import { CountTo } from 'vue3-count-to'

  import { getSummary, getWebLogBarChart } from '@/api/分析'
  import { dateFormat } from '@/config'
  import { newTime } from '@/utils/disabledDate'
  import G2PlotArea from '@/views/G2Plot/G2PlotArea'

  import ColumnArea from './components/column.vue'
  import G2Pie from './components/pie.vue'

  // 数字自增长
  const state = reactive({
    show: true,
    form: {
      startVal: 0, // 起始值
      endVal: 0, // 结束值
      duration: 1000, // 持续时间
      autoplay: true,
      decimals: 0, // 小数点
      prefix: '', // 前缀
      suffix: '', // 后缀
      separator: ',', // 分隔符
      useEasing: true, // 是否使用缓动功能
    },
  })

  const dataSource = ref([
    {
      name: 'uv',
      title: '访问用户数',
      tooltip: '指标说明',
      total: 0,
      DayTitle: '昨日访问用户数',
      day_rate: 0,
      yesterday: 0,
    },
    {
      name: 'pv',
      title: '访问次数',
      tooltip: '指标说明',
      total: 0,
      DayTitle: '昨日访问访问次数',
      day_rate: 0,
      yesterday: 0,
    },
    {
      name: 'order',
      title: '当日新增用户数',
      tooltip: '指标说明',
      total: 0,
    },
    {
      name: 'order',
      title: '累计在网用户数',
      tooltip: '指标说明',
      continue_user_count: 0,
    },
  ])

  // 总览
  const getSummaryData = async () => {
    const { data } = await getSummary()
    console.log(data)
    dataSource.value.forEach((item) => {
      Object.assign(item, data[item.name])
    })
  }

  const data = ref([])
  const onSendData = (value) => {
    data.value = value
    console.log(data.value)
  }
  // 折线图
  const areaRef = ref()
  const areaData = ref([])
  const areaData2 = ref([])
  const getLineData = async () => {
    const { data } = await getWebLogBarChart({
      time_type: 'day',
      date: newTime.format(dateFormat),
      date_range: {
        start_date: dayjs(
          moment().subtract(10, 'days').format(dateFormat)
        ).format(dateFormat),
        end_date: newTime.format(dateFormat),
      },
    })
    areaData.value = data.map((item) => {
      return {
        title: item.name,
        value: item.uv,
        ...item,
      }
    })
    areaData2.value = data.map((item) => {
      return {
        title: item.name,
        value: item.pv,
        ...item,
      }
    })
  }

  const init = () => {
    getSummaryData()
    getLineData()
  }

  init()
</script>

<style lang="scss" scoped>
  .grey {
    color: #00000073;
  }
  .title {
    border-top: 1px solid #e8e8e8;
    padding-top: 9px;
    margin-top: 8px;
    height: 33.3px;
  }
</style>
