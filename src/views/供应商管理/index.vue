<template>
  <div>
    <a-flex align="center" justify="space-between">
      <a-flex align="center" gap="10">
        <div><UserOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form class="flex_form">
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">供应商列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="showDrawer">添加供应商</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name'].includes(column.dataIndex)">
          <div>
            <a-input
              v-if="editableData[record.id]"
              v-model:value="editableData[record.id][column.dataIndex]"
            />
            <template v-else>
              <a-tag color="purple">
                {{ text }}
              </a-tag>
            </template>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div>
            <span v-if="editableData[record.id]">
              <a-space :size="10">
                <a-button type="primary" @click="save(record.id)">
                  保存
                </a-button>
                <a-popconfirm title="确定取消?" @confirm="cancel(record.id)">
                  <a-button>取消</a-button>
                </a-popconfirm>
              </a-space>
            </span>
            <span v-else>
              <a-space :size="10">
                <a-button type="primary" @click="edit(record.id)">
                  编辑
                </a-button>

                <a-popconfirm
                  cancel-text="否"
                  ok-text="是"
                  title="是否确定删除?"
                  @confirm="onDelete(record.id)"
                >
                  <a-button danger type="primary">删除</a-button>
                </a-popconfirm>
              </a-space>
            </span>
          </div>
        </template>
      </template>
    </a-table>
    <a-drawer
      :destroyOnClose="true"
      :open="open"
      title="新建供应商"
      @close="onClose"
    >
      <a-form ref="formRef" :model="form">
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入供应商的名称!' }]"
        >
          <a-input v-model:value="form.name" placeholder="请输入供应商名称" />
        </a-form-item>
      </a-form>
      <template #extra>
        <a-space>
          <a-button @click="onClose">取消</a-button>
          <a-button type="primary" @click="onSubmit">提交</a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>

<script setup>
  import { UserOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { cloneDeep } from 'lodash-es'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { createCps, deleteCps, editCps, getCps } from '@/api/CP'
  import { routerReplace } from '~/src/utils/routes'

  const route = useRoute()
  // 搜索框label
  const label = '供应商名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
  })
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  // 抽屉
  const open = ref(false)
  // 取消抽屉
  const onClose = () => {
    open.value = false
  }
  // 打开抽屉
  const showDrawer = () => {
    open.value = true
  }

  const formRef = ref()
  const form = reactive({
    name: '',
  })
  const onSubmit = async () => {
    await formRef.value?.validate()
    const { code } = await createCps({
      name: form.name,
    })
    if (code == 200) {
      init()
      message.success('创建成功')
    }
    formRef.value?.resetFields()
  }

  const { data, run, total, loading, current, pageSize } = usePagination(getCps)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
    })
  }

  const editableData = reactive({})
  const edit = (id) => {
    editableData[id] = cloneDeep(
      dataSource.value.filter((item) => id === item.id)[0]
    )
  }
  const save = async (id) => {
    console.log(id)
    const { code } = await editCps(
      {
        name: editableData[id].name,
      },
      id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
      delete editableData[id]
    }
  }
  const cancel = (id) => {
    delete editableData[id]
  }
  const onDelete = async (id) => {
    const { code } = await deleteCps(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
