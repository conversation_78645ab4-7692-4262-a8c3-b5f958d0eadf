<template>
  <a-flex align="center" gap="10" justify="center" vertical>
    <a-button class="button" type="primary" @click="onclick">发布</a-button>
  </a-flex>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { toRefs } from 'vue'

  import { editPages } from '@/api/页面'
  import { modal } from '@/utils/modal'

  const props = defineProps(['id'])
  const { id } = toRefs(props)
  const onclick = () => {
    modal(async () => {
      const { code } = await editPages(
        {
          comment: 1,
        },
        id.value
      )
      if (code == 200) {
        message.success('发布成功')
      }
    })
  }
</script>

<style lang="scss" scoped>
  .button {
    border-radius: 35px;
    width: 75vw;
    position: fixed;
    bottom: 0px;
  }
</style>
