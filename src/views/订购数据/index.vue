<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><vab-icon :icon="route.meta.icon" /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item label="供应商">
            <a-select
              v-model:value="formState.product_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              :style="{ width: '200px' }"
              @change="onChange"
            />
          </a-form-item>
          <a-form-item label="时间">
            <a-range-picker v-model:value="formState.date" @change="onChange" />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">订购列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10" justify="flex-end">
      <a-button type="primary" @click="onClick">导出全部数据</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'is_charge'">
          <a-tag :color="text == 1 ? 'red' : 'green'">
            {{ text == 1 ? '付费' : '免费' }}
          </a-tag>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getProducts } from '@/api/产品包'
  import { getUserProductLogs } from '@/api/分析'
  import VabIcon from '@/layout/vab-icon'
  import { newTime } from '@/utils/disabledDate'
  import { exportExcel } from '@/utils/excel'

  const route = useRoute()
  const dateFormat = 'YYYY-MM-DD'
  // 搜索表单
  const formState = reactive({
    product_id: undefined,
    date: [newTime, newTime],
  })
  const onChange = () => {
    handleSearch()
  }

  // 产品包
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getProducts()
    options.value = data
  }
  handleOptions()

  const onClick = async () => {
    message.success('正在导出')
    let mock = []
    const deno = 50
    for (let i = 1; i <= Math.ceil(total.value / deno); i++) {
      const { data } = await getUserProductLogs({
        page: i,
        pageSize: deno,
        product_id: formState.product_id,
        date_range: formState.date
          ? [
              dayjs(formState.date?.[0]).format(dateFormat),
              dayjs(formState.date?.[1]).format(dateFormat),
            ]
          : [
              dayjs(newTime).format(dateFormat),
              dayjs(newTime).format(dateFormat),
            ],
      })
      mock = [...mock, ...data]
    }
    message.success('导出完成')
    exportExcel(
      [
        '产品名称',
        '时间',
        '日新增',
        '日退订',
        '月新增',
        '月退订',
        '年在网',
        '年退订',
        '是否付费',
      ],
      [
        'name',
        'date',
        'day_order',
        'day_unsubscribe',
        'month_order',
        'month_unsubscribe',
        'year_continue_user_count',
        'year_unsubscribe',
        'is_charge',
      ],
      mock,
      '订购数据',
      formState.date
        ? [
            dayjs(formState.date?.[0]).format(dateFormat),
            dayjs(formState.date?.[1]).format(dateFormat),
          ].join('-')
        : [
            dayjs(newTime).format(dateFormat),
            dayjs(newTime).format(dateFormat),
          ].join('-')
    )
  }
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getUserProductLogs)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '产品名称',
      dataIndex: 'name',
    },
    {
      title: '时间',
      dataIndex: 'date',
    },
    {
      title: '日新增',
      dataIndex: 'day_order',
    },
    {
      title: '日退订',
      dataIndex: 'day_unsubscribe',
    },
    {
      title: '月新增',
      dataIndex: 'month_order',
    },
    {
      title: '月退订',
      dataIndex: 'month_unsubscribe',
    },
    {
      title: '年在网',
      dataIndex: 'year_continue_user_count',
    },
    {
      title: '年退订',
      dataIndex: 'year_unsubscribe',
    },
    {
      title: '是否付费',
      dataIndex: 'is_charge',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      product_id: formState.product_id,
      date_range: formState.date
        ? [
            dayjs(formState.date?.[0]).format(dateFormat),
            dayjs(formState.date?.[1]).format(dateFormat),
          ]
        : [
            dayjs(newTime).format(dateFormat),
            dayjs(newTime).format(dateFormat),
          ],
    })
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
