<template>
  <a-row justify="space-between">
    <a-col :span="6">
      <a-flex align="center" class="py-10" gap="10">
        <div><DefaultDivider /></div>
        <div class="font-bold">栏目列表</div>
      </a-flex>
      <a-tree
        :key="treeKey"
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :height="730"
        :load-data="onLoadData"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
        @select="onSelect"
      >
        <template
          #title="{
            key: treeKey,
            title,
            type,
            id,
            parent_id,
            albums_count,
            videos_count,
            name,
          }"
        >
          <a-dropdown :trigger="['contextmenu']">
            <span>
              {{ title }}
              <span>
                {{
                  type == 2 || parent_id == 0
                    ? `(专辑:${albums_count})${
                        parent_id == 0 ? `(视频:${videos_count})` : ''
                      }`
                    : ''
                }}
              </span>
            </span>
            <template #overlay>
              <a-menu
                @click="
                  ({ key: menuKey }) =>
                    onContextMenuClick(treeKey, menuKey, id, name)
                "
              >
                <template v-if="type == 1">
                  <a-menu-item key="1">创建目录</a-menu-item>
                  <a-menu-item key="2">创建专辑包(能绑定专辑)</a-menu-item>
                </template>
                <template v-else>
                  <a-menu-item key="3">绑定专辑</a-menu-item>
                  <a-menu-item key="5">免费策略</a-menu-item>
                </template>
                <a-menu-item v-if="parent_id != 0" key="4">
                  删除栏目
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
        <!-- <template #switcherIcon="{ switcherCls }">
          <down-outlined :class="switcherCls" />
        </template> -->
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-col>
    <a-col :span="17">
      <a-flex align="center" class="py-10" gap="10">
        <DefaultDivider />
        <div class="font-bold">专辑列表</div>
      </a-flex>
      <a-flex class="py-10" gap="10">
        <a-popconfirm
          cancel-text="否"
          ok-text="是"
          title="是否确定删除?"
          @confirm="onAllDelete"
        >
          <a-button
            :disabled="!hasSelected"
            :loading="state.loading"
            type="primary"
          >
            批量删除
          </a-button>
        </a-popconfirm>

        <a-flex v-if="hasSelected" align="center" gap="5">
          <span>已选中</span>
          <a-flex wrap="wrap">
            <a-tag
              v-for="(item, i) in state.selectedRows"
              :key="i"
              closable
              @close.prevent="onAlbumsClose(item.id)"
            >
              {{ `${item.id}-${item.title}` }}
            </a-tag>
          </a-flex>
        </a-flex>
      </a-flex>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
        }"
        tableLayout="fixed"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'videos_count'">
            <a
              @click="
                pushWithQuery('ContentManage', {
                  id: record.id,
                  type: record.type,
                })
              "
            >
              {{ text }}
            </a>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </a-col>
    <CreateColumn ref="createColumnRef" @cancel="handleCancel" @ok="handleOk" />
    <EditColumn ref="editColumnRef" @cancel="handleOk" />
    <FreeStrategies ref="freeStrategiesRef" />
  </a-row>
</template>

<script setup>
  import {
    // DownOutlined,
    FolderOpenOutlined,
    TableOutlined,
  } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getAlbums } from '@/api/专辑'
  import {
    deleteColumns,
    editColumns,
    getColumns,
    getColumnsDetail,
  } from '@/api/栏目'
  import { secondsToMinutes } from '@/utils/index'
  import { modal } from '@/utils/modal'
  import { pushWithQuery, routerReplace } from '@/utils/routes'

  import EditColumn from './components/修改栏目'
  import FreeStrategies from './components/免费策略'
  import CreateColumn from './components/创建栏目'

  const route = useRoute()
  const treeKey = ref(1)
  // 展开指定的树节点
  const expandedKeys = ref(
    route.query?.expandedKeys
      ? route.query?.expandedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  //设置选中的树节点
  const selectedKeys = ref(
    route.query?.selectedKeys
      ? route.query?.selectedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  // treeNodes 数据
  const treeData = ref([])

  const onLoadData = async (treeNode) => {
    console.log(treeNode)
    if (!treeNode?.id) return
    const { data } = await getColumnsDetail(treeNode.id)
    console.log(data)
    treeNode.dataRef.children = data.children.map((item) => {
      return {
        title: item.name,
        key: item.id,
        isLeaf: item.type == 2 || item?.children?.length == 0 ? true : false,
        ...item,
      }
    })

    treeData.value = [...treeData.value]
  }

  // 弹窗
  const createColumnRef = ref('')
  // 树-右键菜单
  const onContextMenuClick = (treeKey, menuKey, id, name) => {
    console.log(`treeKey: ${treeKey}, menuKey: ${menuKey}`)

    switch (menuKey) {
      case '1':
      case '2':
        createColumnRef.value.formState.parent_id = id
        createColumnRef.value.formState.type = Number(menuKey)
        createColumnRef.value?.showModal()
        break
      case '3':
        bindAlbum(id, name)
        break
      case '4':
        handleDeleteColumns(id)
        break
      case '5':
        handleAddFreeStrategies(id, name)
        break
      default:
        break
    }
  }

  // 绑定专辑
  const editColumnRef = ref('')
  // 绑定专辑
  const bindAlbum = (value, name) => {
    editColumnRef.value.setId(value)
    editColumnRef.value.formState.name = name
    editColumnRef.value?.showModal()
  }
  const handleDeleteColumns = async (value) => {
    modal(async () => {
      const { code, data } = await deleteColumns(value)
      if (code == 200) {
        message.success(data)
        expandedKeys.value.filter((item) => item.key == value)
      }
      await handleOk()
    })
  }

  // 免费策略
  const freeStrategiesRef = ref('')
  const handleAddFreeStrategies = (value, name) => {
    freeStrategiesRef.value.setId(value)
    freeStrategiesRef.value.setName(name)
    freeStrategiesRef.value?.showModal()
  }

  // 创建栏目-成功
  const handleOk = async () => {
    await init()
    // 强制重新渲染tree
    treeKey.value += 1
    // 有表格加载id则加载
    related_column_id.value && fetch()
  }
  // 创建栏目-取消
  const handleCancel = () => {
    createColumnRef.value.formState.parent_id = undefined
    createColumnRef.value.formState.type = 1
  }

  // 删除专辑
  const onDelete = async (item) => {
    const { code } = await editColumns(
      {
        albums: {
          del: [
            {
              id: item.id,
            },
          ],
        },
      },
      related_column_id.value
    )
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('删除成功')
    }
  }

  // 批量删除
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
    loading: false,
  })
  const hasSelected = computed(() => state.selectedRowKeys.length > 0)
  const onAllDelete = async () => {
    state.loading = true
    const { code } = await editColumns(
      {
        albums: {
          del: state.selectedRowKeys.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      related_column_id.value
    )
    state.loading = false
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('删除成功')
    }
  }
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const onAlbumsClose = (value) => {
    state.selectedRowKeys = state.selectedRowKeys.filter((item) => {
      return item !== value
    })
    state.selectedRows = state.selectedRows.filter((item) => {
      return item.id !== value
    })
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getAlbums)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        key: item.id,
        duration: secondsToMinutes(item.videos_duration_count),
        ...item,
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: '专辑名称',
      dataIndex: 'title',
      width: 250,
    },
    {
      title: '视频时长(分)',
      dataIndex: 'duration',
    },
    {
      title: '视频数量',
      dataIndex: 'videos_count',
    },
    {
      title: '编辑',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    fetch()
  }
  const fetch = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      related_column_id: related_column_id.value,
    })

    routerReplace(pagination.value, {
      related_column_id: related_column_id.value,
      expandedKeys: expandedKeys.value.join(','),
      selectedKeys: selectedKeys.value.join(','),
    })
  }
  const related_column_id = ref(route.query?.related_column_id)
  related_column_id.value && fetch()
  // 点击树节点触发
  const onSelect = (item, { node: { dataRef } }) => {
    if (item.length == 0) return
    console.log(dataRef)
    // 是否是专辑包
    switch (dataRef.type) {
      case 1:
        if (!expandedKeys.value.includes(dataRef.key))
          expandedKeys.value.push(dataRef.key)
        break
      case 2:
        pagination.value.current = 1
        related_column_id.value = dataRef.id
        selectedKeys.value = [dataRef.id]
        state.selectedRowKeys = []
        fetch()
        break
      default:
        break
    }
  }

  const init = async () => {
    const { data } = await getColumns()
    treeData.value = data.map((item) => {
      return {
        title: item.name,
        key: item.id,
        children: [],
        ...item,
      }
    })
  }
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
