<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="`'${name}'免费策略`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        label="策略"
        name="is_free"
        :rules="[{ required: true, message: '策略不能为空!' }]"
      >
        <a-radio-group
          v-model:value="formState.is_free"
          :options="[
            {
              label: '全部免费',
              value: 1,
            },
            {
              label: '部分免费',
              value: 0,
            },
          ]"
        />
      </a-form-item>
      <a-form-item
        :hidden="formState.is_free == 1"
        label="前"
        name="free_video_num"
        :rules="[{ required: true, message: '不能为空!' }]"
      >
        <a-input-number v-model:value="formState.free_video_num" :min="0" />
        免费
      </a-form-item>
    </a-form>
    <template #footer>
      <a-flex gap="20" justify="center">
        <a-button type="primary" @click="handleOk">保存</a-button>
        <a-button
          danger
          :disabled="is_create"
          type="primary"
          @click="handleClear"
        >
          清空免费策略
        </a-button>
      </a-flex>
    </template>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import {
    createColumnVideoStrategies,
    deleteColumnVideoStrategies,
    editColumnVideoStrategies,
    getColumnVideoStrategiesDetail,
  } from '@/api/栏目视频策略'

  const emits = defineEmits(['cancel', 'ok'])

  // 栏目id
  const id = ref()
  // title
  const name = ref()
  //  是否是创建
  const is_create = ref(true)
  const formRef = ref('')
  const formState = reactive({
    is_free: 0,
    free_video_num: 0,
  })
  const setId = (value) => {
    id.value = value
  }
  const setName = (value) => {
    name.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    formState.is_free = 0
    formState.free_video_num = 0
    is_create.value = true
    await init()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  // 绑定专辑弹窗-确定
  const handleOk = async () => {
    await formRef.value?.validate()
    if (is_create.value) {
      const { code } = await createColumnVideoStrategies({
        column_id: id.value,
        ...formState,
      })
      if (code == 200) {
        message.success('创建成功')
      }
    } else {
      const { code } = await editColumnVideoStrategies(formState, id.value)
      if (code == 200) {
        message.success('编辑成功')
      }
    }

    handleCancel()
  }
  const handleClear = async () => {
    const { code } = await deleteColumnVideoStrategies(id.value)
    if (code == 200) {
      message.success('删除成功')
    }
    handleCancel()
  }

  const init = async () => {
    const { data } = await getColumnVideoStrategiesDetail(id.value)
    if (!Array.isArray(data)) {
      formState.is_free = data.is_free
      formState.free_video_num = data.free_video_num
      is_create.value = false
    } else {
      is_create.value = true
    }
  }

  defineExpose({
    showModal,
    formState,
    setId,
    setName,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
