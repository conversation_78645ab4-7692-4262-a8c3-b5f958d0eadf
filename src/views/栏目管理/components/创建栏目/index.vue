<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="创建栏目"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        label="栏目名称"
        name="name"
        :rules="[{ required: true, message: '栏目名称不能为空!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item
        label="创建类型"
        :rules="[{ required: true, message: '创建类型不能为空!' }]"
      >
        <a-select
          v-model:value="formState.type"
          :options="options"
          style="width: 200px"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createColumns } from '@/api/栏目'

  const emits = defineEmits(['cancel', 'ok'])

  const formRef = ref()
  const formState = reactive({
    name: '',
    parent_id: undefined,
    type: 1,
  })

  const open = ref(false)
  const showModal = () => {
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createColumns(formState)

    if (code == 200) {
      message.success('创建成功')
      emits('ok')
    }
    handleCancel()
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  const options = [
    {
      label: '目录',
      value: 1,
    },
    {
      label: '专辑包(能绑定专辑)',
      value: 2,
    },
  ]

  defineExpose({
    showModal,
    formState,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
