<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :footer="null"
    title="编辑栏目"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        label="栏目名称"
        name="name"
        :rules="[{ required: true, message: '栏目名称不能为空!' }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item label="专辑列表">
        <template v-for="item in formState.albumsList" :key="item">
          <a-tag closable @close="onAlbumsClose(item)">
            {{ item }}
          </a-tag>
        </template>
      </a-form-item>

      <a-form-item label="绑定专辑">
        <a-button type="primary" @click="handleAlbumClick">绑定</a-button>
      </a-form-item>
    </a-form>
    <useModal ref="modalRef" @ok="handleOk" />
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getAlbums } from '@/api/专辑'
  import { editColumns } from '@/api/栏目'
  import { getSplitTxt } from '@/utils/index'
  import useModal from '@/views/绑定专辑'

  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const formRef = ref('')
  const formState = reactive({
    name: '',
    albumsList: [],
  })
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    await getAlbumsData()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  const modalRef = ref()
  // 绑定专辑弹窗
  const handleAlbumClick = () => {
    modalRef.value.setColumnId(id.value)
    modalRef.value?.showDrawer()
  }
  // 绑定专辑弹窗-确定
  const handleOk = async (albums) => {
    const { code } = await editColumns(
      {
        name: formState.name,
        albums: {
          sync: albums.value.map((item) => ({
            id: getSplitTxt(item, 0),
          })),
        },
      },
      id.value
    )
    if (code == 200) {
      message.success('编辑成功')
    }
    await getAlbumsData()
  }

  const getAlbumsData = async () => {
    const { data } = await getAlbums({
      related_column_id: id.value,
    })
    formState.albumsList = data.map((item) => `${item.id}-${item.title}`)
  }
  const onAlbumsClose = async (item) => {
    console.log(item)
    const { code } = await editColumns(
      {
        name: formState.name,
        albums: {
          del: [
            {
              id: getSplitTxt(item, 0),
            },
          ],
        },
      },
      id.value
    )
    if (code == 200) {
      message.success('删除成功')
    }
    await getAlbumsData()
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
