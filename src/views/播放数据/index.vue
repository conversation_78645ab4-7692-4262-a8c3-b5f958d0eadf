<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ShoppingOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.video_id"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="供应商">
            <a-select
              v-model:value="formState.cp_id"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'id',
              }"
              :options="options"
              :style="style"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="时间">
            <a-date-picker
              v-model:value="formState.date"
              :allowClear="false"
              @change="onChange"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">播放列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10" justify="flex-end">
      <a-range-picker v-model:value="time" />
      <a-button type="primary" @click="onRangeClick">指定时间范围导出</a-button>
      <a-button type="primary" @click="onClick">导出全部数据</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'cp_name'">
          <a-tag color="purple">
            {{ text }}
          </a-tag>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { ShoppingOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getCps } from '@/api/CP'
  import { getPlayLogs, getPlayLogsDownload } from '@/api/分析'
  import { newTime } from '@/utils/disabledDate'
  import { exportExcel } from '@/utils/excel'
  import { routerReplace } from '~/src/utils/routes'

  const style = { width: '140px' }
  const route = useRoute()
  const dateFormat = 'YYYY-MM-DD'
  // 搜索框label
  const label = '视频id'
  // 搜索表单
  const formState = reactive({
    video_id: route.query?.video_id,
    cp_id: route.query?.cp_id ? parseInt(route.query?.cp_id) : undefined,
    date: route.query?.date
      ? dayjs(route.query?.date)
      : dayjs().subtract(1, 'day'),
  })
  const onChange = () => {
    handleSearch()
  }
  // 供应商
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getCps()
    options.value = data
  }
  handleOptions()

  const onClick = async () => {
    message.success('正在导出')
    let mock = []
    const deno = 50
    for (let i = 1; i <= Math.ceil(total.value / deno); i++) {
      const { data } = await getPlayLogs({
        page: i,
        pageSize: deno,
        video_id: formState.video_id,
        cp_id: formState.cp_id,
        date: formState.date.format(dateFormat),
      })
      mock = [...mock, ...data]
    }
    message.success('导出完成')
    exportExcel(
      [
        '视频id',
        '视频名称',
        '供应商',
        '访问用户数（个）',
        '播放总次数（次）',
        '人均播放时长（秒）',
        '累计播放时长（秒）',
        '人均播放次数（次）',
      ],
      [
        'video_id',
        'video_name',
        'cp_name',
        'uv',
        'pv',
        'user_time',
        'time',
        'user_pv',
      ],
      mock,
      '播放数据',
      formState.date.format(dateFormat)
    )
  }
  const time = ref([newTime, newTime])
  const onRangeClick = async () => {
    message.success('正在导出')
    try {
      const csvData = await getPlayLogsDownload({
        date_range: time.value
          ? [
              dayjs(time.value?.[0]).format(dateFormat),
              dayjs(time.value?.[1]).format(dateFormat),
            ]
          : [
              dayjs(newTime).format(dateFormat),
              dayjs(newTime).format(dateFormat),
            ],
      })
      // 创建 Blob 对象
      const blob = new Blob(['\ufeff' + csvData], {
        type: 'text/csv,charset=UTF-8',
      })
      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute(
        'download',
        '播放数据' +
          (time.value
            ? [
                dayjs(time.value?.[0]).format(dateFormat),
                dayjs(time.value?.[1]).format(dateFormat),
              ].join('-')
            : [
                dayjs(newTime).format(dateFormat),
                dayjs(newTime).format(dateFormat),
              ].join('-'))
      )

      // 触发下载
      document.body.appendChild(link)
      link.click()

      // 清理
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('下载 CSV 文件时出错:', error)
    }
    message.success('导出完成')
  }
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getPlayLogs)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '视频id',
      dataIndex: 'video_id',
    },
    {
      title: '视频名称',
      dataIndex: 'video_name',
    },
    {
      title: '供应商',
      dataIndex: 'cp_name',
    },
    {
      title: '访问用户数（个）',
      dataIndex: 'uv',
      sorter: true,
    },
    {
      title: '播放总次数（次）',
      dataIndex: 'pv',
      sorter: true,
    },
    {
      title: '人均播放时长（秒）',
      dataIndex: 'user_time',
      sorter: true,
    },
    {
      title: '累计播放时长（秒）',
      dataIndex: 'time',
    },
    {
      title: '人均播放次数（次）',
      dataIndex: 'user_pv',
      sorter: true,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    // },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag, filters, sorter) => {
    console.log(filters, sorter)
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init(sorter)
  }
  const init = (orders) => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      video_id: formState.video_id,
      cp_id: formState.cp_id,
      date: formState.date.format(dateFormat),
      orders: orders?.order
        ? [
            {
              column: orders.field,
              direction: orders.order == 'descend' ? 'desc' : 'asc',
            },
          ]
        : undefined,
    })
    routerReplace(pagination.value, {
      cp_id: formState.cp_id,
      video_id: formState.video_id,
      date: formState.date.format(dateFormat),
    })
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
