<template>
  <div class="container">
    <a-flex align="center" class="title">
      <EditOutlined class="pr-10" />
      <span class="font-size16">题库组卷</span>
    </a-flex>
    <div class="container_bottom">
      <div class="box">
        <div class="m-10">
          <div class="bg">
            <a-carousel>
              <div
                v-for="(item, i) in recommendedPosition.G1"
                :key="i"
                class="cursor"
                @click="item.onClick(item)"
              >
                <img :src="item.image1_url" />
              </div>
            </a-carousel>
          </div>
        </div>
      </div>
    </div>

    <useModal :id="id" ref="ModalRef" @init="fetchData" />
    <Release :id="page_id" />
  </div>
</template>

<script setup>
  import { EditOutlined } from '@ant-design/icons-vue'
  import { reactive, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { clearArrays } from '@/utils/index'
  import useModal from '@/views/广告位弹窗'
  import Release from '@/views/页面发布'
  import { getAdverts } from '~/src/api/广告位'

  const route = useRoute()

  const ModalRef = ref('')
  const id = ref('')
  const onClick = (item) => {
    id.value = item.id
    ModalRef.value?.showModal()
  }

  const page_id = route.query?.id
  const recommendedPosition = reactive({})

  const fetchData = async () => {
    const { data } = await getAdverts({
      page_id,
    })
    clearArrays(recommendedPosition)

    data.map((item) => {
      if (!recommendedPosition[item.group_label]) {
        recommendedPosition[item.group_label] = []
      }
      recommendedPosition[item.group_label].push(
        Object.assign(item, {
          onClick: (x) => onClick(x),
        })
      )
    })
  }
  fetchData()
</script>

<style lang="scss" scoped>
  .container {
    img {
      width: 100%;
      height: auto;
    }
    .title {
      padding: 15px;

      border-bottom: 1px solid #ebeef5;
    }
    .container_bottom {
      margin: 15px;
      .cursor {
        border: 1px dashed #9e9e9e;
      }
      .box {
        margin: 0 auto;
        border: 2px dashed #9e9e9e;
        width: 1000px;
        background-color: #f5f5f5;
        color: black;
        .bg {
          width: 100%;
          border: 1px dashed #9e9e9e;
        }
      }
    }
  }
</style>
