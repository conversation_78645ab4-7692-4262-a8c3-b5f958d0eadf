<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><UserOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex align="center" gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item :label="label2">
            <a-input-search
              v-model:value="formState.username"
              allow-clear
              enter-button
              :placeholder="`输入${label2}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">用户列表</div>
    </a-flex>

    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="['name'].includes(column.dataIndex)">
          <a-tag color="orange">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="['cps'].includes(column.dataIndex)">
          <a-tag v-if="text" color="purple">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="['roles'].includes(column.dataIndex)">
          <a-tag v-if="text" color="blue">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="['edit'].includes(column.dataIndex)">
          <a-button type="primary" @click="editProducts(record.id)">
            编辑
          </a-button>
        </template>
        <template v-else-if="['action'].includes(column.dataIndex)">
          <a-button type="link" @click="handleIntegralModal(record.id)">
            积分明细
          </a-button>
          <a-button type="link" @click="handleProductModal(record.username)">
            产品明细
          </a-button>
        </template>
      </template>
    </a-table>
    <editProductsModal ref="editProductsModalRef" />
    <IntegralModal ref="IntegralModalRef" />
    <productModal ref="productModalRef" />
  </div>
</template>

<script setup>
  import { UserOutlined } from '@ant-design/icons-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  // import { getCps } from '@/api/CP'
  import { getUsers } from '@/api/用户'
  // import { getRoles } from '@/api/角色'
  import { routerReplace } from '~/src/utils/routes'

  import editProductsModal from './components/产品包修改'
  import productModal from './components/产品明细'
  import IntegralModal from './components/积分明细'

  const route = useRoute()
  // 搜索框label
  const label = '用户名称'
  const label2 = '登录名'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
    username: route.query?.username,
    site_id: 1,
  })
  // 搜索
  const handleSearch = () => handleOk()

  const handleOk = () => {
    pagination.value.current = 1
    init()
  }

  // 积分明细
  const IntegralModalRef = ref()
  const handleIntegralModal = (value) => {
    IntegralModalRef.value?.setId(value)
    IntegralModalRef.value?.showModal()
  }

  // 产品明细
  const productModalRef = ref()
  const handleProductModal = (value) => {
    productModalRef.value?.setId(value)
    productModalRef.value?.showModal()
  }

  // 用户产品包
  const editProductsModalRef = ref()
  const editProducts = (id) => {
    editProductsModalRef.value.setId(id)
    editProductsModalRef.value.showModal()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getUsers)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        role_id: item.roles?.[0]?.id ?? 0,
        roles: item.roles?.[0]?.name ?? '',
        cp_id: item.cps?.[0]?.id ?? 0,
        cps: item.cps?.[0]?.name ?? '',
        province: item?.region?.province,
        city: item?.region?.city,
        area: item?.region?.area,
      }
    })
  })
  const columns = ref([
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: label2,
      dataIndex: 'username',
    },
    {
      title: '角色',
      dataIndex: 'roles',
    },
    {
      title: '学校',
      dataIndex: 'school',
    },
    {
      title: '年级',
      dataIndex: 'grade',
    },
    {
      title: '省',
      dataIndex: 'province',
    },
    {
      title: '市',
      dataIndex: 'city',
    },
    {
      title: '区',
      dataIndex: 'area',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
    },
    {
      title: '用户产品包',
      dataIndex: 'edit',
    },
    {
      title: '编辑',
      dataIndex: 'action',
    },
  ])
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      name: formState.name,
      username: formState.username,
      site_id: formState.site_id,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
      username: formState.username,
    })
  }
  // 供应商
  // const cpOptions = ref()
  // const handleCpData = async () => {
  //   const { data } = await getCps()
  //   cpOptions.value = data
  //   cpOptions.value.unshift({
  //     name: '暂不绑定',
  //     id: 0,
  //   })
  // }

  // // 角色
  // const ruleOptions = ref()
  // const handleRuleData = async () => {
  //   const { data } = await getRoles()
  //   ruleOptions.value = data
  //   ruleOptions.value.unshift({
  //     name: '暂不绑定',
  //     id: 0,
  //   })
  // }
  // handleRuleData()
  // handleCpData()
  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
