<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :footer="false"
    :title="`产品明细`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item label="">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          rowKey="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, text }">
            <template v-if="['status'].includes(column.dataIndex)">
              <template v-if="text == 1">
                <a-tag color="green">订购</a-tag>
              </template>
              <template v-else>
                <a-tag color="red">退订</a-tag>
              </template>
            </template>
          </template>
        </a-table>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'

  import { getDcoosMessages } from '@/api/产品包'

  const emits = defineEmits(['cancel', 'ok'])

  const { data, run, total, loading, current } = usePagination(getDcoosMessages)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = ref([
    {
      title: '订购/退订',
      dataIndex: 'status',
    },
    {
      title: '产品包名称',
      dataIndex: 'product_name',
    },
    {
      title: '时间',
      dataIndex: 'created_at',
    },
  ])
  const pagination = computed(() => {
    return {
      showQuickJumper: true,
      current: current.value,
      pageSize: 5,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      client_id: id.value,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
  }

  // 栏目id
  const id = ref()
  const formRef = ref('')
  const formState = reactive({})
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    pagination.value.current = 1
    init()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
