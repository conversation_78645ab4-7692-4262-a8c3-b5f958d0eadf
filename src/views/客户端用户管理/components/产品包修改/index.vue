<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="修改产品包"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item label="已绑定产品包">
        <a-tag
          v-for="(item, i) in productsData"
          :key="i"
          closable
          :color="
            dayjs().isAfter(dayjs(item.end_time, 'YYYY-MM-DD HH:mm:ss'))
              ? 'red'
              : 'green'
          "
          @close.prevent="close(item)"
        >
          {{ item.name }}-({{ item.start_time }}-{{ item.end_time }})
        </a-tag>
      </a-form-item>
      <a-form-item
        label="绑定产品包"
        :rules="[{ required: true, message: '绑定产品包不能为空!' }]"
      >
        <a-select
          v-model:value="formState.product_id"
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item
        label="绑定状态"
        :rules="[{ required: true, message: '绑定产品包不能为空!' }]"
      >
        <a-select
          v-model:value="formState.status"
          :options="[
            {
              label: '订购',
              value: 1,
            },
          ]"
          style="width: 200px"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.status"
        label="订购天数"
        :rules="[{ required: true, message: '绑定产品包不能为空!' }]"
      >
        <a-space direction="vertical" :size="10">
          <a-input-number
            id="inputNumber"
            v-model:value="formState.days"
            :min="0"
          />
          <a-space :size="10" wrap>
            <a-button type="primary" @click="() => (formState.days = 7)">
              7天
            </a-button>
            <a-button type="primary" @click="() => (formState.days = 30)">
              一个月
            </a-button>
            <a-button type="primary" @click="() => (formState.days = 60)">
              二个月
            </a-button>
            <a-button type="primary" @click="() => (formState.days = 90)">
              三个月
            </a-button>
            <a-button type="primary" @click="() => (formState.days = 365)">
              一年
            </a-button>
          </a-space>
        </a-space>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { reactive, ref } from 'vue'

  import { getProducts } from '@/api/产品包'
  import { editUsersProducts, getUsersDetail } from '@/api/用户'
  import { modal } from '@/utils/modal.ts'

  const emits = defineEmits(['cancel', 'ok'])
  const productsData = ref([])
  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const formRef = ref()
  const formState = reactive({
    product_id: undefined, // 产品包id
    status: 1, // 0退订 1订购
    days: 1, // 1的必填 订购天数
  })
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getProducts()

    console.log(data.map((item) => item.id))
    // const productIdsToDisable = productsData.value.map((item) => item.id)
    options.value = data.map((item) => {
      return {
        id: item.id,
        name: item.name,
        // disabled: productIdsToDisable.includes(item.id),
      }
    })
  }

  const open = ref(false)
  const showModal = async () => {
    open.value = true
    handleData()
  }
  const handleData = async () => {
    const {
      data: { name, products },
    } = await getUsersDetail(id.value)
    formState.name = name
    productsData.value = products
    handleOptions()
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editUsersProducts(formState, id.value)

    if (code == 200) {
      message.success('修改成功')
      handleData()
    }
  }
  const handleCancel = async () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const close = (item) => {
    console.log(item)
    modal(async () => {
      const { code } = await editUsersProducts(
        {
          product_id: item.id,
          status: 0,
        },
        id.value
      )
      if (code == 200) {
        message.success('编辑成功')
        handleData()
      }
    })
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
