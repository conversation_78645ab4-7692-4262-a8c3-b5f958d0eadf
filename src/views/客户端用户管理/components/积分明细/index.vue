<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :footer="false"
    :title="`积分明细`"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item label="用户头像" name="avatar_url">
        <a-image :src="formState.avatar_url" :width="100" />
      </a-form-item>
      <a-form-item label="用户名称" name="name">
        {{ formState.name }}
      </a-form-item>
      <a-form-item label="云电脑账号/登录名" name="username">
        {{ formState.username }}
      </a-form-item>
      <a-form-item label="当前积分" name="point">
        {{ formState.point }}
      </a-form-item>
      <a-form-item label="">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          rowKey="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, text }">
            <template v-if="['type'].includes(column.dataIndex)">
              <template v-if="text == 1">
                <a-tag color="green">获取</a-tag>
              </template>
              <template v-else>
                <a-tag color="red">消耗</a-tag>
              </template>
            </template>
          </template>
        </a-table>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'

  import { getPointLogs, getUsersDetail } from '@/api/用户'

  const emits = defineEmits(['cancel', 'ok'])

  const { data, run, total, loading, current } = usePagination(getPointLogs)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = ref([
    {
      title: '积分',
      dataIndex: 'point',
    },
    {
      title: '获取/消耗',
      dataIndex: 'type',
    },
    {
      title: '来源',
      dataIndex: 'message',
    },
    {
      title: '时间',
      dataIndex: 'created_at',
    },
  ])
  const pagination = computed(() => {
    return {
      showQuickJumper: true,
      current: current.value,
      pageSize: 5,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      user_id: id.value,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
  }

  // 栏目id
  const id = ref()
  const formRef = ref('')
  const formState = reactive({
    avatar_url: '',
    name: '',
    username: '',
    point: 0,
  })
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    pagination.value.current = 1
    init()
    await handleUsersDetailData()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    id.value = undefined
    open.value = false
    formRef.value?.resetFields()
    emits('cancel')
  }

  const handleUsersDetailData = async () => {
    const { data } = await getUsersDetail(id.value)
    console.log(data)
    formState.avatar_url = data.avatar_url
    formState.name = data.name
    formState.username = data.username
    formState.point = data?.point?.point ?? 0
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
