<template>
  <div>
    <a-flex align="center" justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10" justify="flex-end" wrap="wrap">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.title"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年级">
            <a-select
              v-model:value="formState.grade"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options1"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学科">
            <a-select
              v-model:value="formState.subject"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options2"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="地区">
            <a-cascader
              v-model:value="formState.region"
              change-on-select
              expand-trigger="hover"
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options3"
              :placeholder="`请选择地区`"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年份">
            <a-select
              v-model:value="formState.year"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options4"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">试卷列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">创建试卷</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :defaultExpandAllRows="true"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['has_answer'].includes(column.dataIndex)">
          <template v-if="text == 1">
            <a-tag color="success">有</a-tag>
          </template>
          <template v-else>
            <a-tag color="error">无</a-tag>
          </template>
        </template>
        <template
          v-else-if="
            ['year', 'view_count', 'download_count'].includes(column.dataIndex)
          "
        >
          <a-tag color="blue">{{ text }}</a-tag>
        </template>
        <template v-else-if="['is_top'].includes(column.dataIndex)">
          <a-switch
            v-model:checked="record.is_top"
            checked-children="是"
            :checkedValue="1"
            un-checked-children="否"
            :unCheckedValue="0"
            @change="onChange($event, record)"
          />
        </template>

        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>

            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
    <CreateQuestions ref="createQuestionsRef" @ok="handleOk" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import {
    computed,
    // nextTick,
    reactive,
    ref,
  } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { getQbQueSources } from '@/api/试题/资源'
  import {
    deleteQbExamPapers,
    editQbExamPapers,
    getQbExamPapers,
  } from '~/src/api/试卷/试卷'
  import { getQbSubjects } from '~/src/api/试题/学科'
  import { routerReplace } from '~/src/utils/routes'

  import CreateQuestions from './components/创建试卷'

  const route = useRoute()
  // 搜索框label
  const label = '标题'
  // 搜索表单
  const formState = reactive({
    grade: route.query?.grade,
    subject: route.query?.subject,
    title: route.query?.title,
    region: route.query?.region ?? [],
    year: route.query?.year,
  })
  // 年级
  const options1 = ref([])
  // 学科
  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getQbSubjects()
    options2.value = data
  }
  handleOptions2()

  // 区域
  const options3 = ref([])
  // 年份
  const options4 = ref([])
  const handleOptions3 = async () => {
    const { data } = await getQbQueSources({
      parent_id: 0,
    })
    data.forEach((item) => {
      switch (item.code.code) {
        case 'province':
          options3.value.push(item)
          break
        case 'year':
          options4.value.push(item)
          break
        case 'grade':
          options1.value.push(item)
          break
        default:
          break
      }
    })
  }
  handleOptions3()
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbExamPapers)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试卷id',
      dataIndex: 'id',
    },
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '年份',
      dataIndex: 'year',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '查看次数',
      dataIndex: 'view_count',
    },
    {
      title: '下载次数',
      dataIndex: 'download_count',
    },
    {
      title: '是否置顶',
      dataIndex: 'is_top',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    let region = ''

    if (Array.isArray(formState.region) && formState.region.length > 0) {
      region = formState.region[formState.region.length - 1]
    }
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      grade: formState.grade,
      subject: formState.subject,
      title: formState.title,
      region,
      year: formState.year,
      orders: [
        {
          column: 'is_top',
          direction: 'desc',
        },
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      title: formState.title,
    })
  }

  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteQbExamPapers(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }
  // 创建试卷
  const createQuestionsRef = ref('')
  const onClick = () => createQuestionsRef.value?.showModal()
  const handleOk = () => {
    init()
  }
  // 编辑试卷
  const edit = (id) => {
    createQuestionsRef.value?.setId(id)
    createQuestionsRef.value?.showModal()
  }

  // 是否置顶
  // 账号状态
  const onChange = async (checked, record) => {
    console.log(checked, record)
    const { code } = await editQbExamPapers(
      {
        is_top: checked,
      },
      record.id
    )
    if (code == 200) {
      init()
      message.success('修改成功')
    }
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
