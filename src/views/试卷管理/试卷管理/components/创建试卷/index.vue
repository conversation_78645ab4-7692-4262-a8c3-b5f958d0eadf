<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="id ? '编辑试卷' : '创建试卷'"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item
        :label="label1"
        name="title"
        :rules="[{ required: true, message: `请输入${label1}!` }]"
      >
        <a-input
          v-model:value="formState.title"
          :placeholder="`请输入${label1}`"
        />
      </a-form-item>
      <a-form-item
        label="文件"
        name="file_id"
        :rules="[{ required: true, message: `文件不能为空!` }]"
      >
        <Audio
          ref="AudioRef"
          listType="text"
          @setImageAddress="handleParamsCover_url_id"
        />
      </a-form-item>
      <a-form-item v-if="id" label="年份" name="year">
        <a-date-picker v-model:value="formState.year" picker="year" />
      </a-form-item>
      <a-form-item label="有无答案" name="has_answer">
        <a-switch
          v-model:checked="formState.has_answer"
          checked-children="有"
          :checkedValue="1"
          un-checked-children="无"
          :unCheckedValue="0"
        />
      </a-form-item>
      <a-form-item label="是否置顶" name="is_top">
        <a-switch
          v-model:checked="formState.is_top"
          checked-children="是"
          :checkedValue="1"
          un-checked-children="否"
          :unCheckedValue="0"
        />
      </a-form-item>
      <a-form-item
        label="学段学科"
        name="value"
        :rules="[{ required: true, message: `请选择学段学科!` }]"
      >
        <a-cascader
          v-model:value="formState.value"
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
            children: 'qb_subjects',
          }"
          :multiple="true"
          :options="options"
          :placeholder="`请选择学段学科`"
          :showCheckedStrategy="Cascader.SHOW_CHILD"
          @change="handleOptions2"
        />
      </a-form-item>
      <a-tree
        v-model:checkedKeys="checkedKeys"
        :checkable="true"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
      >
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { Cascader, message } from 'ant-design-vue'
  import dayjs from 'dayjs'
  import { debounce } from 'lodash-es'
  import { reactive, ref } from 'vue'

  import { getQbColumns } from '@/api/试卷/栏目'
  import {
    createQbExamPapers,
    editQbExamPapers,
    getQbExamPapersDetail,
  } from '@/api/试卷/试卷'
  import { getQbGrades } from '@/api/试题/学段'
  import { debounceTime } from '@/config'
  import { uniqueArrays } from '@/utils'
  import Audio from '@/views/audio'

  const label1 = '标题'
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    title: '',
    file_id: '',
    year: undefined,
    has_answer: 0,
    is_top: 0,
    value: [],
    qb_columns: [],
  })
  const id = ref()
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)
  const handleCancel = () => {
    id.value = undefined
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    checkedKeys.value = []
    treeData.value = []
    open.value = true
    id.value && (await init())
  }
  const AudioRef = ref('')
  const handleOk = async () => {
    await formRef.value?.validate()
    let code
    if (id.value) {
      const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
        checkedKeys.value,
        formState.qb_columns
      )
      ;({ code } = await editQbExamPapers(
        {
          title: formState.title,
          file_id: formState.file_id,
          year: dayjs(formState.year).format('YYYY'),
          has_answer: formState.has_answer,
          is_top: formState.is_top,
          qb_columns: {
            sync: uniqueToFirstArray.map((item) => {
              return {
                id: item,
              }
            }),
            del: uniqueToSecondArray.map((item) => {
              return {
                id: item,
              }
            }),
          },
        },
        id.value
      ))
    } else {
      ;({ code } = await createQbExamPapers({
        title: formState.title,
        file_id: formState.file_id,
        has_answer: formState.has_answer,
        is_top: formState.is_top,
        qb_columns: {
          sync: checkedKeys.value.map((item) => {
            return {
              id: item,
            }
          }),
        },
      }))
    }

    if (code == 200) {
      emits('ok')
      message.success(id.value ? `编辑成功` : '创建成功')
    }
    handleCancel()
  }
  // 设置图片上传成功回调
  const handleParamsCover_url_id = (value) => {
    formState.file_id = value
  }
  const init = async () => {
    const { data } = await getQbExamPapersDetail(id.value)
    AudioRef.value?.fileList.push({
      url: data.file_url,
    })
    formState.title = data.title
    formState.file_id = data.file_id
    formState.year = dayjs(data.year, 'YYYY')
    formState.has_answer = data.has_answer
    formState.is_top = data.is_top
    formState.qb_columns = data.qb_columns.map((item) => item.id)
    formState.value = Array.from(
      new Set(
        data.qb_columns
          .map((item) => {
            return [item.qb_grade_id, item.qb_subject_id]
          })
          .map(JSON.stringify)
      )
    ).map(JSON.parse)
    data.qb_columns.length > 0 && handleOptions2()
    checkedKeys.value = data.qb_columns.map((item) => item.id)
  }

  // 学段学科
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data.map((item) => {
      return {
        ...item,
      }
    })
  }
  handleOptions()

  const treeData = ref([])
  const checkedKeys = ref([])
  const handleOptions2 = debounce(async () => {
    const array = []
    for (let i = 0; i < formState.value.length; i++) {
      const { data } = await getQbColumns({
        qb_grade_id: formState.value[i][0],
        qb_subject_id: formState.value[i][1],
        parent_id: 0,
        orders: [
          {
            column: 'orderby',
            direction: 'desc',
          },
        ],
      })
      const result =
        options.value
          .filter((item) => formState.value[i][0] == item.id)
          .flatMap((item) =>
            item.qb_subjects
              .filter((subject) => formState.value[i][1] == subject.id)
              .map((subject) => `${item.name}-${subject.name}`)
          )[0] || ''
      array.push({
        name: result,
        id: result,
        type: 1,
        disabled: true,
        children: addDisabledToChildren(data),
      })
    }
    treeData.value = array
  }, debounceTime * 3)
  // 递归函数：给树形结构的每个节点添加 disabled: true 字段
  const addDisabledToChildren = (array) => {
    return array.map((item) => ({
      ...item,
      disabled: item.type == 1 ? true : false,
      children: item.children ? addDisabledToChildren(item.children) : [], // 递归处理子对象
    }))
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
