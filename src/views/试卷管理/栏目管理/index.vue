<template>
  <a-row justify="space-between">
    <a-col :span="5">
      <a-flex align="center" class="py-10" gap="10">
        <div><DefaultDivider /></div>
        <div class="font-bold">栏目列表</div>
      </a-flex>

      <a-form ref="formRef" layout="vertical" :model="formState">
        <a-form-item
          :label="label1"
          name="value"
          :rules="[
            { required: true, type: 'array', message: `请选择${label1}!` },
          ]"
        >
          <a-cascader
            v-model:value="formState.value"
            expand-trigger="hover"
            :fieldNames="{
              label: 'name',
              value: 'id',
              children: 'qb_subjects',
            }"
            :options="options"
            :placeholder="`请选择${label1}`"
            @change="onChange"
          />
        </a-form-item>
      </a-form>
      <a-input-search v-model:value="searchValue" style="margin-bottom: 8px" />
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :autoExpandParent="autoExpandParent"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
        @expand="onExpand"
        @select="onSelect"
      >
        <template #title="{ type, id, name, is_show }">
          <a-space :size="10">
            <a-dropdown :trigger="['contextmenu']">
              <span v-if="name.indexOf(searchValue) > -1">
                {{ name.substring(0, name.indexOf(searchValue)) }}
                <span style="color: #f50">{{ searchValue }}</span>
                {{
                  name.substring(name.indexOf(searchValue) + searchValue.length)
                }}
              </span>
              <span v-else>{{ name }}</span>
              <template #overlay>
                <a-menu
                  @click="({ key: menuKey }) => onContextMenuClick(menuKey, id)"
                >
                  <template v-if="type == 1">
                    <a-menu-item key="1">创建目录</a-menu-item>
                    <a-menu-item key="2">创建试卷包(能绑定试卷)</a-menu-item>
                  </template>
                  <a-menu-item key="4">修改名称</a-menu-item>
                  <a-menu-item key="3">删除栏目</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-switch
              :checked="is_show == 1"
              checked-children="发布"
              size="small"
              un-checked-children="未发布"
              @change="onChecked($event, id)"
            />
          </a-space>
        </template>

        <!-- <template #switcherIcon="{ switcherCls }">
          <down-outlined :class="switcherCls" />
        </template> -->
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-col>
    <a-col :span="18">
      <a-form>
        <a-flex gap="10" wrap="wrap">
          <a-form-item :label="label3">
            <a-input-search
              v-model:value="formState.title"
              allow-clear
              enter-button
              :placeholder="`输入${label3}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年级">
            <a-select
              v-model:value="formState.grade"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options1"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="学科">
            <a-select
              v-model:value="formState.subject"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options2"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="地区">
            <a-cascader
              v-model:value="formState.region"
              change-on-select
              expand-trigger="hover"
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options3"
              :placeholder="`请选择地区`"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item label="年份">
            <a-select
              v-model:value="formState.year"
              allow-clear
              :fieldNames="{
                label: 'name',
                value: 'name',
              }"
              :options="options4"
              style="width: 140px"
              @change="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
      <a-divider style="height: 3px; background-color: #eeeeee" />
      <a-flex align="center" class="py-10" gap="10">
        <DefaultDivider />
        <div class="font-bold">试卷列表</div>
      </a-flex>
      <a-flex class="py-10" gap="10">
        <a-flex gap="10" vertical>
          <a-flex gap="10">
            <a-button type="primary" @click="onContextMenuClick('1', 0)">
              创建顶级栏目
            </a-button>
            <a-button :disabled="disabled" type="primary" @click="onClick">
              绑定试卷
            </a-button>
            <a-button :disabled="disable2" type="primary" @click="onClick2">
              绑定搜索资源
            </a-button>
          </a-flex>
          <a-flex gap="10">
            <a-popconfirm
              cancel-text="否"
              :disabled="!hasSelected"
              ok-text="是"
              title="是否确定解除?"
              @confirm="handleUnbindAll"
            >
              <a-button
                :disabled="!hasSelected"
                :loading="state.loading"
                type="primary"
              >
                解除绑定
              </a-button>
            </a-popconfirm>
            <a-button
              :disabled="!hasSelected"
              :loading="state.loading"
              type="primary"
              @click="handleUnbindPaths"
            >
              绑定路径
            </a-button>

            <a-flex v-if="hasSelected" align="center" gap="5">
              <span>已选中</span>
              <a-flex wrap="wrap">
                <a-tag
                  v-for="(item, i) in state.selectedRows"
                  :key="i"
                  closable
                  @close.prevent="onAlbumsClose(item.id)"
                >
                  {{ item.id }}
                </a-tag>
              </a-flex>
            </a-flex>
          </a-flex>
        </a-flex>
      </a-flex>

      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="{
          selectedRowKeys: state.selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
        }"
        rowKey="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="['has_answer'].includes(column.dataIndex)">
            <template v-if="text == 1">
              <a-tag color="success">有</a-tag>
            </template>
            <template v-else>
              <a-tag color="error">无</a-tag>
            </template>
          </template>
          <template
            v-else-if="
              ['year', 'view_count', 'download_count'].includes(
                column.dataIndex
              )
            "
          >
            <a-tag color="blue">{{ text }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-space :size="10">
              <a-button type="primary" @click="onEditQuestions(record.id)">
                编辑
              </a-button>
              <a-popconfirm
                cancel-text="否"
                ok-text="是"
                title="是否确定解除?"
                @confirm="onDelete(record.id)"
              >
                <a-button danger type="primary">解除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-col>
    <CreateQuestions ref="createQuestionsRef" @ok="handleOk" />
    <CreateSearches ref="CreateSearchesRef" @ok="handleOk" />
    <CreateColumn ref="createColumnRef" @ok="handleOk" />
    <EditModal ref="editModalRef" @ok="handleOk" />
    <editQuestions ref="editQuestionsRef" @ok="handleOk" />
    <EditQuestionsPath
      ref="EditQuestionsPathRef"
      :selectedRows="state.selectedRows"
      @ok="
        () => {
          state.loading = false
          state.selectedRowKeys = []
          handleOk()
        }
      "
    />
  </a-row>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref, watch } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { deleteQbColumns, editQbColumns, getQbColumns } from '@/api/试卷/栏目'
  import { getQbExamPapers } from '@/api/试卷/试卷'
  import { getQbGrades } from '@/api/试题/学段'
  import { getQbQueSources } from '@/api/试题/资源'
  import { modal } from '@/utils/modal'
  import { getQbSubjects } from '~/src/api/试题/学科'
  import { routerReplaceNoPagi } from '~/src/utils/routes'

  import editQuestions from '../试卷管理/components/创建试卷'
  import EditModal from './components/修改栏目'
  import CreateColumn from './components/创建栏目'
  import CreateSearches from './components/绑定搜索资源'
  import CreateQuestions from './components/绑定试题'
  import EditQuestionsPath from './components/绑定路径'
  const route = useRoute()

  const label1 = '学段学科'
  const label3 = '标题'
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data
  }
  handleOptions()

  // 年级
  const options1 = ref([])
  // 学科
  const options2 = ref([])
  const handleOptions2 = async () => {
    const { data } = await getQbSubjects()
    options2.value = data
  }
  handleOptions2()

  // 区域
  const options3 = ref([])
  // 年份
  const options4 = ref([])
  const handleOptions3 = async () => {
    const { data } = await getQbQueSources({
      parent_id: 0,
    })
    data.forEach((item) => {
      switch (item.code.code) {
        case 'province':
          options3.value.push(item)
          break
        case 'year':
          options4.value.push(item)
          break
        case 'grade':
          options1.value.push(item)
          break
        default:
          break
      }
    })
  }
  handleOptions3()

  const onChange = () => {
    treeData.value = []
    fetch()
  }

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const searchValue = ref('')
  const formRef = ref()
  const formState = reactive({
    title: '',
    value: route.query?.qb_grade_id
      ? [
          parseInt(route.query?.qb_grade_id),
          parseInt(route.query?.qb_subject_id),
        ]
      : [],
    grade: '',
    subject: '',
    region: [],
    year: '',
  })

  // 展开指定的树节点
  const expandedKeys = ref(
    route.query?.expandedKeys
      ? route.query?.expandedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  const selectedKeys = ref(
    route.query?.selectedKeys
      ? route.query?.selectedKeys.split(',').map((item) => parseInt(item))
      : []
  )
  // treeNodes 数据
  const treeData = ref([])
  const getParentKey = (id, tree) => {
    let parentKey
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i]
      if (node.children) {
        if (node.children.some((item) => item.id === id)) {
          parentKey = node.id
        } else if (getParentKey(id, node.children)) {
          parentKey = getParentKey(id, node.children)
        }
      }
    }
    return parentKey
  }
  // 树-所有项放到同级
  const dataList = ref([])
  const generateList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i]
      dataList.value.push({
        name: node.name,
        id: node.id,
      })
      if (node.children) {
        generateList(node.children)
      }
    }
  }

  // 是否自动展开
  const autoExpandParent = ref(true)
  // 树-展开事件
  const onExpand = (keys) => {
    expandedKeys.value = keys
    autoExpandParent.value = false
  }
  watch(searchValue, (value) => {
    generateList(treeData.value)
    const expanded = dataList.value
      .map((item) => {
        if (item.name.indexOf(value) > -1) {
          return getParentKey(item.id, treeData.value)
        }
        return null
      })
      .filter((item, i, self) => {
        return item && self.indexOf(item) === i
      })
    expandedKeys.value = expanded
    searchValue.value = value
    autoExpandParent.value = true
  })

  // 树-右键菜单
  const onContextMenuClick = (menuKey, id) => {
    switch (menuKey) {
      case '1':
      case '2':
        createColumnRef.value.formState.qb_grade_id = formState.value[0]
        createColumnRef.value.formState.qb_subject_id = formState.value[1]
        createColumnRef.value.formState.parent_id = id
        createColumnRef.value.formState.type = Number(menuKey)
        createColumnRef.value?.showModal()
        break
      case '3':
        handleDeleteColumns(id)
        break
      case '4':
        edit(id)
        break
      default:
        break
    }
  }
  // 删除栏目
  const handleDeleteColumns = async (value) => {
    modal(async () => {
      const { code } = await deleteQbColumns(value)
      if (code == 200) {
        message.success('删除成功')
      }
      await fetch()
    })
  }

  // 弹窗
  const createColumnRef = ref('')
  // 创建栏目-成功
  const handleOk = async () => {
    // expandedKeys.value = []
    await fetch()
    // 有表格加载id则加载
    qb_column_id.value && init()
  }

  // 编辑试卷
  const editQuestionsRef = ref()
  const onEditQuestions = (id) => {
    editQuestionsRef.value?.setId(id)
    editQuestionsRef.value?.showModal()
  }

  // 删除试卷
  const onDelete = async (id) => {
    const { code } = await editQbColumns(
      {
        qbExamPapers: {
          del: [
            {
              id,
            },
          ],
        },
      },
      qb_column_id.value
    )
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('解除成功')
    }
  }
  // 知识点发布
  const onChecked = async (checked, id) => {
    const { code } = await editQbColumns(
      {
        is_show: checked == false ? 0 : 1,
      },
      id
    )
    handleOk()
    if (code == 200) {
      message.success('编辑成功')
    }
  }

  // 批量删除
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
    loading: false,
  })
  const hasSelected = computed(() => state.selectedRowKeys.length > 0)
  const handleUnbindAll = async () => {
    state.loading = true
    const { code } = await editQbColumns(
      {
        qbExamPapers: {
          del: state.selectedRowKeys.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      qb_column_id.value
    )
    state.loading = false
    state.selectedRowKeys = []
    handleOk()
    if (code == 200) {
      message.success('删除成功')
    }
  }

  const EditQuestionsPathRef = ref()
  const handleUnbindPaths = () => {
    EditQuestionsPathRef.value.showModal()
  }
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const onAlbumsClose = (value) => {
    state.selectedRowKeys = state.selectedRowKeys.filter((item) => {
      return item !== value
    })
    state.selectedRows = state.selectedRows.filter((item) => {
      return item.id !== value
    })
  }

  // 绑定试卷
  const createQuestionsRef = ref()
  const onClick = () => {
    createQuestionsRef.value.setId(qb_column_id.value)
    createQuestionsRef.value.showModal()
  }

  // 绑定搜索资源
  const CreateSearchesRef = ref()
  const onClick2 = () => {
    CreateSearchesRef.value.setId(qb_column_id.value)
    CreateSearchesRef.value.showModal()
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  // 编辑
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 表格
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbExamPapers)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试卷id',
      dataIndex: 'id',
    },
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '年份',
      dataIndex: 'year',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '查看次数',
      dataIndex: 'view_count',
    },
    {
      title: '下载次数',
      dataIndex: 'download_count',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    let region = ''

    if (Array.isArray(formState.region) && formState.region.length > 0) {
      region = formState.region[formState.region.length - 1]
    }
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      title: formState.title,
      grade: formState.grade,
      subject: formState.subject,
      region,
      year: formState.year,
      qb_column_id: qb_column_id.value,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplaceNoPagi({
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
      expandedKeys: expandedKeys.value.join(','),
      qb_column_id: qb_column_id.value,
      selectedKeys: selectedKeys.value.join(','),
    })
  }
  // 栏目id
  const qb_column_id = ref(route.query?.qb_column_id)
  qb_column_id.value && init()

  // 绑定试卷-按钮可用性
  const disabled = ref(true)
  // 绑定搜索资源-按钮可用性
  const disable2 = ref(true)
  // 点击树节点触发
  const onSelect = async (item, { node: { dataRef } }) => {
    await formRef.value?.validate()
    if (item.length == 0) return
    console.log(dataRef)
    disabled.value = dataRef.type == 2 ? false : true
    disable2.value = dataRef.parent_id == 0 ? false : true
    if (!expandedKeys.value.includes(dataRef.id))
      expandedKeys.value.push(dataRef.id)
    selectedKeys.value = [dataRef.id]
    pagination.value.current = 1
    qb_column_id.value = dataRef.id
    state.selectedRowKeys = []
    init()
  }

  const fetch = async () => {
    const { data } = await getQbColumns({
      qb_grade_id: formState.value[0],
      qb_subject_id: formState.value[1],
      parent_id: 0,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
      ],
    })
    treeData.value = data
  }
  if (route.query?.qb_grade_id) {
    onChange()
  }
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
