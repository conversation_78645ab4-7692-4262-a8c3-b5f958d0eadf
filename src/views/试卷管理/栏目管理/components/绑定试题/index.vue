<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="绑定试卷"
    :width="900"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item label="标题" name="title">
        <a-input-search
          v-model:value="formState.title"
          allow-clear
          enter-button
          :placeholder="`输入标题快速查询`"
          @pressEnter="handleSearch"
          @search="handleSearch"
        />
      </a-form-item>
    </a-form>
    <a-flex align="center" gap="5">
      <span>已选中</span>
      <a-flex wrap="wrap">
        <a-tag
          v-for="(item, i) in state.selectedRows"
          :key="i"
          closable
          @close.prevent="onAlbumsClose(item.id)"
        >
          {{ item.id }}
        </a-tag>
      </a-flex>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :defaultExpandAllRows="true"
      :loading="loading"
      :pagination="pagination"
      :row-selection="{
        selectedRowKeys: state.selectedRowKeys,
        onChange: onSelectChange,
        preserveSelectedRowKeys: true,
      }"
      rowKey="id"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="['has_answer'].includes(column.dataIndex)">
          <template v-if="text == 1">
            <a-tag color="success">有</a-tag>
          </template>
          <template v-else>
            <a-tag color="error">无</a-tag>
          </template>
        </template>
        <template
          v-else-if="
            ['year', 'view_count', 'download_count'].includes(column.dataIndex)
          "
        >
          <a-tag color="blue">{{ text }}</a-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'

  import { editQbColumns } from '@/api/试卷/栏目'
  import { getQbExamPapers } from '@/api/试卷/试卷'

  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    title: '',
  })
  const open = ref(false)
  const handleCancel = () => {
    state.selectedRowKeys = []
    state.selectedRows = []
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = () => {
    handleSearch()
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editQbColumns(
      {
        qbExamPapers: {
          sync: state.selectedRowKeys.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }
  // 表格
  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbExamPapers)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
      }
    })
  })
  const columns = [
    {
      title: '试卷id',
      dataIndex: 'id',
    },
    {
      title: '标题',
      dataIndex: 'title',
    },
    {
      title: '年份',
      dataIndex: 'year',
    },
    {
      title: '有无答案',
      dataIndex: 'has_answer',
    },
    {
      title: '查看次数',
      dataIndex: 'view_count',
    },
    {
      title: '下载次数',
      dataIndex: 'download_count',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      title: formState.title,
      unrelated_qb_column_id: id.value,
      orders: [
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
  }

  // 批量删除
  const state = reactive({
    selectedRowKeys: [],
    selectedRows: [],
  })
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log('selectedRowKeys changed: ', selectedRowKeys, selectedRows)
    state.selectedRowKeys = selectedRowKeys
    state.selectedRows = selectedRows
  }
  const onAlbumsClose = (value) => {
    state.selectedRowKeys = state.selectedRowKeys.filter((item) => {
      return item !== value
    })
    state.selectedRows = state.selectedRows.filter((item) => {
      return item.id !== value
    })
  }
  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
