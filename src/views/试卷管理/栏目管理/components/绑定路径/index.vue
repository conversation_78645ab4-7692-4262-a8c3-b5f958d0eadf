<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="'绑定路径'"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :label-col="{ span: 4 }" :model="formState">
      <a-form-item label="选中试卷id">
        <a-flex wrap="wrap">
          <a-tag v-for="(item, i) in selectedRows" :key="i">
            {{ item.id }}
          </a-tag>
        </a-flex>
      </a-form-item>
      <a-form-item
        label="学段学科"
        name="value"
        :rules="[{ required: true, message: `请选择学段学科!` }]"
      >
        <a-cascader
          v-model:value="formState.value"
          expand-trigger="hover"
          :fieldNames="{
            label: 'name',
            value: 'id',
            children: 'qb_subjects',
          }"
          :multiple="true"
          :options="options"
          :placeholder="`请选择学段学科`"
          :showCheckedStrategy="Cascader.SHOW_CHILD"
          @change="handleOptions2"
        />
      </a-form-item>
      <a-tree
        v-model:checkedKeys="checkedKeys"
        :checkable="true"
        :field-names="{
          title: 'name',
          key: 'id',
        }"
        :height="730"
        :show-icon="true"
        :show-line="{ showLeafIcon: false }"
        :tree-data="treeData"
      >
        <template #icon="{ type }">
          <template v-if="type == 1">
            <FolderOpenOutlined />
          </template>
          <template v-else>
            <TableOutlined />
          </template>
        </template>
      </a-tree>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { FolderOpenOutlined, TableOutlined } from '@ant-design/icons-vue'
  import { Cascader, message } from 'ant-design-vue'
  import { debounce } from 'lodash-es'
  import { reactive, ref, toRefs } from 'vue'

  import { getQbColumns } from '@/api/试卷/栏目'
  import { editQbExamPapersBatch } from '@/api/试卷/试卷'
  import { getQbGrades } from '@/api/试题/学段'
  import { debounceTime } from '@/config'

  const props = defineProps(['selectedRows'])
  const { selectedRows } = toRefs(props)

  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    value: [],
    qb_columns: [],
  })

  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = async () => {
    checkedKeys.value = []
    treeData.value = []
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()

    const { code } = await editQbExamPapersBatch({
      ids: selectedRows.value.map((item) => item.id),
      qb_columns: {
        clear: 1,
        sync: checkedKeys.value.map((item) => {
          return {
            id: item,
          }
        }),
      },
    })
    if (code == 200) {
      emits('ok')
      message.success('编辑成功')
    }
    handleCancel()
  }
  // 学段学科
  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbGrades()
    options.value = data.map((item) => {
      return {
        ...item,
      }
    })
  }
  handleOptions()

  const treeData = ref([])
  const checkedKeys = ref([])
  const handleOptions2 = debounce(async () => {
    const array = []
    for (let i = 0; i < formState.value.length; i++) {
      const { data } = await getQbColumns({
        qb_grade_id: formState.value[i][0],
        qb_subject_id: formState.value[i][1],
        parent_id: 0,
        orders: [
          {
            column: 'orderby',
            direction: 'desc',
          },
        ],
      })
      const result =
        options.value
          .filter((item) => formState.value[i][0] == item.id)
          .flatMap((item) =>
            item.qb_subjects
              .filter((subject) => formState.value[i][1] == subject.id)
              .map((subject) => `${item.name}-${subject.name}`)
          )[0] || ''
      array.push({
        name: result,
        id: result,
        type: 1,
        disabled: true,
        children: addDisabledToChildren(data),
      })
    }
    treeData.value = array
  }, debounceTime * 3)
  // 递归函数：给树形结构的每个节点添加 disabled: true 字段
  const addDisabledToChildren = (array) => {
    return array.map((item) => ({
      ...item,
      disabled: item.type == 1 ? true : false,
      children: item.children ? addDisabledToChildren(item.children) : [], // 递归处理子对象
    }))
  }

  defineExpose({
    showModal,
    formState,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
