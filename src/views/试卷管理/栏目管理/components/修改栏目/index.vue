<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    :title="`编辑${label}`"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="`${label}名称`"
        name="name"
        :rules="[{ required: true, message: `${label}名称不能为空!` }]"
      >
        <a-input v-model:value="formState.name" />
      </a-form-item>
      <a-form-item
        label="排序权重"
        name="orderby"
        :rules="[
          { required: true, type: 'number', message: '排序权重不能为空' },
        ]"
      >
        <a-input-number v-model:value="formState.orderby" :min="0" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { editQbColumns, getQbColumnsDetail } from '@/api/试卷/栏目'

  const label = '栏目'
  const emits = defineEmits(['cancel', 'ok'])

  const id = ref()
  const formRef = ref('')
  const formState = reactive({
    name: '',
    orderby: 0,
  })
  const setId = (value) => {
    id.value = value
  }

  const open = ref(false)

  // 打开弹窗
  const showModal = async () => {
    await fetch()
    open.value = true
  }
  // 关闭弹窗
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  // 加载数据
  const fetch = async () => {
    const { data } = await getQbColumnsDetail(id.value)
    Object.assign(formState, data)
  }
  // 提交
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await editQbColumns(formState, id.value)
    if (code == 200) {
      emits('ok')
      message.success('修改成功')
    }
    handleCancel()
  }

  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
