<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="绑定搜索资源"
    :width="900"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-tree
      v-model:checkedKeys="checkedKeys"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :autoExpandParent="autoExpandParent"
      checkable
      :field-names="{
        title: 'name',
        key: 'id',
      }"
      :height="450"
      :show-icon="true"
      :show-line="{ showLeafIcon: false }"
      :tree-data="treeData"
    />
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { getQbSearches } from '@/api/试卷/搜索资源'
  import { editQbColumns } from '@/api/试卷/栏目'
  import { uniqueArrays } from '@/utils'

  // 搜索框label
  const id = ref()
  const setId = (value) => {
    id.value = value
  }
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    name: '',
  })
  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = () => {
    treeData.value = []
    expandedKeys.value = []
    selectedKeys.value = []
    checkedKeys.value = []
    copy_checkKeys.value = []
    init()
    handleChecked()
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { uniqueToFirstArray, uniqueToSecondArray } = uniqueArrays(
      checkedKeys.value,
      copy_checkKeys.value
    )
    const { code } = await editQbColumns(
      {
        qbSearches: {
          sync: uniqueToFirstArray.map((item) => {
            return {
              id: item,
            }
          }),
          del: uniqueToSecondArray.map((item) => {
            return {
              id: item,
            }
          }),
        },
      },
      id.value
    )
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }
  const treeData = ref([])
  const expandedKeys = ref([])
  const selectedKeys = ref([])
  const checkedKeys = ref([])
  const copy_checkKeys = ref([])
  // 是否自动展开
  const autoExpandParent = ref(true)
  const init = async () => {
    const { data } = await getQbSearches({
      parent_id: 0,
    })
    treeData.value = data
  }

  const generateList = (data) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i]
      checkedKeys.value.push(node.id)
      if (node.children) {
        generateList(node.children)
      }
    }
  }
  const handleChecked = async () => {
    const { data } = await getQbSearches({
      parent_id: 0,
      qb_column_id: id.value,
    })
    generateList(data)
    copy_checkKeys.value = checkedKeys.value
  }
  defineExpose({
    showModal,
    formState,
    setId,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
