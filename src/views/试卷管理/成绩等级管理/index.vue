<template>
  <div>
    <!-- <a-flex align="center" justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10" justify="flex-end" wrap="wrap">
          <a-form-item label="等级">
            <a-input-search
              v-model:value="formState.standard_name"
              allow-clear
              enter-button
              placeholder="输入等级快速查询"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex> -->
    <!-- <a-divider style="height: 3px; background-color: #eeeeee" /> -->
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">成绩等级列表</div>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      @change="handleTableChange"
      class="mt-20"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>
            <!-- 删除 -->
            <!-- <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm> -->
          </a-space>
        </template>
      </template>
    </a-table>
    <CreateModal ref="createModalRef" @ok="init" />
    <EditModal ref="editModalRef" @ok="init" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { routerReplace } from '~/src/utils/routes'
  import { 
    getGradeStandards, 
    createGradeStandard, 
    updateGradeStandard, 
    deleteGradeStandard 
  } from '@/api/试卷/成绩管理/index'

  import CreateModal from './components/添加等级'
  import EditModal from './components/编辑等级'

  const route = useRoute()
  
  // 搜索表单
  const formState = reactive({
    standard_name: route.query?.standard_name,
  })

  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const dataSource = ref([])
  const total = ref(0)
  const loading = ref(false)

  const handleGradeStandards = async (params = {}) => {
    try {
      loading.value = true
      const { data } = await getGradeStandards({
        page: current.value,
        pageSize: pageSize.value,
        ...params
      })
      return data   
    } catch (error) {
      console.error('获取数据失败:', error)
      return []
    } finally {
      loading.value = false
    }
  }

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '等级',
      dataIndex: 'standard_name',
      width: 120,
    },
    {
      title: '成绩区间',
      dataIndex: 'score_range',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 200,
    },
  ]

  const current = ref(1)
  const pageSize = ref(10)

  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: current.value,
      pageSize: pageSize.value,
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    current.value = pag?.current || 1
    pageSize.value = pag?.pageSize || 10
    init({
        page: pag?.current || 1,
        pageSize: pag?.pageSize || 10,
    })
  }

  const init = async (params = {}) => {
    const res = await handleGradeStandards(params)
    
    if (res) {
      // 处理API返回的数据结构
      const data = res
      
      dataSource.value = data.map((item) => {
        return {
          ...item,
          grade: item.standard_name, // 映射字段名
          score_range: item.score_range
        }
      })
      total.value = data.length
    }
    routerReplace(pagination.value, {
      standard_name: formState.standard_name,
    })
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 创建-弹窗
  const createModalRef = ref('')
  const onClick = () => {
    createModalRef.value?.showModal()
  }

  // 删除
  const onDelete = async (id) => {
    try {
      const { code } = await deleteGradeStandard(id)
      if (code === 200) {
        init()
        message.success('删除成功')
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  init()
</script>

<style lang="scss" scoped>
</style>