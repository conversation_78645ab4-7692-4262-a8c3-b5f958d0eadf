<template>
  <a-modal
    v-model:open="visible"
    :confirm-loading="confirmLoading"
    title="添加等级"
    width="500px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="等级名称" name="standard_name">
        <a-input
          v-model:value="formState.standard_name"
          placeholder="请输入等级名称（如：A、B、C、D）"
          maxlength="50"
        />
      </a-form-item>
      <a-form-item label="成绩区间" name="score_range" required>
        <a-flex gap="8" align="center">
          <a-input-number
            v-model:value="formState.min_score"
            :min="0"
            :max="100"
            placeholder="最小值"
            style="width: 120px"
          />
          <span>~</span>
          <a-form-item-rest>
            <a-input-number
              v-model:value="formState.max_score"
              :min="0"
              :max="100"
              placeholder="最大值"
              style="width: 120px"
            />
          </a-form-item-rest>
        </a-flex>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'
  import { createGradeStandard } from '@/api/试卷/成绩管理/index'

  const emit = defineEmits(['ok'])

  const visible = ref(false)
  const confirmLoading = ref(false)
  const formRef = ref()

  const formState = reactive({
    standard_name: '',
    min_score: null,
    max_score: null,
  })

  const rules = {
    standard_name: [
      { required: true, message: '请输入等级名称', trigger: 'blur' },
      { min: 1, max: 50, message: '等级名称长度在1到50个字符', trigger: 'blur' },
    ],
    score_range: [
      { validator: validateScoreRange, trigger: 'change blur' },
    ],
  }

  // 判断是否为有效数字
  const isValidNumber = (value) => {
    return value !== null && value !== undefined && value !== '' && !isNaN(value) && value >= 0
  }

  // 自定义验证器
  function validateScoreRange() {
    const hasMin = isValidNumber(formState.min_score)
    const hasMax = isValidNumber(formState.max_score)
    
    // 输入了内容但无效
    if ((formState.min_score != null && formState.min_score !== '' && !hasMin) ||
        (formState.max_score != null && formState.max_score !== '' && !hasMax)) {
      return Promise.reject('请输入数字')
    }
    
    // 两个值都有效时检查大小关系
    if (hasMin && hasMax) {
      if (formState.min_score > formState.max_score) {
        return Promise.reject('最小值必须小于等于最大值')
      }
    }
    
    return Promise.resolve()
  }

  const showModal = () => {
    visible.value = true
    // 重置表单
    formState.standard_name = ''
    formState.min_score = null
    formState.max_score = null
    formRef.value?.clearValidate()
  }

  const handleOk = async () => {
    try {
      await formRef.value.validate()
      confirmLoading.value = true
      
      // 调用API创建成绩等级
      const { code } = await createGradeStandard({
        standard_name: formState.standard_name,
        min_score: formState.min_score,
        max_score: formState.max_score
      })
      
      if (code === 200) {
        message.success('添加成功')
        visible.value = false
        emit('ok')
      }
    } catch (error) {
      console.error('创建失败:', error)
      message.error('创建失败')
    } finally {
      confirmLoading.value = false
    }
  }

  const handleCancel = () => {
    visible.value = false
  }

  defineExpose({
    showModal,
  })
</script>

<style lang="scss" scoped>
</style>
