<template>
  <a-modal
    v-model:open="visible"
    :confirm-loading="confirmLoading"
    :ok-button-props="{ disabled: !isFormValid }"
    title="编辑等级"
    width="500px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="等级名称">
        <span
        >
        {{ formState.grade }}
        </span>
      </a-form-item>
      <a-form-item label="成绩区间" name="score_range" :rules="rules.score_range">
        <a-flex gap="8" align="center">
          <a-input-number
            v-model:value="formState.min_score"
            :min="0"
            :max="100"
            placeholder="最小值"
            style="width: 120px"
            @change="handleScoreChange"
            @blur="handleScoreBlur"
          />
          <span>~</span>
          <a-form-item-rest>
            <a-input-number
              v-model:value="formState.max_score"
              :min="0"
              :max="100"
              placeholder="最大值"
              style="width: 120px"
              @change="handleScoreChange"
              @blur="handleScoreBlur"
            />
          </a-form-item-rest>
        </a-flex>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { getGradeStandard, updateGradeStandard } from '@/api/试卷/成绩管理/index'

  const emit = defineEmits(['ok'])

  const visible = ref(false)
  const confirmLoading = ref(false)
  const formRef = ref()
  const currentId = ref(null)

  const formState = reactive({
    grade: '',
    min_score: null,
    max_score: null,
  })

  // 判断是否为有效数字
  const isValidNumber = (value) => {
    return value !== null && value !== undefined && value !== '' && !isNaN(value) && value >= 0
  }

  // 计算表单是否有效
  const isFormValid = computed(() => {
    return isValidNumber(formState.min_score) && 
           isValidNumber(formState.max_score) && 
           formState.min_score <= formState.max_score
  })

  const rules = {
    score_range: [
      { validator: validateScoreRange, trigger: 'change blur' },
    ],
  }

  // 自定义验证器
  function validateScoreRange() {
    const hasMin = isValidNumber(formState.min_score)
    const hasMax = isValidNumber(formState.max_score)
    
    // 输入了内容但无效
    if ((formState.min_score != null && formState.min_score !== '' && !hasMin) ||
        (formState.max_score != null && formState.max_score !== '' && !hasMax)) {
      return Promise.reject('请输入数字')
    }
    
    // 两个值都有效时检查大小关系
    if (hasMin && hasMax) {
      if (formState.min_score > formState.max_score) {
        return Promise.reject('最小值必须小于等于最大值')
      }
    }
    
    return Promise.resolve()
  }

  const showModal = () => {
    visible.value = true
  }

  // 处理分数变化事件
  const handleScoreChange = () => {
    // 触发表单验证
    formRef.value?.validateFields(['score_range'])
  }

  // 处理分数失焦事件
  const handleScoreBlur = () => {
    // 触发表单验证
    formRef.value?.validateFields(['score_range'])
  }
  
  const setId = async (id) => {
    currentId.value = id
    try {
      // 根据ID获取数据
      const { code, data } = await getGradeStandard(id)
      if (code === 200 && data) {
        formState.grade = data.standard_name
        formState.min_score = data.min_score
        formState.max_score = data.max_score
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      message.error('获取数据失败')
    }
  }

  const handleOk = async () => {
    try {
      confirmLoading.value = true
      
      console.log(formState)
      // 调用API更新成绩等级
      const { code } = await updateGradeStandard(currentId.value, {
        min_score: formState.min_score,
        max_score: formState.max_score
      })
      
      if (code === 200) {
        message.success('编辑成功')
        visible.value = false
        emit('ok')
      }
    } catch (error) {
      console.error('更新失败:', error)
      message.error('更新失败')
    } finally {
      confirmLoading.value = false
    }
  }

  const handleCancel = () => {
    visible.value = false
  }

  defineExpose({
    showModal,
    setId,
  })
</script>

<style lang="scss" scoped>
  // 自定义错误提示样式
  :deep(.ant-form-item-explain-error) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 12px;
    font-weight: 400;
    color: #ff4d4f;
    line-height: 1.5715;
  }
  
</style>
