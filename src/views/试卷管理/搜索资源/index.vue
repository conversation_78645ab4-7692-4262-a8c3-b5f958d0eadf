<template>
  <div>
    <a-flex justify="space-between">
      <a-flex align="center" gap="10">
        <div><ReadOutlined /></div>
        <div class="ls-3 font-bold">{{ route.meta.title }}</div>
      </a-flex>
      <a-form>
        <a-flex gap="10">
          <a-form-item :label="label">
            <a-input-search
              v-model:value="formState.name"
              allow-clear
              enter-button
              :placeholder="`输入${label}快速查询`"
              @pressEnter="handleSearch"
              @search="handleSearch"
            />
          </a-form-item>
        </a-flex>
      </a-form>
    </a-flex>
    <a-divider style="height: 3px; background-color: #eeeeee" />
    <a-flex align="center" class="py-10" gap="10">
      <DefaultDivider />
      <div class="font-bold">搜索资源列表</div>
    </a-flex>
    <a-flex class="py-10" gap="10">
      <a-button type="primary" @click="onClick">添加搜索资源</a-button>
    </a-flex>
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="pagination"
      rowKey="id"
      tableLayout="fixed"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="['name', 'code'].includes(column.dataIndex)">
          <a-tag color="blue">
            {{ text }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a-space :size="10">
            <a-button type="primary" @click="edit(record.id)">编辑</a-button>
            <a-popconfirm
              cancel-text="否"
              ok-text="是"
              title="是否确定删除?"
              @confirm="onDelete(record.id)"
            >
              <a-button danger type="primary">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
    <CreateModal ref="createModalRef" @ok="init" />
    <EditModal ref="editModalRef" @ok="init" />
  </div>
</template>

<script setup>
  import { ReadOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { computed, reactive, ref } from 'vue'
  import { usePagination } from 'vue-request'
  import { useRoute } from 'vue-router'

  import { deleteQbSearches, getQbSearches } from '@/api/试卷/搜索资源'
  import { setEmptyChildrenToUndefined } from '@/utils/index'
  import { routerReplace } from '~/src/utils/routes'

  import EditModal from './components/修改资源'
  import CreateModal from './components/添加资源'

  const route = useRoute()
  // 搜索框label
  const label = '属性资源名称'
  // 搜索表单
  const formState = reactive({
    name: route.query?.name,
  })
  // 搜索
  const handleSearch = () => {
    pagination.value.current = 1
    init()
  }

  const { data, run, total, loading, current, pageSize } =
    usePagination(getQbSearches)

  const dataSource = computed(() => {
    if (!data.value) return []
    return data.value.data.map((item) => {
      return {
        ...item,
        children: setEmptyChildrenToUndefined(item.children),
      }
    })
  })
  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
    },
    {
      title: label,
      dataIndex: 'name',
    },
    {
      title: '资源code名称',
      dataIndex: 'code',
    },
    {
      title: '排序权重',
      dataIndex: 'orderby',
    },
    {
      title: '操作',
      dataIndex: 'action',
    },
  ]
  const pagination = computed(() => {
    return {
      showSizeChanger: true,
      showQuickJumper: true,
      current: parseInt(route.query?.current ?? current.value),
      pageSize: parseInt(route.query?.pageSize ?? pageSize.value),
      total: total.value,
      showTotal: (total) => {
        return `总共${total}条`
      },
    }
  })

  const handleTableChange = (pag) => {
    pagination.value.current = pag?.current
    pagination.value.pageSize = pag?.pageSize
    init()
  }
  const init = () => {
    run({
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      parent_id: 0,
      name: formState.name,
      orders: [
        {
          column: 'orderby',
          direction: 'desc',
        },
        {
          column: 'id',
          direction: 'desc',
        },
      ],
    })
    routerReplace(pagination.value, {
      name: formState.name,
    })
  }

  // 编辑-弹窗
  const editModalRef = ref('')
  // 编辑
  const edit = (id) => {
    editModalRef.value.setId(id)
    editModalRef.value?.showModal()
  }

  // 创建-弹窗
  const createModalRef = ref('')
  const onClick = () => {
    createModalRef.value?.showModal()
  }

  // 删除
  const onDelete = async (id) => {
    const { code } = await deleteQbSearches(id)
    if (code == 200) {
      init()
      message.success('删除成功')
    }
  }

  init()
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
