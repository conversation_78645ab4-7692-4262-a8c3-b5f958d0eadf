<template>
  <a-modal
    v-model:open="open"
    :destroyOnClose="true"
    title="新建资源"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formState">
      <a-form-item
        :label="label1"
        name="name"
        :rules="[{ required: true, message: `请输入${label1}!` }]"
      >
        <a-input
          v-model:value="formState.name"
          :placeholder="`请输入${label1}`"
        />
      </a-form-item>
      <a-form-item
        v-if="formState.parent_id[0] == 0"
        :label="label2"
        name="code"
        :rules="[{ required: true, message: `请输入${label2}!` }]"
      >
        <a-input
          v-model:value="formState.code"
          :placeholder="`请输入${label2}`"
        />
      </a-form-item>
      <a-form-item
        :label="label3"
        name="parent_id"
        :rules="[{ required: true, message: `请输入${label3}!` }]"
      >
        <a-cascader
          v-model:value="formState.parent_id"
          change-on-select
          :fieldNames="{
            label: 'name',
            value: 'id',
          }"
          :options="options"
          :placeholder="`请输入${label3}`"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { message } from 'ant-design-vue'
  import { reactive, ref } from 'vue'

  import { createQbSearches, getQbSearches } from '@/api/试卷/搜索资源'

  const label1 = '属性资源名称'
  const label2 = '资源code名称'
  const label3 = '绑定父级资源'
  const emits = defineEmits(['cancel', 'ok'])
  const formRef = ref()
  const formState = reactive({
    name: '',
    code: '',
    parent_id: [0],
  })

  const options = ref([])
  const handleOptions = async () => {
    const { data } = await getQbSearches({
      parent_id: 0,
    })
    options.value = data
    options.value.unshift({
      name: '顶级资源',
      id: 0,
    })
  }
  // handleOptions()

  const open = ref(false)
  const handleCancel = () => {
    formRef.value?.resetFields()
    open.value = false
    emits('cancel')
  }
  const showModal = () => {
    handleOptions()
    open.value = true
  }
  const handleOk = async () => {
    await formRef.value?.validate()
    const { code } = await createQbSearches({
      name: formState.name,
      code: formState.parent_id[0] == 0 ? formState.code : undefined,
      parent_id: formState.parent_id[formState.parent_id.length - 1],
    })
    if (code == 200) {
      emits('ok')
      message.success('创建成功')
    }
    handleCancel()
  }

  defineExpose({
    showModal,
    formState,
  })
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
