<template>
  <a-upload
    v-model:file-list="fileList"
    :accept="accept || `image/*,application/pdf`"
    :before-upload="beforeUpload"
    :customRequest="customRequest"
    :list-type="listType ? listType : 'picture-card'"
    :max-count="1"
    @remove="fileRemove"
  >
    <template v-if="['text'].includes(listType)">
      <a-button>
        <plus-outlined />
        上传
      </a-button>
    </template>
    <template v-else>
      <div>
        <plus-outlined />
        上传
      </div>
    </template>
  </a-upload>
  <!-- <img alt="example" :src="previewImage" style="width: 100%" /> -->
</template>

<script setup>
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { message } from 'ant-design-vue'
  import { onMounted, ref, toRefs } from 'vue'

  import { uploadToOSS } from '@/utils/ossUploader'

  const props = defineProps(['listType', 'uploadType', 'accept'])
  const { listType, uploadType, accept } = toRefs(props)

  const emits = defineEmits(['init', 'setImageAddress'])

  // 图片上传加载状态
  const loading = ref(false)
  // 图片上传列表
  const fileList = ref([])
  const fileType = uploadType.value || [
    'image/png',
    'image/jpeg',
    'application/pdf',
  ]
  // 上传前事件
  const beforeUpload = async (file) => {
    const isJpgOrPng = fileType.includes(file.type)
    if (!isJpgOrPng) {
      message.error('上传文件格式错误')
    }
    return isJpgOrPng
  }

  // 删除事件
  const fileRemove = (file) => {
    const index = fileList.value.indexOf(file)
    const newFileList = fileList.value.slice()
    newFileList.splice(index, 1)
    fileList.value = newFileList
  }

  // 上传行为事件
  const customRequest = async (options) => {
    loading.value = true
    uploadToOSS(
      fileList.value,
      (result, file) => {
        loading.value = false
        emits('setImageAddress', result.name)
        options.onSuccess(result, file)
        message.success('上传成功')
      },
      (error) => {
        loading.value = false
        options.onError('', error)
      }
    )
  }

  const init = async () => {
    emits('init')
  }
  onMounted(() => {
    init()
  })

  defineExpose({
    fileList,
    loading,
  })
</script>

<script setup></script>

<style lang="scss" scoped>
  .default {
  }
</style>
