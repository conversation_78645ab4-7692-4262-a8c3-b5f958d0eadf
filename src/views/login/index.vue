<template>
  <div class="login-container">
    <a-row>
      <a-col :lg="14" :md="0" :sm="12" :xl="7" :xs="0" />
      <a-col :lg="10" :md="12" :sm="24" :xl="10" :xs="24">
        <div class="login-container-form">
          <div class="login-container-title">
            <p class="font-family font-size40">
              {{ title }}
              <span class="font-family font-size24">{{ titleVersion }}</span>
            </p>
            <p class="font-arial">{{ titleEnglish }}</p>
          </div>
          <a-form class="form" :model="form" @submit.prevent="onSubmit">
            <a-form-item>
              <a-input
                v-model:value="form.username"
                class="form-item"
                placeholder="Username"
              >
                <template v-slot:prefix>
                  <UserOutlined style="color: rgba(0, 0, 0, 0.25)" />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item>
              <a-input
                v-model:value="form.password"
                class="form-item"
                placeholder="Password"
                type="password"
              >
                <template v-slot:prefix>
                  <LockOutlined style="color: rgba(0, 0, 0, 0.25)" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item>
              <a-flex align="center" gap="20">
                <a-input
                  v-model:value="form.captcha"
                  class="form-item"
                  placeholder="验证码"
                  @focus="onFocus"
                />
                <a-image
                  fallback="data:image/png;base64,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"
                  :height="50"
                  :preview="false"
                  :src="src"
                  style="cursor: pointer"
                  :width="120"
                  @click="onClick"
                />
              </a-flex>
            </a-form-item>
            <a-form-item>
              <a-button
                :disabled="
                  form.username === '' ||
                  form.password === '' ||
                  form.captcha === ''
                "
                html-type="submit"
                type="primary"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </a-col>
    </a-row>
    <div class="login-container-tips">
      <span class="login-container-tips-span">{{ copyright }}</span>
      <p class="login-container-tips-span">
        {{ titleEnglish }} {{ titleVersion }}
      </p>
    </div>
  </div>
</template>
<script setup>
  import { LockOutlined, UserOutlined } from '@ant-design/icons-vue'
  import { debounce } from 'lodash-es'
  import { computed, reactive, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  // import { dependencies, devDependencies } from '*/package.json'
  import { useStore } from 'vuex'

  import { getCaptcha } from '@/api/user'
  import { copyright, debounceTime, titleEnglish, titleVersion } from '@/config'
  const store = useStore()
  const router = useRouter()
  const route = useRoute()
  const form = reactive({
    username: '',
    password: '',
    key: '',
    captcha: '',
  })

  const redirect = ref('/')
  const title = computed(() => store.getters['settings/title'])
  const handleRoute = () => {
    return redirect.value === '/404' || redirect.value === '/403'
      ? '/'
      : redirect.value
  }
  const onSubmit = debounce(async () => {
    try {
      await store.dispatch('user/login', form)
    } catch (error) {
      console.log(error)
      getCaptchaData()
    }
    router.push(handleRoute())
  }, debounceTime)

  // 验证码
  const src = ref('')
  const lock = ref(false)
  const getCaptchaData = async () => {
    const { img, key } = await getCaptcha()
    src.value = img
    form.key = key
  }
  setTimeout(() => {
    onFocus()
  }, 5000)
  const onFocus = debounce(async () => {
    if (lock.value) return
    lock.value = true
    getCaptchaData()
  }, debounceTime)
  const onClick = debounce(async () => {
    getCaptchaData()
  }, debounceTime)

  watch(
    router.currentRoute,
    () => {
      redirect.value = route.query?.redirect ?? '/'
    },
    { immediate: true, deep: true }
  )
</script>
<style lang="less">
  .login-container {
    width: 100%;
    height: 100vh;
    background: url('~@/assets/login_images/login_background.jpg');
    background-size: cover;
    &-form {
      margin-top: calc((100vh - 380px) / 2);
      width: calc(100%);
      // height: 200px;
      // margin-top: calc((100vh - 380px) / 2);
      // width: calc(100% - 40px);
      // margin-left: 40px;
    }
    &-hello {
      font-size: 32px;
      color: #fff;
    }
    &-title {
      margin-bottom: 30px;
      font-size: 20px;
      color: #fff;
      text-align: center;
      width: calc(100%);
    }
    .font-family {
      font-family: '思源黑体';
      user-select: none;
    }
    .font-size40 {
      font-size: 2.5rem;
      letter-spacing: 2px;
      text-shadow: 1px 1px 6px #00000059;
      margin-bottom: 0;
    }
    .font-size24 {
      font-size: 1.5rem;
      text-shadow: 1px 1px 6px #00000059;
    }
    .font-arial {
      font-family: Arial;
      text-shadow: 1px 1px 6px #00000059;
      word-spacing: 5px;
      letter-spacing: 12px;
      user-select: none;
    }
    .form {
      width: calc(59%);
      margin: 0 auto;
    }
    &-tips {
      position: fixed;
      bottom: @vab-margin;
      width: 100%;
      height: 40px;
      color: rgba(255, 255, 255, 0.856);
      text-align: center;
    }
    .login-container-tips-span {
      text-shadow: 2px 2px 1px #00000042;
    }
    .form-item {
      border-radius: 45px;
    }
    .ant-col {
      width: 100%;
      padding: 0 10px 0 10px;
    }
    .ant-input {
      height: 35px;
    }
    .ant-btn {
      width: 100%;
      height: 45px;
      border-radius: 99px;
    }
  }
</style>
