<template>
  <div class="container" v-loading="loading" v-if="!isPublicLibrary">
    <div class="left">
      <span class="title">知识点列表</span>
      <el-form :model="leftFormData" :rules="leftFormRules" ref="leftFormRef" :inline="true" class=""
        label-width="120px" label-position="top" style="margin-top: 20px;">
        <el-row style="width:200px;">

          <el-col :span="24" :lg="24" :sm="24" :xs="24">
            <el-input v-model="filterText" class="w-60 mb-2" placeholder="搜索" clearable style="margin-bottom:15px;">
              <template #append>
                <el-button type="primary" icon="el-icon-search"></el-button>
              </template>
            </el-input>
          </el-col>

          <el-col :span="24" :lg="24" :sm="24" :xs="24">
            <el-form-item label="学段-科目" prop="gradeSubject" style="width:200px">
              <el-cascader v-model="leftFormData.gradeSubject" :options="gradeSubjectOptions"
                @change="onChangeGradeSubject" clearable placeholder="请选择学段学科" />
            </el-form-item>
          </el-col>

          <el-col :span="24" :lg="24" :sm="24" :xs="24">
            <el-form-item label="教材-教材年级" prop="bookBookgrade" style="width:200px">
              <el-cascader v-model="leftFormData.bookBookgrade" :options="bookBookgradeOptions"
                @change="onChangeBookBookgrade" clearable placeholder="请选择教材年级">
                <template #empty>
                  {{ leftFormData.gradeSubject ? "无数据" : "请先选择学段学科" }}
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>

          <el-col :span="24" :lg="24" :sm="24" :xs="24">
            <el-form-item prop="search">
              <el-tree ref="treeRef" style="width:200px;margin-top: 12px;" class="filter-tree custom-tree"
                :data="treeData" :filter-node-method="filterNode" node-key="id" :default-expand-all="false"
                :current-node-key="currSelectedTreeItemId" highlight-current @current-change="onTreeDataChange"
                :empty-text="leftFormData.bookBookgrade ? '无数据' : '请先选择教材-教材年级'">
              </el-tree>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
    </div>
    <div class="right">
      <div class="guideBar">{{ `我的备课资料 >${guideBar}` }}</div>
      <el-form :model="tableForm" :inline="true" class="search-box" label-width="80px" style="margin-top:15px;">
        <el-form-item label="" style="width:100%; color:black;" prop="name">
          <div class="row" style="width: 100%;display:flex;align-items: center; justify-content: space-between;">
            <div class="btn">
              <el-button class="t-l-btn" color="#1677FF" @click="onUploadFileClick"
                icon="el-icon-upload">上传文件</el-button>
              <el-button class="t-l-btn" color="#1677FF" @click="onClickEnterPublicBase">进入公共资料库</el-button>
            </div>

            <el-input v-model="tableForm.name" placeholder="搜索我的备课资料" style="width: 220px;" clearable
              @clear="resetQuery" @keyup.enter="getList">
              <template #append>
                <el-button @click="getList" icon="el-icon-search"
                  style="background-color: transparent; color:black"></el-button>
              </template>
            </el-input>
          </div>
        </el-form-item>
      </el-form>
      <div class="fileList">
        <div class="file-item" v-for="item in tableData" :key="item.id">
          <div class="file-icon">
            <img :src="pdfIcon" style="background-color: transparent;width: 60px;height: 60px;"
              v-if="item.fileType === 'pdf'"></img>
            <img :src="wordIcon" style="background-color: transparent;width: 60px;height: 60px;" v-else></img>
          </div>
          <div class="file-info">
            <div class="file-name">{{ item.fileName }}</div>
            <div class="bottomInfo">
              <div class="file-type">{{ item.type == 1 ? "课件" : "教案" }}</div>
              <div class="file-desc">
                <div class="icon"></div>
                <div class="text">{{ item.createTime }}</div>
              </div>
              <div class="downloadBtn" @click.stop="handleDownload(item)"></div>
            </div>
          </div>
          <div class="file-actions">
            <el-button class="t-r-l-btn" v-if="item.status?.code === 1" type="danger"
              style="border-radius:5px; width:90px;" @click="uploadToLibrary(item)">上传资料库</el-button>
            <div v-else-if="item.status?.code === 2" class="wait">待审核</div>
            <div v-else-if="item.status?.code === 3" class="succ">上传成功</div>
            <div v-else-if="item.status?.code === 4" class="fail">被驳回</div>

            <el-button class="t-d-btn" type="danger" style="width: 60px;
            height: 32px;border-radius:5px; margin-left:0px;" @click="handleDelete(item)">删除</el-button>
          </div>
        </div>
      </div>

      <div style="display: flex; justify-content: flex-end;">
        <!-- 生成教案书按钮 -->
        <el-button class="t-l-btn" color="#1677FF" @click="onClickGenerateBookBtn"
          style="margin-top:15px; border-radius: 5px;">生成教案书</el-button>
      </div>

      <el-pagination v-model:page-size="tableForm.pageSize" v-model:current-page="tableForm.pageNum"
        :page-sizes="pageSizes" :total="total" @size-change="() => getList()" @current-change="() => getList()"
        layout="total, sizes, ->, prev, pager, next, jumper" style="margin-top: 20px"></el-pagination>
    </div>

    <!-- 生成教案书弹窗 -->
    <el-dialog v-model="generateBookDialogVisible" title="生成教案书" width="80%" style="height:80%;overflow-y: auto;">
      <div class="generateBookDialogContent">
        <div class="left">
          <el-tree ref="dialogTreeRef" style="width:200px;margin-top: 12px;" class="filter-tree custom-tree"
            :data="treeData" node-key="id" :default-expand-all="false" highlight-current
            :current-node-key="dialogCurrSelectedTreeItemId" @current-change="onDialogTreeDataChange">
          </el-tree>
        </div>
        <div class="right">
          <div class="file-list-container">
            <div class="file-item" v-for="item in dialogTableData" :key="item.id"
              @click="item.checked = !item.checked; updateSelectedMaterials(item)">
              <el-checkbox class="file-checkbox" v-model="item.checked" @click.stop=""
                @change="updateSelectedMaterials(item)"></el-checkbox>
              <div class="file-icon">
                <img :src="pdfIcon" style="background-color: transparent;width: 80px;height: 80px;"
                  v-if="item.fileType === 'pdf'"></img>
                <img :src="wordIcon" style="background-color: transparent;width: 80px;height: 80px;" v-else></img>
              </div>
              <div class="file-info">
                <div class="file-name">{{ item.fileName }}</div>
                <div class="bottomInfo">
                  <div class="file-type">{{ item.type == 2 ? "教案" : "课件" }}</div>
                  <div class="file-desc">
                    <div class="icon"></div>
                    <div class="text">{{ item.createTime }}</div>
                  </div>
                  <div class="downloadBtn" @click.stop="handleDownload(item)"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="selected-materials">
            <div class="selected-title">已选材料：{{ selectedMaterialsCount }}个</div>
            <div v-if="selectedMaterialsCount === 0"
              style="color: #909399; text-align: center; background-color: transparent">
              暂无选中的材料
            </div>
            <div class="selected-items" v-else>
              <div class="selected-item" v-for="item in selectedMaterials" :key="item.id">
                <div class="selected-item-name">{{ item.fileName }}</div>
                <el-button @click="delSelectedMaterial(item)"
                  style="border:none; width: 15px; height: 15px; background-color: transparent;" icon="el-icon-close"
                  circle></el-button>
              </div>
            </div>
          </div>

        </div>

      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="generateBookDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onGenerateBookConfirm">生成</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传文件弹窗 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" style="width: 50%;">
      <el-upload style="display: inline-block;margin-left: 10px;width: 80%;" drag :on-change="onFileChange" :limit="1"
        v-model:file-list="fileList" :auto-upload="false" accept=".pdf,.docx">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <!-- <div class="upload-tip">支持jpg/png/pdf/doc/docx/xlsx格式文件，且不超过10MB</div> -->
      </el-upload>
      <div style="margin:15px 0 5px 0;">类型</div>
      <el-select v-model="uploadType" placeholder="Select" style="width: 150px;">
        <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <template #footer>
        <span style="display: flex;justify-content: flex-end;gap: 10px;">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button type="primary" @click="submitUpload">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- PDF预览弹窗 -->
    <el-dialog v-model="pdfDialogVisible" title="" :before-close="handlePdfDialogClose" destroy-on-close
      style="width:90%;height:80%;">
      <!-- <PdfPreview :currentPdfUrl="currentPdfUrl" :spliceUrl="spliceUrl">
      </PdfPreview> -->

      <FilePreview :fileUrl="currentPdfUrl" :spliceUrl="spliceUrl" :fileType="currentFileType" />

      <div class="bottomBtn">
        <el-button @click="handleDownloadByUrl(currentPdfUrl)" color="#1677FF" icon="el-icon-download">下载教案书</el-button>
      </div>

    </el-dialog>
  </div>

</template>

<script setup>
import {
  ref,
  getCurrentInstance,
  reactive,
  onMounted,
  onUnmounted,
  computed,
  nextTick,
  watch
} from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, ArrowRight, Operation, Folder, FolderOpened, Document, Plus, Minus } from '@element-plus/icons-vue'
// import { Search } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import adminQueTabs from '@/api/model/adminQueTabs'
import adminSubjects from '@/api/model/adminSubjects';
import adminMyLessonPreparation from '@/api/model/adminMyLessonPreparation'
// import questionManager from '@/api/model/questionManager';

import PdfPreview from '@/components/PdfPreview.vue'

import pdfIcon from '@/assets/pdfIcon.png'
import wordIcon from '@/assets/wordIcon.png'
import pptIcon from '@/assets/pptIcon.png'

import FilePreview from '@/components/FilePreview.vue'

const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

const loading = ref(true)

// 分页
const total = ref(0);
const pageSizes = [5, 10, 20];
let defForm = { pageNum: 1, pageSize: pageSizes[1], name: null }
// 表单数据
const tableForm = reactive({ ...defForm })

// 左侧
let defLeftFormData = { gradeSubject: null, bookBookgrade: null, search: null }
const leftFormData = reactive({ ...defLeftFormData })
const leftFormRef = ref(null)

// 树形及筛选
const filterText = ref('')
const treeRef = ref(null)

const defaultProps = {
  children: 'children',
  label: 'label',
}

watch(filterText, (val) => {
  treeRef.value.filter(val)
})

const filterNode = (value, data) => {
  if (!value) return true
  return data.label.includes(value)
}


// 学段学科级联选择值
// const gradeSubjectOptions = ref([
//   {
//     value: '1',
//     label: '初中',
//     children: [
//       {
//         value: 'phycisc',
//         label: '物理',
//       },
//     ]
//   },
// ])
const gradeSubjectOptions = ref([])

// 教材年级级联选择值
// const bookBookgradeOptions = ref([
//   {
//     value: '1',
//     label: '人教版',
//     children: [
//       {
//         value: 'math1',
//         label: '数学必修1',
//       },
//     ]
//   },
// ])
const bookBookgradeOptions = ref([])

const leftFormRules = reactive({
  gradeSubject: [
    { required: true, message: '请选择学段学科', trigger: 'change' },
  ],
  bookBookgrade: [
    { required: true, message: '请选择教材年级', trigger: 'change' },
  ],
})


// const treeData = ref([
//   {
//     id: '3',
//     label: '第三章 铁 金属材料',
//     children: [
//       {
//         id: '3-1',
//         label: '铁及其化合物',
//       },

//     ],
//   },
// ])
const treeData = ref([])
// 选择的树形数据
const selectedTreeData = ref({})
// 当前选择的属性数据的id
const currSelectedTreeItemId = ref('')
// 树形数据改变
const onTreeDataChange = async (val) => {
  // console.log("树形数据改变=====================")
  // console.log(val)

  selectedTreeData.value = val
  currSelectedTreeItemId.value = val.id

  await getList()
}

// 获取删除标记id列表
// const ids = ref([])
// const getIds = async () => {
//   const res = await questionManager.delTaglist.post()
//   // console.log("删除标记ids列表========================")
//   // console.log(res)

//   ids.value = res.data.join(',')
//   // console.log(ids.value)
// }

// 右侧 ==========================================
const guideBar = computed(() => {
  // let str = '我的备课资料'
  let str = ''
  if (leftFormData.gradeSubject && Array.isArray(leftFormData.gradeSubject) && leftFormData.gradeSubject.length === 2) {
    let gradeSubjectItem = getGradeSubjectItemBaseLawId(leftFormData.gradeSubject)
    if (gradeSubjectItem) {
      str += `${gradeSubjectItem[0].label}${gradeSubjectItem[1].label}`

      if (leftFormData.bookBookgrade && Array.isArray(leftFormData.bookBookgrade) && leftFormData.bookBookgrade.length === 2) {
        let bookBookgradeItem = getBookBookgradeItemBaseId(leftFormData.bookBookgrade)
        if (bookBookgradeItem) {
          str += ` >${bookBookgradeItem[0].label}${bookBookgradeItem[1].label}`

          if (selectedTreeData.value) {
            if (selectedTreeData.value.children) {
              // 父级直接获取
              str += ` >${selectedTreeData.value.label}`

            } else {
              // 子级还需获取父级
              str += ` >${selectedTreeData.value.parentLabel}`
              str += ` >${selectedTreeData.value.label}`
            }
          }
        }
      }
    }
  }
  return str
})

// 判断是什么类型的文件
const judgeFileType = (filePath) => {
  // console.log("文件路径=============")
  // console.log(filePath)

  if (!filePath) return 'docx'

  if (filePath.endsWith(".docx")) return 'docx'
  else if (filePath.endsWith(".pdf")) return 'pdf'
  else if (filePath.endsWith(".ppt")) return 'ppt'
  else return 'docx'
}

const isBatchDelBtnDisabled = computed(() => {
  return !tableSelection.value || tableSelection.value.length == 0
})

// 重置
const resetQuery = async () => {
  for (let key in tableForm) {
    tableForm[key] = defForm[key];
  }
  await getList();
}

// 搜索
const handleQuery = async () => {
  await getList();
}

// 获取接口列表
const getList = async (queryType = 0) => {
  // queryType:0 selectedTreeData.value, 1 dialogSelectedTreeData

  // 什么都不传时，默认传第一个学段和科目和栏目值处理
  // if (!leftFormData.gradeSubject || (Array.isArray(leftFormData.gradeSubject) && leftFormData.gradeSubject.length === 0)) {
  //   await selectFirstGradeSubjectBook()
  // }

  if (!leftFormData.gradeSubject || leftFormData.gradeSubject.length === 0) {
    await selectUserFirstGradeSubject()
  }
  // if (!leftFormData.bookBookgrade || leftFormData.bookBookgrade.length === 0) {
  //   await selectFirstBookBookgrade()
  // }
  // if (!selectedTreeData.value || !selectedTreeData.value.id) {

  // }

  // 传递的query参数
  let queryData = {
    page: tableForm.pageNum,
    pageSize: tableForm.pageSize,
    tabId: leftFormData.bookBookgrade?.[1],
    subjectId: leftFormData.gradeSubject?.[1],
    gradeId: leftFormData.gradeSubject?.[0],
    columnId: queryType === 0 ? selectedTreeData.value?.id : dialogSelectedTreeData.value?.id
  }

  if (tableForm.name) {
    queryData.fileName = tableForm.name
  }

  // if (queryData.gradeId === null || queryData.gradeId === undefined
  //   || queryData.subjectId === null || queryData.subjectId === undefined
  //   || queryData.columnId === null || queryData.columnId === undefined
  //   || queryData.queColumnsId === null || queryData.queColumnsId === undefined) {
  //   loading.value = false
  //   return
  // }

  // console.log("queryData================")
  // console.log(queryData)

  adminMyLessonPreparation.lessonPlanPage.post(queryData).then(res => {
    // console.log("分页查询结果=============")
    // console.log(res)

    res.data.records.forEach(k => {
      k.fileType = judgeFileType(k.filePath)
    })
    // console.log(res.data.records)

    if (queryType === 0) {
      tableData.value.splice(0);
      total.value = res.data?.total;
      if (res.status == 200) {
        tableData.value.push(...res.data.records)
      }
    } else {
      dialogTableData.value.splice(0);
      total.value = res.data?.total;
      if (res.status == 200) {
        res.data.records.forEach(i => {
          i.checked = false
        })
        dialogTableData.value.push(...res.data.records)
      }
    }
  }).catch(e => {
    console.log(e)
  }).finally(() => {
    loading.value = false
  })


}

// 获取学段学科列表
const getGradeSubjectList = async () => {
  let res = await adminSubjects.listGradeSubject.get()

  // console.log("学段学科列表====================================")
  // console.log(res)

  let options = res.data.map(i => {
    return {
      gradeId: i.gradeId,
      label: i.gradeName,
      value: i.lawGradeId,
      disabled: i.subjects ? false : true,
      children: i.subjects?.map(subject => {
        return {
          subjectId: subject.subjectId,
          label: subject.subjectName,
          value: subject.lawSubjectId
        }
      })
    }
  })
  // console.log(options)

  gradeSubjectOptions.value = [...options]
}

// 学段学科选择改变
const onChangeGradeSubject = async (val) => {
  // [lawGradeId, lawSubjectId]
  // console.log(val)

  leftFormData.bookBookgrade = null
  bookBookgradeOptions.value = []
  filterText.value = ''
  selectedTreeData.value = null
  currSelectedTreeItemId.value = ''
  treeData.value = []

  if (!val || !Array.isArray(val) || val.length !== 2) {
    // await selectFirstGradeSubjectBook()
    await getList()
    return
  }

  await getBookBookgradeList(val[0], val[1])
  await getList()
}

// 获取教材教材年级列表
const getBookBookgradeList = async (lewGradeid, lewSubjectid) => {
  let res = await adminMyLessonPreparation.treeList.post({ lewGradeid, lewSubjectid })

  console.log("教材教材年级列表====================================")
  console.log(res)

  let options = res.data.map(i => {
    return {
      value: i.id,  //教材id
      label: i.name,  //教材名
      lewGradeid,
      lewSubjectid,
      disabled: i.children ? false : true,
      children: i.children?.map(child => {
        return {
          value: child.id,  //教材类目id
          label: child.name,
        }
      })
    }
  })
  // console.log(options)

  bookBookgradeOptions.value = [...options]

}

// 教材 教材年级改变
const onChangeBookBookgrade = async (val) => {
  // [教材Id tabId, 教材类目Id]
  // console.log(val)

  filterText.value = ''
  selectedTreeData.value = null
  currSelectedTreeItemId.value = ""
  treeData.value = []

  if (!val || !Array.isArray(val) || val.length !== 2) {
    await getList()
    return
  }

  // 根据教材Id tabId 从 bookBookgradeOptions 中查找对应项从而获得 lewGradeid 和 lewSubjectid
  let selOptions = bookBookgradeOptions.value.find(i => i.value === val[0])
  if (!selOptions) return

  await getTreeData(selOptions.lewGradeid, selOptions.lewSubjectid, val[1])
  await getList()
}

// 获取具体教材版本及其内容
const getTreeData = async (lewGradeid, lewSubjectid, tabId) => {
  let res = await adminMyLessonPreparation.knowledgeList.post({
    lewGradeid,
    lewSubjectid,
    tabId
  })

  // console.log(res)

  let options = res.data.map(i => {
    return {
      id: i.id,
      label: i.name,
      disabled: !i.children,
      children: i.children?.map(child => {
        return {
          id: child.id,
          label: child.name,
          parentId: i.id,
          parentLabel: i.name,
        }
      })
    }
  })
  // console.log(options)

  treeData.value = [...options]
}

// 根据 leftFormData.gradeSubject 获取学段学科数据
const getGradeSubjectItemBaseLawId = (val) => {
  // [lawGradeId, lawSubjectId]
  // console.log(val)

  if (val === null || val === undefined || !Array.isArray(val) || val.length !== 2) return undefined

  const gradeItem = gradeSubjectOptions.value.find(i => i.value === val[0])

  const subjectItem = gradeItem?.children?.find(j => j.value === val[1])

  if (gradeItem === undefined || subjectItem === undefined) return []
  return [gradeItem, subjectItem]

}

// 根据 leftFormData.bookBookgrade 获取教材教材年级数据
const getBookBookgradeItemBaseId = (val) => {
  // [教材Id tabId, 教材类目Id]
  // console.log(val)

  if (val === null || val === undefined || !Array.isArray(val) || val.length !== 2) return undefined

  const bookItem = bookBookgradeOptions.value.find(i => i.value === val[0])

  const bookgradeItem = bookItem?.children?.find(j => j.value === val[1])

  if (bookItem === undefined || bookgradeItem === undefined) return []
  return [bookItem, bookgradeItem]

}

// 获取当前用户的学段学科列表
const currentUserGradeSubjectList = ref([])
const getCurrentUserGradeSubjectList = async () => {
  const res = await adminSubjects.getCurrentUserListGradeSubject.get()
  // console.log("当前用户的学段学科列表========================")
  // console.log(res)

  currentUserGradeSubjectList.value = res.data
}

// 学段-学科选中用户的第一个选段学科值
const selectUserFirstGradeSubject = async () => {
  // console.log("currentUserGradeSubjectList.value========================")
  // console.log(currentUserGradeSubjectList.value)

  if (currentUserGradeSubjectList.value && Array.isArray(currentUserGradeSubjectList.value)
    && currentUserGradeSubjectList.value.length > 0) {
    leftFormData.gradeSubject = []
    leftFormData.gradeSubject.push(currentUserGradeSubjectList.value[0].lawGradeId)
    leftFormData.gradeSubject.push(currentUserGradeSubjectList.value[0].subjects?.[0]?.lawSubjectId)

    // console.log("leftFormData.gradeSubject=====================")
    // console.log(leftFormData.gradeSubject)

    // await getGradeList(leftFormData.gradeSubject?.[0])
    await onChangeGradeSubject([leftFormData.gradeSubject?.[0], leftFormData.gradeSubject?.[1]])
  }
}

// 选第一个年级学科
const selectFirstGradeSubject = async () => {
  // console.log("学段学科列表配置=================================")
  // console.log(gradeSubjectOptions.value)

  leftFormData.gradeSubject = []
  leftFormData.gradeSubject.push(gradeSubjectOptions.value?.[0].value)
  leftFormData.gradeSubject.push(gradeSubjectOptions.value?.[0].children?.[0].value)

  await onChangeGradeSubject(leftFormData.gradeSubject)
}

// 选第一个教材
// const selectFirstBook = async () => {
//   // console.log("教材年级列表配置=================================")
//   // console.log(bookBookgradeOptions.value)

//   leftFormData.bookBookgrade = []
//   leftFormData.bookBookgrade.push(bookBookgradeOptions.value?.[0]?.value)
// }

// 选第一个教材教材版本
const selectFirstBookBookgrade = async () => {
  // console.log("教材年级列表配置=================================")
  // console.log(bookBookgradeOptions.value)

  leftFormData.bookBookgrade = []
  leftFormData.bookBookgrade.push(bookBookgradeOptions.value?.[0]?.value)
  leftFormData.bookBookgrade.push(bookBookgradeOptions.value?.[0]?.children?.[0].value)

  await onChangeBookBookgrade(leftFormData.bookBookgrade)
}

// 默认选第一个年级学科和教材
const selectFirstGradeSubjectBook = async () => {
  await selectFirstGradeSubject()
  await selectFirstBookBookgrade()
}

// 选中第一个栏目值
const selectFirstTreeData = async () => {
  if (!leftFormData.gradeSubject || leftFormData.gradeSubject.length === 0) return

  if (!treeData.value || treeData.value.length === 0 || !treeData.value[0].id) {
    treeData.value?.splice(0)

    await getTreeData(leftFormData.gradeSubject[0], leftFormData.gradeSubject[1])
  }


  filterText.value = ''
  selectedTreeData.value = treeData.value[0]
  currSelectedTreeItemId.value = treeData.value[0].id
}

// 表格数据
const tableData = ref([]);
// type: 类型，1课件，2教案

// 操作

// 删除
const handleDelete = (item) => {
  // 删除二次确认框
  ElMessageBox.confirm(
    '您确认删除该数据吗？',
    '消息中心：',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {

    // console.log("删除 项=====================")
    // console.log(item)

    let res = await adminMyLessonPreparation.remove.post([item.id])
    if (res.status == 200) {
      ElMessage.success('删除成功');
      await getList();
    } else {
      ElMessage.error(res.message)
    }
  }).catch(() => {
    // 关闭弹窗
  })
}

// 上传资料库
const uploadToLibrary = async (item) => {
  console.log("上传资料库 项=====================")
  console.log(item)

  let postData = {
    file_name: item.fileName,
    file_path: item.filePath,
    id: item.id,
    qb_grade_id: item.gradeId,
    qb_que_column_id: item.columnId,
    qb_que_tab_id: item.tabId,
    qb_subject_id: item.subjectId,
    type: item.type.code
  }

  const res = await adminMyLessonPreparation.saveMaterials.post(postData)
  if (res.status === 200) {
    ElMessage.success("上传成功")
    await getList()
  } else {
    ElMessage.error(res.message)
  }
}

// 批量删除
const handleBatchDelete = () => {
  if (!tableSelection.value || tableSelection.value.length == 0) {
    ElMessage.warning("请先选择需要删除的数据行");
    return false;
  }
  // 删除二次确认框
  ElMessageBox.confirm(
    '您确认删除选中的数据吗？',
    '消息中心：',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    let ids = tableSelection.value.map(item => item.id)
    let res = await adminQueTabs.remove.post(ids)
    if (res.status == 200) {
      ElMessage.success('删除成功');
      await getList();
    } else {
      ElMessage.error(res.message)
    }
  }).catch(() => {
    // 关闭弹窗
  })
}

const handleDownload = async (item) => {

  // console.log("点击下载项==================")
  // console.log(item)

  // if (item.filePath) {
  //   if (!isHttpUrl(item.filePath)) item.filePath = domainUrl.value + item.filePath
  //   const link = document.createElement('a');
  //   link.href = item.filePath;
  //   link.download = item.fileName + '.pdf'
  //   link.click();
  // }

  if (item.filePath) {
    if (!isHttpUrl(item.filePath)) {
      item.filePath = domainUrl.value + item.filePath
    }
    // 在新窗口打开 PDF
    window.open(item.filePath, '_blank')
  }
}

const handleDownloadByUrl = async (url) => {

  if (url) {
    if (!isHttpUrl(url)) url = domainUrl.value + url

    const link = document.createElement('a');
    link.href = url;
    link.download = 'download.pdf'
    link.click();
  }
}

// 操作 end

// 生成教案书弹窗
const generateBookDialogVisible = ref(false)
const dialogTreeRef = ref(null)
const dialogSelectedTreeData = ref({})
const dialogCurrSelectedTreeItemId = ref("")
// 列表数据
const dialogTableData = ref([]);

// 点击生成教案书按钮
const onClickGenerateBookBtn = async () => {
  // if (!leftFormData.bookBookgrade) {
  //   ElMessage.warning("请先选择教材-教材年级");
  //   return;
  // }

  await nextTick(() => {
    if (dialogTreeRef.value) {
      dialogTreeRef.value.setCurrentKey(null) // 或 setCurrentNode(null)
    }
    selectedMaterials.value = []
  })
  dialogTableData.value = tableData.value
  dialogTableData.value.forEach(i => i.checked = false)
  dialogSelectedTreeData.value = selectedTreeData.value
  dialogCurrSelectedTreeItemId.value = currSelectedTreeItemId.value

  generateBookDialogVisible.value = true
}



// 弹窗树形数据改变
const onDialogTreeDataChange = async (val) => {
  // console.log("树形数据改变=====================")
  // console.log(val)

  if (val) {
    dialogSelectedTreeData.value = val
    dialogCurrSelectedTreeItemId.value = val.id
  }

  getList(1)

}

// 计算已选材料
// const selectedMaterials = computed(() => {
//   return tableData.value.filter(item => item.checked);
// });
const selectedMaterials = ref([])

// 计算已选材料数量
const selectedMaterialsCount = computed(() => {
  return selectedMaterials.value.length;
});

// 更新已选材料（如果需要执行其他操作）
const updateSelectedMaterials = (val) => {
  // console.log("更新选择材料===============")
  // console.log(val)

  if (val.checked) {
    selectedMaterials.value.push(val)
  } else {
    selectedMaterials.value = selectedMaterials.value.filter(i => i.id !== val.id)
  }

  // console.log("选中材料===============")
  // console.log(selectedMaterials.value)
};

const delSelectedMaterial = (item) => {
  // console.log("选中删除项============")
  // console.log(item)
  item.checked = false;
  updateSelectedMaterials(item);
};

// 确认生成教案书
const onGenerateBookConfirm = async () => {
  // console.log("生成教案书=====================")
  // console.log("树形数据====================")
  // console.log(dialogSelectedTreeData.value)
  // console.log("已选材料====================")
  // console.log(selectedMaterials.value)

  // if (!dialogSelectedTreeData.value || !dialogSelectedTreeData.value.id) {
  //   ElMessage.warning("请先选择章节");
  //   return;
  // }

  if (selectedMaterialsCount.value === 0) {
    ElMessage.warning("请先选择材料");
    return;
  }

  let fileArr = selectedMaterials.value.map(i => {
    let path = i.filePath
    if (!isHttpUrl(path)) path = domainUrl.value + path
    return {
      fileName: i.fileName,
      filePath: path
    }
  })

  const res = await adminMyLessonPreparation.generateLessonBook.post({
    uploadFiles: fileArr
  })

  generateBookDialogVisible.value = false
  if (res.status === 200) {
    console.log("生成教案结果==============")
    console.log(res)

    ElMessage.success("生成成功")
    if (!isHttpUrl(res.data)) {
      spliceUrl.value = domainUrl.value
    } else {
      spliceUrl.value = ''
    }

    // console.log("spliceUrl==================")
    // console.log(spliceUrl.value)

    currentPdfUrl.value = res.data

    // if (currentPdfUrl.value.endsWith('docx') || currentPdfUrl.value.endsWith('doc')) {
    //   currentFileType.value = 'docx'
    // } else if (currentPdfUrl.value.endsWith('pdf')) {
    //   currentFileType.value = 'pdf'
    // } else if (currentPdfUrl.value.endsWith('xlsx')) {
    //   currentFileType.value = 'xlsx'
    // } else {
    //   currentFileType.value = ''
    // }

    currentFileType.value = 'docx'
    pdfDialogVisible.value = true
  } else {
    ElMessage.error(res.message)
  }



}

// 上传文件弹窗 =======================================

// 控制上传文件弹窗显示
const showUploadDialog = ref(false);
// 上传文件列表
const fileList = ref([]);
// 上传类型 1课件，2教案
const uploadType = ref(1)
const typeOptions = ref([
  {
    value: 1,
    label: "课件"
  },
  {
    value: 2,
    label: "教案"
  }
])

const onUploadFileClick = () => {
  if ((!leftFormData.gradeSubject || !Array.isArray(leftFormData.gradeSubject || leftFormData.gradeSubject.length === 0)) ||
    (!leftFormData.bookBookgrade || !Array.isArray(leftFormData.bookBookgrade || leftFormData.bookBookgrade.length === 0)) ||
    (!selectedTreeData.value || !selectedTreeData.value.id)
  ) {
    ElMessage.warning("请先选择学段、科目、教材年级、知识点")
    return
  }

  fileList.value = []
  showUploadDialog.value = true
}

const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result); // 这里就是 base64 字符串
    reader.onerror = (error) => reject(error);
  });
};

// 上传文件相关方法
const submitUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件');
    return;
  }

  if (!(uploadType.value === 1 || uploadType.value === 2)) {
    ElMessage.warning('请先选择要上传的类型');
    return;
  }

  console.log("fileList.value====================")
  console.log(fileList.value)

  let formData = new FormData();
  formData.append(`file`, fileList.value[0].raw);
  const res = await adminMyLessonPreparation.uploadFile.post(formData)

  const res2 = await adminMyLessonPreparation.saveOrUpdate.post({
    gradeId: leftFormData.gradeSubject[0],
    subjectId: leftFormData.gradeSubject[1],
    tabId: leftFormData.bookBookgrade[1],
    columnId: selectedTreeData.value.id,
    type: uploadType.value,
    status: 1, //状态，1待上传，2上传待审核，3上传成功，4被驳回
    fileType: 1, //文件类型，1我的备课，2智能教案上传附件，3智能教案生成结果
    fileName: res.data.originFileName,
    filePath: res.data.accessUrl
  })
  if (res2.status === 200) {
    ElMessage.success('文件上传成功');
    await getList()
  } else {
    ElMessage.error(res2.message)
  }

  showUploadDialog.value = false;

  // 清空文件列表
  fileList.value = [];
};

const handleUploadSuccess = (response, file, fileList) => {
  ElMessage.success('文件上传成功');
  console.log('上传成功:', file);
};

const handleUploadError = (error, file, fileList) => {
  ElMessage.error('文件上传失败');
  console.error('上传失败:', error);
};

const onFileChange = (file, files) => {
  // 这个函数是fileList变化前触发的
  const isValid =
    file.name.endsWith('.pdf') || file.name.endsWith('.docx');

  if (!isValid) {
    ElMessage.error('仅支持上传 .pdf 或 .docx 文件');
    // 移除不合法的文件
    fileList.value = fileList.value.filter(f => f.uid !== file.uid);
    return false;
  }
};

// PDF预览=======================================

//访问文件的前缀域名部分
const domainUrl = ref('')
const getDomainUrl = async () => {
  const res = await adminMyLessonPreparation.domain.get()
  domainUrl.value = res.data.normalPrefix
  // console.log("前缀域名===========")
  // console.log(domainUrl.value)
}

const pdfDialogVisible = ref(false);
const currentPdfTitle = ref('');
const currentPdfUrl = ref("")
const selectedItem = ref({})
const spliceUrl = ref('')
const currentFileType = ref('')

// 点击预览
const handlePreview = async (item) => {
  console.log("点击预览===========================")
  console.log(item)

  selectedItem.value = item

  try {
    const res = await adminColumns.qbExamPapersDownload.get({
      examPaperId: item.id,
      show_page_id: 29
    })

    console.log("下载试卷接口结果=====================")
    console.log(res)
    console.log(res.data.data.file_url)

    currentPdfUrl.value = res.data.data.file_url;
    currentPdfTitle.value = item.title || '预览';
    pdfDialogVisible.value = true;

    // 确保对话框内容（canvas/缩略图容器）已挂载
    await nextTick()

  } catch (e) {
    console.log(e)
  } finally {
  }

}

// 关闭PDF对话框时的处理
const handlePdfDialogClose = (done) => {
  done()
}


// ====================================================


// 公共资料库
const isPublicLibrary = ref(false)
// 类型选择
const typeRadio = ref('all')
// 排序选择
// const sortRadio = ref("desc")
const newSort = ref("asc") //asc desc
const readSort = ref("asc")

const publicBasePage = ref({
  pageSize: 10,
  pageNum: 1,
  pageSizes: [10, 20, 50],
  total: 0
})

// 列表数据
// const listData = ref([
//   {
//     id: 1,
//     name: '《我爱我们的祖国》课件-2025-2026学年语文一年级上册统编版',
//     type: '课件',
//     desc: '2025-07-29',
//   },
// ]);
const listData = ref([])

// 获取公共资料库分页数据
const getPublicBaseList = async () => {
  listData.value.splice(0)

  // 若没选类型，默认全部
  if (!typeRadio.value) typeRadio.value = 'all'
  // 若没选排序，默认降序
  // if (!sortRadio.value) sortRadio.value = 'desc'

  let postData = {
    // orderDirection: sortRadio.value,
    orderDirection: newSort.value,
    page: publicBasePage.value.pageNum,
    pageSize: publicBasePage.value.pageSize,
  }
  if (typeRadio.value !== 'all') postData.type = typeRadio.value
  const res = await adminMyLessonPreparation.materials.post(postData)

  // console.log("分页查询数据===============")
  // console.log(res)

  publicBasePage.value.total = res.data.meta.pagination.total
  listData.value.push(...res.data.data)
  listData.value.forEach(i => {
    i.fileType = judgeFileType(i.file_path)
  })
}


// 点击进入公共资料库
const onClickEnterPublicBase = async () => {
  // if ((!leftFormData.gradeSubject || !Array.isArray(leftFormData.gradeSubject || leftFormData.gradeSubject.length === 0)) ||
  //   (!leftFormData.bookBookgrade || !Array.isArray(leftFormData.bookBookgrade || leftFormData.bookBookgrade.length === 0)) ||
  //   (!selectedTreeData.value || !selectedTreeData.value.id)
  // ) {
  //   ElMessage.warning("请先选择学段、科目、教材年级、知识点")
  //   return
  // }

}

// 备课
const onSaveLesson = async (item) => {
  console.log("备课项========")
  console.log(item)

  const res = await adminMyLessonPreparation.saveOrUpdate.post({
    gradeId: item.qb_grade_id,
    subjectId: item.qb_subject_id,
    tabId: item.qb_que_tab_id,
    columnId: item.qb_que_column_id,
    type: item.type,
    status: 5, //状态，1待上传，2上传待审核，3上传成功，4被驳回 5从公共资料库获取
    fileType: 1, //文件类型，1我的备课，2智能教案上传附件，3智能教案生成结果
    fileName: item.file_name,
    filePath: item.file_url
  })
  if (res.status === 200) {
    ElMessage.success('备课成功');
    await getList()
  } else {
    ElMessage.error(res.message)
  }
}

// pdf预览
const previewItem = ref(null)
const currentPreviewFileType = ref("")

function isHttpUrl(url) {
  return /^http/i.test(url);
}

const getItemDetail = async (item) => {
  const res = await adminMyLessonPreparation.materialDetail.get({ id: item.id })
  console.log("详情结果===============")
  console.log(res.data)
  return res.data
}

const onItemPreview = async (item) => {
  console.log("预览项===============================")
  console.log(item)

  // const resDetail = await getItemDetail(item)

  previewItem.value = item

  if (!isHttpUrl(previewItem.value.filePath)) {
    spliceUrl.value = domainUrl.value
  } else {
    spliceUrl.value = ''
  }

  if (previewItem.value.filePath.endsWith('docx') || previewItem.value.filePath.endsWith('doc')) {
    currentPreviewFileType.value = 'docx'
  } else if (previewItem.value.filePath.endsWith('pdf')) {
    currentPreviewFileType.value = 'pdf'
  } else if (previewItem.value.filePath.endsWith('xlsx')) {
    currentPreviewFileType.value = 'xlsx'
  } else {
    currentPreviewFileType.value = 'docx'
  }
}

const onItemPreviewByUrl = async (fileUrl, file) => {
  console.log("预览项URL===============================")
  console.log(fileUrl)
  console.log(file)

  if (!isHttpUrl(fileUrl)) {
    spliceUrl.value = domainUrl.value
  } else {
    spliceUrl.value = ''
  }

  if (fileUrl.endsWith('docx') || fileUrl.endsWith('doc')) {
    currentPreviewFileType.value = 'docx'
  } else if (fileUrl.endsWith('pdf')) {
    currentPreviewFileType.value = 'pdf'
  } else if (fileUrl.endsWith('xlsx')) {
    currentPreviewFileType.value = 'xlsx'
  } else {
    currentPreviewFileType.value = 'docx'
  }

  previewItem.value = {
    filePath: fileUrl,
    fileName: file.file_name,
  }
}


// =====================================================

onMounted(async () => {
  // 获取学段学科列表
  await getGradeSubjectList()

  // 获取当前用户的学段学科列表
  await getCurrentUserGradeSubjectList()

  // 选择用户第一个学段学科
  await selectUserFirstGradeSubject()

  await getDomainUrl()

  // 获取删除标记id列表
  // await getIds()

  await getList();
})

onUnmounted(() => {
})
</script>

<style scoped lang="scss">
.container {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  padding: 20px;
  background: #F9FAFB;

  :deep(.el-button.is-disabled) {
    background-color: rgb(245, 245, 245);
    border: rgb(187, 187, 187) 1px solid;
    color: rgb(187, 187, 187);
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    padding: 0 0 0 10px;
    border-left: #A9C9F4 5px solid;
    // margin-bottom: 20px;
  }

  .left {
    flex-shrink: 0;
    width: 250px;
    background: #fff;
    padding: 20px;
    overflow-y: auto;
  }

  .right {
    flex: 1;
    background: #fff;
    padding: 20px 30px;

    .guideBar {
      font-size: 15px;
      font-weight: 500;
    }

    .fileList {}
  }

  .generateBookDialogContent {
    display: flex;
    gap: 15px;
    height: 100%;
    width: 100%;

    .left {
      flex: 1;
      // border-right: #C6C6C6 1px solid;
    }

    .right {
      flex: 4;
      display: flex;
      flex-direction: column;

      .file-list-container {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 15px;

      }

      .selected-materials {
        // border-top: 1px solid #ebeef5;
        padding: 10px;
        background-color: #DEDDDD;
        border-radius: 10px;

        .selected-title {
          font-weight: 500;
          margin-bottom: 10px;
        }

        .selected-items {
          max-height: 100px;
          overflow-y: auto;
          // border: 1px solid #dcdfe6;
          border-radius: 4px;
          padding: 10px;
          background-color: white;

          .selected-item {
            display: flex;
            align-items: center;

            &:not(:first-child) {
              margin-top: 7px;
            }

            .selected-item-name {
              flex: 1;
              font-size: 13px;
            }
          }
        }
      }

    }
  }

  .el-upload__text {
    font-size: 18px;
    font-weight: 600;
  }

  .upload-tip {
    margin-top: 10px;
    font-size: 15px;
    color: rgba($color: #000000, $alpha: 0.6)
  }

  .bottomBtn {
    box-sizing: border-box;
    margin-top: 15px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .el-tree-node__content {
    box-sizing: border-box;
    max-width: 200px;

    min-height: 36px !important;
    align-items: flex-start !important;

    .el-text.is-truncated {
      white-space: normal !important;
      word-break: break-word !important;
      overflow: visible !important;
      display: inline !important;
      text-overflow: clip !important;
      max-width: 100%;
    }
  }

  // 强制覆盖默认的展开图标
  :deep(.el-tree-node__expand-icon) {
    .el-icon {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }

  // 隐藏默认的箭头图标
  :deep(.el-tree-node__expand-icon) {
    .el-icon {
      display: none !important;
    }
  }

  // 显示自定义图标
  :deep(.custom-expand-icon) {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 16px !important;
    height: 16px !important;
  }

  :deep(.custom-expand-icon .el-icon) {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 16px !important;
  }
}

.file-item {
  display: flex;
  align-items: center;
  margin: 0 0 10px 0;
  padding: 0 0 10px 0;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }

  .file-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    background-color: transparent;
    border-radius: 4px;
  }

  .file-info {
    flex: 1;

    .file-name {
      font-weight: 400;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .bottomInfo {
      display: flex;
      gap: 10px;
      font-size: 15px;
      color: #909399;

      .file-desc {
        display: flex;
        align-items: center;
        gap: 5px;

        .icon {
          width: 18px;
          height: 18px;
          background-image: url("~@/assets/clockIcon.png");
          background-size: contain;
          background-position: center center;
          background-repeat: no-repeat;
        }
      }

      .downloadBtn {
        margin-left: 15px;
        height: 21px;
        width: 21px;

        background-image: url("~@/assets/downloadIcon.png");
        background-position: center center;
        background-repeat: no-repeat;
        background-size: contain;
      }
    }


  }

  .file-actions {
    margin-left: 10px;
    display: flex;
    gap: 13px;

    .succ,
    .fail,
    .wait {
      width: 90px;
      height: 30px;
      border-radius: 12px;
      font-size: 15px;
      text-align: center;
      line-height: 30px;
      font-weight: 600;
      cursor: default;
    }

    .wait {
      color: #1677FF;
      background-color: #bad2ec;
    }

    .fail {
      color: #B65822;
      background-color: #FEF3C7;
    }

    .succ {
      color: #437558;
      background-color: #DCFCE7;
    }
  }
}

// 调整对话框样式
:deep(.el-dialog__body) {
  padding: 0;
  margin: 0 auto;
  width: 90%;
  height: 90%;
  // height: calc(100% - 98px);
}
</style>

<style scoped>
:deep(.el-tree-node__expand-icon) {
  .el-icon {
    display: none !important;
  }
}

/* 自定义树形组件展开图标 */
:deep(.custom-tree .el-tree-node__expand-icon) {
  width: 16px;
  height: 16px;
  background-color: #909399;
  mask: url('/img/plus-icon.svg') no-repeat center;
  -webkit-mask: url('/img/plus-icon.svg') no-repeat center;
  mask-size: 16px 16px;
  -webkit-mask-size: 16px 16px;
  transition: all 0.3s ease;
}

:deep(.custom-tree .el-tree-node__expand-icon.expanded) {
  mask: url('/img/minus-icon.svg') no-repeat center;
  -webkit-mask: url('/img/minus-icon.svg') no-repeat center;
  mask-size: 16px 16px;
  -webkit-mask-size: 16px 16px;
}

:deep(.custom-tree .el-tree-node__expand-icon:hover) {
  background-color: #409eff;
}
</style>
