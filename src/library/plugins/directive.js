import { hasPermission } from '@/utils/permission'
export function setup(app) {
  /**
   * @description 自定义指令v-permissions
   */
  app.directive('permissions', {
    mounted(el, binding) {
      const { value } = binding
      if (value)
        if (!hasPermission(value))
          el.parentNode && el.parentNode.removeChild(el)
    },
  })

  /**
   * @description 自定义指令v-focus
   */
  app.directive('focus', {
    mounted(el) {
      el.focus()
    },
  })

  /**
   * @description 自定义指令v-lazy
   */
  app.directive('lazy', {
    mounted(el, binding) {
      const lazyLoadObser = new IntersectionObserver(
        (entry) => {
          entry.forEach((item) => {
            if (item.intersectionRatio > 0) {
              binding.value.init(1, 10, lazyLoadObser, item.target)
            }
          })
        },
        { root: null, rootMargin: '0px', threshold: 1.0 }
      )
      lazyLoadObser.observe(el)
    },
  })
}
