import 'ant-design-vue/dist/reset.css'
import '@/vab'
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import '@/utils/mathjax'
import 'mathjax/es5/tex-svg'

import { Boot } from '@wangeditor/editor'
import { module } from 'fjf-one'
Boot.registerModule(module)
import Antd from 'ant-design-vue'
import { createApp } from 'vue'
import { setGlobalOptions } from 'vue-request'

import { setupVab } from '@/library/index.js'
import router from '@/router/index.js'
import store from '@/store'
import DefaultDivider from '@/views/Divider/default'
import UseItem from '@/views/item_txt'

import App from './App.vue'
// import '@/utils/MouseClickEffect.js' 单击特效
/**
 * <AUTHOR> @description 正式环境默认使用mock，正式项目记得注释后再打包
 */
// 全局配置
setGlobalOptions({
  manual: true, // 手动模式
  // debounceInterval: 300, // 防抖
  // throttleInterval: 1000, // 节流
  pagination: {
    currentKey: 'page',
    pageSizeKey: 'pageSize',
    totalKey: 'meta.pagination.total',
  },
})
const app = createApp(App)

// if (process.env.NODE_ENV === 'production') {
//   const { mockXHR } = require('@/utils/static')
//   mockXHR()
// }
setupVab(app)
app
  .use(store)
  .use(router)
  .use(Antd)
  .component('use-item', UseItem)
  .component('DefaultDivider', DefaultDivider)
  .mount('#app')
