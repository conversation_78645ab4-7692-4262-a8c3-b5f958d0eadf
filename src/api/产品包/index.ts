import qs from 'qs'

import request from '@/utils/request'

/**
 * 产品包列表
 * @param params
 * @returns
 */
export function getProducts(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/products`,
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 产品包详情
 * @param id
 * @returns
 */
export function getProductsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/products/${id}`,
    method: 'GET',
  })
}

/**
 * 修改产品包
 * @param data
 * @returns
 */
export function editProducts(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/products/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 产品包订阅明细列表
 * @param params
 * @returns
 */
export function getDcoosMessages(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/dcoosMessages`,
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}
/**
 * 产品包订阅明细详情
 * @param params
 * @returns
 */
export function getDcoosMessagesDetail(params: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/dcoosMessages/${id}`,
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}
