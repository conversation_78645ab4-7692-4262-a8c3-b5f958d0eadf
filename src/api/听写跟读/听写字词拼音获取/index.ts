import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍语文听写字词拼音获取
 * @param params
 * @returns
 */
export function getLiteracyBookChineseWordsPinyin(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords/pinyin`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}
