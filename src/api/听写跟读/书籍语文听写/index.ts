import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍语文听写列表
 * @param params
 * @returns
 */
export function getLiteracyBookChineseWords(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取书籍语文听写详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyBookChineseWordsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords/${id}`,
    method: 'GET',
  })
}

/**
 * 创建书籍语文听写
 * @param data
 * @returns
 */
export function createLiteracyBookChineseWords(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords`,
    method: 'POST',
    data,
  })
}

/**
 * 修改书籍语文听写
 * @param data
 * @returns
 */
export function editLiteracyBookChineseWords(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除书籍语文听写
 * @param data
 * @returns
 */
export function deleteLiteracyBookChineseWords(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookChineseWords/${id}`,
    method: 'DELETE',
  })
}
