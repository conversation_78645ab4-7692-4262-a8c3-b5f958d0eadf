import qs from 'qs'

import request from '@/utils/request'

/**
 * 学段列表
 * @param params
 * @returns
 */
export function getLiteracyGrades(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/grades`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取学段详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyGradesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/grades/${id}`,
    method: 'GET',
  })
}

/**
 * 创建学段
 * @param data
 * @returns
 */
export function createLiteracyGrades(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/grades`,
    method: 'POST',
    data,
  })
}

/**
 * 修改学段
 * @param data
 * @returns
 */
export function editLiteracyGrades(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/grades/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学段
 * @param data
 * @returns
 */
export function deleteLiteracyGrades(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/grades/${id}`,
    method: 'DELETE',
  })
}
