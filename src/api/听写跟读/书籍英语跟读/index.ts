import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍英语跟读列表
 * @param params
 * @returns
 */
export function getLiteracyBookEnglishFollows(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishFollows`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取书籍英语跟读详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyBookEnglishFollowsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishFollows/${id}`,
    method: 'GET',
  })
}

/**
 * 创建书籍英语跟读
 * @param data
 * @returns
 */
export function createLiteracyBookEnglishFollows(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishFollows`,
    method: 'POST',
    data,
  })
}

/**
 * 修改书籍英语跟读
 * @param data
 * @returns
 */
export function editLiteracyBookEnglishFollows(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishFollows/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除书籍英语跟读
 * @param data
 * @returns
 */
export function deleteLiteracyBookEnglishFollows(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishFollows/${id}`,
    method: 'DELETE',
  })
}
