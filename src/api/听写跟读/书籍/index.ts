import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍列表
 * @param params
 * @returns
 */
export function getLiteracyBooks(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/books`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取书籍详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyBooksDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/books/${id}`,
    method: 'GET',
  })
}

/**
 * 创建书籍
 * @param data
 * @returns
 */
export function createLiteracyBooks(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/books`,
    method: 'POST',
    data,
  })
}

/**
 * 修改书籍
 * @param data
 * @returns
 */
export function editLiteracyBooks(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/books/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除书籍
 * @param data
 * @returns
 */
export function deleteLiteracyBooks(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/books/${id}`,
    method: 'DELETE',
  })
}
