import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍单元章节列表
 * @param params
 * @returns
 */
export function getLiteracyBookUnits(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookUnits`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取书籍单元章节详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyBookUnitsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookUnits/${id}`,
    method: 'GET',
  })
}

/**
 * 创建书籍单元章节
 * @param data
 * @returns
 */
export function createLiteracyBookUnits(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookUnits`,
    method: 'POST',
    data,
  })
}

/**
 * 修改书籍单元章节
 * @param data
 * @returns
 */
export function editLiteracyBookUnits(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookUnits/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除书籍单元章节
 * @param data
 * @returns
 */
export function deleteLiteracyBookUnits(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookUnits/${id}`,
    method: 'DELETE',
  })
}
