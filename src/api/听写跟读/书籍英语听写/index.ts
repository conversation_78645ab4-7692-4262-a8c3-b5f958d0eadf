import qs from 'qs'

import request from '@/utils/request'

/**
 * 书籍英语听写列表
 * @param params
 * @returns
 */
export function getLiteracyBookEnglishWords(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishWords`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取书籍英语听写详情
 * @param params
 * @param id
 * @returns
 */
export function getLiteracyBookEnglishWordsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishWords/${id}`,
    method: 'GET',
  })
}

/**
 * 创建书籍英语听写
 * @param data
 * @returns
 */
export function createLiteracyBookEnglishWords(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishWords`,
    method: 'POST',
    data,
  })
}

/**
 * 修改书籍英语听写
 * @param data
 * @returns
 */
export function editLiteracyBookEnglishWords(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishWords/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除书籍英语听写
 * @param data
 * @returns
 */
export function deleteLiteracyBookEnglishWords(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/literacy/bookEnglishWords/${id}`,
    method: 'DELETE',
  })
}
