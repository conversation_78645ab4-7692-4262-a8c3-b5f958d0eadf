import qs from 'qs'

import request from '@/utils/request'

/**
 * 资源列表
 * @param params
 * @returns
 */
export function getQbQueSources(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSources`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取资源详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueSourcesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSources/${id}`,
    method: 'GET',
  })
}

/**
 * 创建资源
 * @param data
 * @returns
 */
export function createQbQueSources(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSources`,
    method: 'POST',
    data,
  })
}

/**
 * 修改资源
 * @param data
 * @returns
 */
export function editQbQueSources(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSources/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除资源
 * @param data
 * @returns
 */
export function deleteQbQueSources(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSources/${id}`,
    method: 'DELETE',
  })
}
