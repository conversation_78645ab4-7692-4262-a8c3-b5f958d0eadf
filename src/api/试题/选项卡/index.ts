import qs from 'qs'

import request from '@/utils/request'

/**
 * 选项卡列表
 * @param params
 * @returns
 */
export function getQbQueTabs(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTabs`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取选项卡详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueTabsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTabs/${id}`,
    method: 'GET',
  })
}

/**
 * 创建选项卡
 * @param data
 * @returns
 */
export function createQbQueTabs(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTabs`,
    method: 'POST',
    data,
  })
}

/**
 * 修改选项卡
 * @param data
 * @returns
 */
export function editQbQueTabs(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTabs/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除选项卡
 * @param data
 * @returns
 */
export function deleteQbQueTabs(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTabs/${id}`,
    method: 'DELETE',
  })
}
