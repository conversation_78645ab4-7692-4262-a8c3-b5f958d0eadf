import qs from 'qs'

import request from '@/utils/request'

/**
 * 模板列表
 * @param params
 * @returns
 */
export function getQbQueTemplates(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTemplates`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取模板详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueTemplatesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTemplates/${id}`,
    method: 'GET',
  })
}

/**
 * 创建模板
 * @param data
 * @returns
 */
export function createQbQueTemplates(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTemplates`,
    method: 'POST',
    data,
  })
}

/**
 * 修改模板
 * @param data
 * @returns
 */
export function editQbQueTemplates(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTemplates/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除模板
 * @param data
 * @returns
 */
export function deleteQbQueTemplates(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueTemplates/${id}`,
    method: 'DELETE',
  })
}
