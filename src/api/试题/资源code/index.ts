import qs from 'qs'

import request from '@/utils/request'

/**
 * 资源code列表
 * @param params
 * @returns
 */
export function getQbQueSourceCodes(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSourceCodes`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取资源code详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueSourceCodesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSourceCodes/${id}`,
    method: 'GET',
  })
}

/**
 * 创建资源code
 * @param data
 * @returns
 */
export function createQbQueSourceCodes(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSourceCodes`,
    method: 'POST',
    data,
  })
}

/**
 * 修改资源code
 * @param data
 * @returns
 */
export function editQbQueSourceCodes(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSourceCodes/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除资源code
 * @param data
 * @returns
 */
export function deleteQbQueSourceCodes(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueSourceCodes/${id}`,
    method: 'DELETE',
  })
}
