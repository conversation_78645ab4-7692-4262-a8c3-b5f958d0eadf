import qs from 'qs'

import request from '@/utils/request'

/**
 * 学段列表
 * @param params
 * @returns
 */
export function getQbGrades(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbGrades`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取学段详情
 * @param params
 * @param id
 * @returns
 */
export function getQbGradesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbGrades/${id}`,
    method: 'GET',
  })
}

/**
 * 创建学段
 * @param data
 * @returns
 */
export function createQbGrades(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbGrades`,
    method: 'POST',
    data,
  })
}

/**
 * 修改学段
 * @param data
 * @returns
 */
export function editQbGrades(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbGrades/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学段
 * @param data
 * @returns
 */
export function deleteQbGrades(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbGrades/${id}`,
    method: 'DELETE',
  })
}
