import qs from 'qs'

import request from '@/utils/request'

/**
 * 学科列表
 * @param params
 * @returns
 */
export function getQbSubjects(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbSubjects`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取学科详情
 * @param params
 * @param id
 * @returns
 */
export function getQbSubjectsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbSubjects/${id}`,
    method: 'GET',
  })
}

/**
 * 创建学科
 * @param data
 * @returns
 */
export function createQbSubjects(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbSubjects`,
    method: 'POST',
    data,
  })
}

/**
 * 修改学科
 * @param data
 * @returns
 */
export function editQbSubjects(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbSubjects/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学科
 * @param data
 * @returns
 */
export function deleteQbSubjects(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/qbSubjects/${id}`,
    method: 'DELETE',
  })
}
