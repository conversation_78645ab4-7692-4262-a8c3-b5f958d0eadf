import qs from 'qs'

import request from '@/utils/request'

/**
 * 知识点列表
 * @param params
 * @returns
 */
export function getQbQueColumns(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueColumns`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取知识点详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueColumnsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueColumns/${id}`,
    method: 'GET',
  })
}

/**
 * 创建知识点
 * @param data
 * @returns
 */
export function createQbQueColumns(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueColumns`,
    method: 'POST',
    data,
  })
}

/**
 * 修改知识点
 * @param data
 * @returns
 */
export function editQbQueColumns(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueColumns/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除知识点
 * @param data
 * @returns
 */
export function deleteQbQueColumns(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueColumns/${id}`,
    method: 'DELETE',
  })
}
