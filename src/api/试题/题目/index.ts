import qs from 'qs'

import request from '@/utils/request'

/**
 * 题目列表
 * @param params
 * @returns
 */
export function getQbQueQuestions(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueQuestions`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取题目详情
 * @param params
 * @param id
 * @returns
 */
export function getQbQueQuestionsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueQuestions/${id}`,
    method: 'GET',
  })
}

/**
 * 创建题目
 * @param data
 * @returns
 */
export function createQbQueQuestions(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueQuestions`,
    method: 'POST',
    data,
  })
}

/**
 * 修改题目
 * @param data
 * @returns
 */
export function editQbQueQuestions(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueQuestions/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除题目
 * @param data
 * @returns
 */
export function deleteQbQueQuestions(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/que/qbQueQuestions/${id}`,
    method: 'DELETE',
  })
}
