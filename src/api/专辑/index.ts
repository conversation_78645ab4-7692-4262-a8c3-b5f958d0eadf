import qs from 'qs'

import request from '@/utils/request'

/**
 * 专辑列表
 * @param params
 * @returns
 */
export function getAlbums(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/albums`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取专辑详情
 * @param params
 * @param id
 * @returns
 */
export function getAlbumsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/albums/${id}`,
    method: 'GET',
  })
}

/**
 * 创建专辑
 * @param data
 * @returns
 */
export function createAlbums(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/albums`,
    method: 'POST',
    data,
  })
}

/**
 * 修改专辑
 * @param data
 * @returns
 */
export function editAlbums(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/albums/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除专辑
 * @param data
 * @returns
 */
export function deleteAlbums(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/albums/${id}`,
    method: 'DELETE',
  })
}