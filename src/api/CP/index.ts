import qs from 'qs'

import request from '@/utils/request'

/**
 * cp列表
 * @param params
 * @returns
 */
export function getCps(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/cps`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取供应商详情
 * @param id
 * @returns
 */
export function getCpsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/cps/${id}`,
    method: 'GET',
  })
}

/**
 * 创建供应商
 * @param data
 * @returns
 */
export function createCps(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/cps`,
    method: 'POST',
    data,
  })
}

/**
 * 修改供应商
 * @param data
 * @returns
 */
export function editCps(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/cps/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学科
 * @param data
 * @returns
 */
export function deleteCps(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/cps/${id}`,
    method: 'DELETE',
  })
}
