import qs from 'qs'

import request from '@/utils/request'

/**
 * 栏目列表
 * @param params
 * @returns
 */
export function getColumns(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columns`,
    method: 'GET',
    params: {
      ...params,
      parent_id: 0,
    },
    // paramsSerializer: (params) =>
    //   qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 栏目详情
 * @param id
 * @returns
 */
export function getColumnsDetail(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columns/${id}`,
    method: 'GET',
  })
}

/**
 * 创建子栏目
 * @param data
 * @returns
 */
export function createColumns(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columns`,
    method: 'POST',
    data,
  })
}

/**
 * 编辑子栏目
 * @param data
 * @returns
 */
export function editColumns(data: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columns/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除栏目
 * @param id
 * @returns
 */
export function deleteColumns(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columns/${id}`,
    method: 'DELETE',
  })
}
