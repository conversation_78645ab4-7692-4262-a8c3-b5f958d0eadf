import qs from 'qs'

import request from '@/utils/request'

/**
 * 用户列表
 * @param params
 * @returns
 */
export function getUsers(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users`,
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 用户创建
 * @param data
 * @returns
 */
export function createUsers(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users`,
    method: 'POST',
    data,
  })
}

/**
 * 用户详情
 * @param data
 * @returns
 */
export function getUsersDetail(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users/${id}`,
    method: 'GET',
  })
}

/**
 * 用户编辑
 * @param data
 * @param id
 * @returns
 */
export function editUsers(data: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 用户删除
 * @param data
 * @param id
 * @returns
 */
export function deleteUsers(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users/${id}`,
    method: 'DELETE',
  })
}

/**
 * 用户积分明细
 * @param params
 * @returns
 */
export function getPointLogs(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/pointLogs`,
    method: 'GET',
    params: {
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 用户产品包修改
 * @param data
 * @param id
 * @returns
 */
export function editUsersProducts(data: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/users/${id}/products`,
    method: 'PUT',
    data,
  })
}
