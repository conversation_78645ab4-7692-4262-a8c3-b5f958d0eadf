import qs from 'qs'

import request from '@/utils/request'

/**
 * 学级列表
 * @param params
 * @returns
 */
export function getGrades(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/grades`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取学级详情
 * @param id
 * @returns
 */
export function getGradesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/grades/${id}`,
    method: 'GET',
  })
}

/**
 * 创建学级
 * @param data
 * @returns
 */
export function createGrades(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/grades`,
    method: 'POST',
    data,
  })
}

/**
 * 修改学级
 * @param data
 * @returns
 */
export function editGrades(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/grades/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学级
 * @param data
 * @returns
 */
export function deleteGrades(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/grades/${id}`,
    method: 'DELETE',
  })
}
