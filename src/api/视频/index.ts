import request from '@/utils/request'

interface Video {
  title: string
  play_id: string
  upload_type: number
  cp_id: number
  cover_url_id?: string
}

/**
 * 视频列表
 * @param params
 * @returns
 */
export async function getVideos(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/videos`,
    method: 'GET',
    params,
  })
}

/**
 * 视频详情
 * @param id
 * @returns
 */
export async function getVideosDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/videos/${id}`,
    method: 'GET',
  })
}

/**
 * 创建视频
 * @param data
 * @returns
 */
export async function createVideos(data: Video) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/videos`,
    method: 'POST',
    data,
  })
}

/**
 * 修改视频
 * @param data
 * @returns
 */
export async function editVideos(data: Video, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/videos/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除视频
 * @param id
 * @returns
 */
export async function deleteVideos(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/videos/${id}`,
    method: 'DELETE',
  })
}
