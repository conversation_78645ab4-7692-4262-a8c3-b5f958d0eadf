import request from '@/utils/request'

/**
 * 栏目视频策略详情(栏目的id)
 * @param id
 * @returns
 */
export function getColumnVideoStrategiesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columnVideoStrategies/${id}`,
    method: 'GET',
  })
}

/**
 * 创建栏目视频策略(栏目的id)
 * @param data
 * @returns
 */
export function createColumnVideoStrategies(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columnVideoStrategies`,
    method: 'POST',
    data,
  })
}

/**
 * 修改栏目视频策略(栏目的id)
 * @param data
 * @returns
 */
export function editColumnVideoStrategies(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columnVideoStrategies/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除栏目视频策略(栏目的id)
 * @param data
 * @returns
 */
export function deleteColumnVideoStrategies(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/columnVideoStrategies/${id}`,
    method: 'DELETE',
  })
}
