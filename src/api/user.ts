import axios from 'axios'

import { Authorization, contentType } from '@/config'
import store from '@/store'
import request from '@/utils/request'
// interface Person {
//   firstName: string;
//   lastName: string;
// }
/**
 *
 * @param params
 * @returns
 */
export function login(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/login`,
    method: 'POST',
    data,
  })
}

export function getUserInfo() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/me`,
    method: 'GET',
  })
}

export function logout() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/logout`,
    method: 'POST',
  })
}

export function refresh() {
  return axios({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/refresh`,
    method: 'POST',
    headers: {
      [Authorization]: store.getters['user/accessToken'],
      'Content-Type': contentType,
    },
  })
}

export function getVOD() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/sts/vod`,
    method: 'GET',
  })
}
export function getOSS() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/sts/oss`,
    method: 'GET',
  })
}

export function getCaptcha() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/captcha`,
    method: 'GET',
  })
}
export function NewLogin(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/auth/newLogin`,
    method: 'POST',
    data,
  })
}
