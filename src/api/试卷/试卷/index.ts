import qs from 'qs'

import request from '@/utils/request'

/**
 * 试卷列表
 * @param params
 * @returns
 */
export function getQbExamPapers(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取试卷详情
 * @param params
 * @param id
 * @returns
 */
export function getQbExamPapersDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers/${id}`,
    method: 'GET',
  })
}

/**
 * 创建试卷
 * @param data
 * @returns
 */
export function createQbExamPapers(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers`,
    method: 'POST',
    data,
  })
}

/**
 * 修改试卷
 * @param data
 * @returns
 */
export function editQbExamPapers(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除试卷
 * @param data
 * @returns
 */
export function deleteQbExamPapers(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers/${id}`,
    method: 'DELETE',
  })
}

/**
 * 试卷批量修改
 * @param data
 * @returns
 */
export function editQbExamPapersBatch(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbExamPapers/batch`,
    method: 'PUT',
    data,
  })
}
