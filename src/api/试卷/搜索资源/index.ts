import qs from 'qs'

import request from '@/utils/request'

/**
 * 资源列表
 * @param params
 * @returns
 */
export function getQbSearches(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbSearches`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取资源详情
 * @param params
 * @param id
 * @returns
 */
export function getQbSearchesDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbSearches/${id}`,
    method: 'GET',
  })
}

/**
 * 创建资源
 * @param data
 * @returns
 */
export function createQbSearches(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbSearches`,
    method: 'POST',
    data,
  })
}

/**
 * 修改资源
 * @param data
 * @returns
 */
export function editQbSearches(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbSearches/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除资源
 * @param data
 * @returns
 */
export function deleteQbSearches(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbSearches/${id}`,
    method: 'DELETE',
  })
}
