import qs from 'qs'

import request from '@/utils/request'

/**
 * 栏目列表
 * @param params
 * @returns
 */
export function getQbColumns(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbColumns`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取栏目详情
 * @param params
 * @param id
 * @returns
 */
export function getQbColumnsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbColumns/${id}`,
    method: 'GET',
  })
}

/**
 * 创建栏目
 * @param data
 * @returns
 */
export function createQbColumns(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbColumns`,
    method: 'POST',
    data,
  })
}

/**
 * 修改栏目
 * @param data
 * @returns
 */
export function editQbColumns(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbColumns/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除栏目
 * @param data
 * @returns
 */
export function deleteQbColumns(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/qb/examPaper/qbColumns/${id}`,
    method: 'DELETE',
  })
}
