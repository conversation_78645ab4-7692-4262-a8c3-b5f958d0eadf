import qs from 'qs'

import request from '@/utils/request'

/**
 * 页面列表
 * @param params
 * @returns
 */
export function getPages(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/pages`,
    method: 'GET',
    params: {
      type: 1,
      ...params,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 页面详情
 * @param id
 * @returns
 */
export function getPagesDetail(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/pages/${id}`,
    method: 'GET',
  })
}

/**
 * 页面修改
 * @param data
 * @param id
 * @returns
 */
export function editPages(data: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/pages/${id}`,
    method: 'PUT',
    data,
  })
}
