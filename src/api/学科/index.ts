import qs from 'qs'

import request from '@/utils/request'

/**
 * 学科列表
 * @param params
 * @returns
 */
export function getSubjects(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/subjects`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 获取学科详情
 * @param params
 * @param id
 * @returns
 */
export function getSubjectsDetail(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/subjects/${id}`,
    method: 'GET',
  })
}

/**
 * 创建学科
 * @param data
 * @returns
 */
export function createSubjects(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/subjects`,
    method: 'POST',
    data,
  })
}

/**
 * 修改学科
 * @param data
 * @returns
 */
export function editSubjects(data: any, id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/subjects/${id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除学科
 * @param data
 * @returns
 */
export function deleteSubjects(id: number | string) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/subjects/${id}`,
    method: 'DELETE',
  })
}
