import qs from 'qs'

import request from '@/utils/request'

/**
 * 广告位列表
 * @param params
 * @returns
 */
export function getAdverts(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/adverts`,
    method: 'GET',
    params: {
      ...params,
      type: 1,
    },
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 广告位详情
 * @param id
 * @returns
 */
export function getAdvertsDetail(id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/adverts/${id}`,
    method: 'GET',
  })
}

/**
 * 广告位创建
 * @param id
 * @returns
 */
export function createAdverts(data: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/adverts`,
    method: 'POST',
    data,
  })
}

/**
 * 广告位编辑
 * @param data
 * @returns
 */
export function editAdverts(data: any, id: string | number) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/adverts/${id}`,
    method: 'PUT',
    data,
  })
}
