import http from "@/utils/request"

export default {
  page: {
    url: `/hcapi/admin/adminSubjects/page/`,
    name: "分页列表",
    post: async function (data) {
      return await http.post(this.url + data.pageNum + "/" + data.pageSize, data);
    }
  },

  saveOrUpdate: {
    url: `/hcapi/admin/adminSubjects/saveOrUpdate`,
    name: "新增编辑",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  detail: {
    url: `/hcapi/admin/adminSubjects/detail/`,
    name: "详情",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  remove: {
    url: `/hcapi/admin/adminSubjects/remove`,
    name: "删除",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  pageGrades: {
    url: `/hcapi/admin/adminSubjects/pageGrades/`,
    name: "学级分页列表",
    post: async function (data) {
      return await http.post(this.url + data.pageNum + "/" + data.pageSize, data);
    }
  },
  detailGrade: {
    url: `/hcapi/admin/adminSubjects/detailGrade/`,
    name: "学级详情",
    get: async function (id, status) {
      return await http.get(this.url + id);
    }
  },

  listGradeSubject: {
    url: `/hcapi/admin/adminSubjects/listGradeSubject`,
    name: "学段学科列表",
    get: async function () {
      return await http.get(this.url);
    }
  },

  simpleGradeList: {
    url: `/hcapi/admin/adminSubjects/simpleGradeList`,
    name: "学级列表",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  originGradeList: {
    url: `/hcapi/admin/adminSubjects/originGradeList`,
    name: "乐维学级列表",
    get: async function () {
      return await http.get(this.url);
    }
  },

  subjectsList: {
    url: `/hcapi/admin/adminSubjects/list`,
    name: "学科列表",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  saveOrUpdateGrade: {
    url: `/hcapi/admin/adminSubjects/saveOrUpdateGrade`,
    name: "新增编辑学级",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  getExamScreeningByGrade: {
    url: `/hcapi/admin/adminSubjects/getExamScreeningByGrade`,
    name: "根据学段获得试卷年级筛选条件",
    get: async function (query) {
      let str = '?'
      for (let k in query) {
        if (query[k] === undefined || query[k] === null) {
          // str += `${k}=&`
          str += ''
        }
        else {
          str += `${k}=${query[k]}&`
        }
      }
      str = str.substring(0, str.length - 1)
      // console.log(str, "str========================")

      return await http.get(this.url + str);

    }
  },

  getCurrentUserListGradeSubject: {
    url: `/hcapi/admin/adminSubjects/getCurrentUserListGradeSubject`,
    name: "获得当前用户的学段学科列表",
    get: async function () {
      return await http.get(this.url);
    }
  },

  bookList: {
    url: `/hcapi/admin/adminSubjects/bookList`,
    name: "根据学科获得教材列表",
    get: async function (query) {
      return await http.get(this.url + `?subjectId=${query.subjectId}&gradeId=${query.gradeId}`);
    }
  },
  getCurrentUserSubjects: {
    url: `/hcapi/admin/adminSubjects/getCurrentUserSubject`,
    name: "获得当前用户的学科",
    get: async function () {
      return await http.get(this.url);
    }
  }
}

