import http from "@/utils/request"

export default {
  treeList: {
    url: `/hcapi/admin/adminLessonQueTabs/treeList`,
    name: "教材-教材年级列表",
    post: async function (data, query) {
      return await http.post(this.url, data, query);
    }
  },

  knowledgeList: {
    url: `/hcapi/admin/adminLessonQueColumns/treeList`,
    name: "知识点列表",
    post: async function (data, query) {
      return await http.post(this.url, data, query);
    }
  },

  lessonPlanPage: {
    url: `/hcapi/admin/adminLessonPlan/page/`,
    name: "教案书分页数据",
    post: async function (data) {
      return await http.post(this.url + data.page + "/" + data.pageSize, data);
    }
  },

  generateLesson: {
    url: `/hcapi/admin/adminLessonPlan/generateLesson`,
    name: "生成教案",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  generateLessonBook: {
    url: `/hcapi/admin/adminLessonPlan/generateLessonBook`,
    name: "生成教案书",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  remove: {
    url: `/hcapi/admin/adminLessonPlan/remove`,
    name: "删除",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  uploadFile: {
    url: `/hcapi/system/file/put-file`,
    name: "上传文件",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },

  domain: {
    url: `/hcapi/system/file/domain`,
    name: "访问文件的前缀域名部分",
    get: async function () {
      return await http.get(this.url);
    }
  },

  generateLessonBook: {
    url: `/hcapi/admin/adminLessonPlan/generateLessonBook`,
    name: "生成教案书",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  saveOrUpdate: {
    url: `/hcapi/admin/adminLessonPlan/saveOrUpdate`,
    name: "新增编辑",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  saveMaterials: {
    url: `/hcapi/admin/adminLewApi/lesson/preparation/saveMaterials`,
    name: "上传公共资料库",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  materialDetail: {
    url: `/hcapi/admin/adminLewApi/lesson/preparation/materialDetail/`,
    name: "获取乐维侧的资料详情",
    get: async function (path) {
      return await http.get(this.url + `${path.id}`);
    }
  },
  materials: {
    url: `/hcapi/admin/adminLewApi/lesson/preparation/materials`,
    name: "获取乐维侧的公共资料库分页数据",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  }
}
