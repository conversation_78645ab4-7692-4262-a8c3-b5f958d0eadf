import http from "@/utils/request"

export default {
  page: {
    url: `/hcapi/admin/adminQueTabs/page/`,
    name: "分页列表",
    post: async function (data) {
      return await http.post(this.url + data.pageNum + "/" + data.pageSize, data);
    }
  },

  saveOrUpdate: {
    url: `/hcapi/admin/adminQueTabs/saveOrUpdate`,
    name: "新增编辑",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  detail: {
    url: `/hcapi/admin/adminQueTabs/detail/`,
    name: "详情",
    get: async function (id, status) {
      return await http.get(this.url + id);
    }
  },
  remove: {
    url: `/hcapi/admin/adminQueTabs/remove`,
    name: "删除",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  treeList: {
    url: `/hcapi/admin/adminQueTabs/treeList`,
    name: "获取教材-教材年级列表",
    post: async function (data) {
      return await http.post(this.url, data);
    }
  },
  bookList: {
    url: `/hcapi/admin/adminQueTabs/bookList`,
    name: "教材列表",
    get: async function () {
      return await http.get(this.url);
    }
  },
}

