import request from '@/utils/request'

// 成绩等级管理接口

// 获取成绩等级列表
export const getGradeStandards = (params?: any) => {
  return request({
    url: '/api/admin/gradeStandards',
    method: 'get',
    params
  })
}

// 创建成绩等级
export const createGradeStandard = (data: {
  standard_name: string
  min_score: number
  max_score: number
}) => {
  return request({
    url: '/api/admin/gradeStandards',
    method: 'post',
    data
  })
}

// 获取单个成绩等级
export const getGradeStandard = (id: number) => {
  return request({
    url: `/api/admin/gradeStandards/${id}`,
    method: 'get'
  })
}

// 更新成绩等级
export const updateGradeStandard = (id: number, data: {
  min_score: number
  max_score: number
}) => {
  return request({
    url: `/api/admin/gradeStandards/${id}`,
    method: 'put',
    data
  })
}

// 删除成绩等级
export const deleteGradeStandard = (id: number) => {
  return request({
    url: `/api/admin/gradeStandards/${id}`,
    method: 'delete'
  })
}
