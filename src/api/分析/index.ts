import qs from 'qs'

import request from '@/utils/request'

/**
 * 总览
 * @returns
 */
export function getSummary() {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/analyzes/summary`,
    method: 'GET',
  })
}

/**
 * 日志柱状图
 * @param params
 * @returns
 */
export function getWebLogBarChart(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/analyzes/webLogBarChart`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 页面数据(不做排行)
 * @returns
 */
export function getPageLog(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/analyzes/pageLog`,
    method: 'GET',
    params,
  })
}

/**
 * 浏览数据
 * @returns
 */
export function getPlayLogs(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/analyzes/playLogs`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 产品包订购信息
 * @returns
 */
export function getUserProductLogs(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/userProductLogs`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}

/**
 * 浏览数据下载
 * @returns
 */
export function getPlayLogsDownload(params: any) {
  return request({
    url: `${process.env.VUE_APP_DATA_URL_PXWH}/api/admin/analyzes/playLogDownload`,
    method: 'GET',
    params,
    paramsSerializer: (params) =>
      qs.stringify(params, { arrayFormat: 'indices' }),
  })
}
