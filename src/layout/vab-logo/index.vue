<template>
  <div class="vab-logo">
    <vab-icon v-if="logo" :icon="logo" />
    <img alt="" class="logo" :src="svg" />
    <span class="anticon"></span>
    <span>{{ title }}</span>
  </div>
</template>

<script>
  import { computed } from 'vue'
  import { useStore } from 'vuex'

  import svg from '@/assets/logo.svg'
  import VabIcon from '@/layout/vab-icon'

  export default {
    name: 'VabLogo',
    components: { VabIcon },
    setup() {
      const store = useStore()
      return {
        logo: computed(() => store.getters['settings/logo']),
        title: computed(() => store.getters['settings/title']),
        svg,
      }
    },
  }
</script>
<style lang="less" scoped>
  .vab-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin: 16px 5px;
    overflow: hidden;
    overflow: hidden;
    font-size: 15px;
    color: #fff;
    text-overflow: ellipsis;
    white-space: nowrap;
    .logo {
      height: 100%;
      vertical-align: top;
    }
  }
</style>
