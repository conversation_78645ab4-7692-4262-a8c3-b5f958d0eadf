<template>
  <div class="vab-avatar">
    <div>
      <a-tooltip placement="bottom">
        <template #title>
          <span>主页</span>
        </template>
        <vab-icon class="icon" icon="home-4-line" @click="onHome" />
      </a-tooltip>
    </div>
    <div>
      <a-tooltip placement="bottom">
        <template #title>
          <span>刷新</span>
        </template>
        <vab-icon class="icon" icon="donut-chart-line" @click="refresh" />
      </a-tooltip>
    </div>
    <a-dropdown>
      <span class="ant-dropdown-link">
        <a-avatar :src="avatar" />
        {{ username }}
        <DownOutlined />
      </span>
      <template v-slot:overlay>
        <a-menu>
          <a-menu-item @click="logout">退出登录</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
  import { DownOutlined } from '@ant-design/icons-vue'
  import { computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useStore } from 'vuex'

  import { recordRoute } from '@/config'
  import VabIcon from '@/layout/vab-icon'
  import { pushWithQuery } from '@/utils/routes'

  const store = useStore()
  const router = useRouter()
  const route = useRoute()
  const avatar = computed(() => store.getters['user/avatar'])
  const username = computed(() => store.getters['user/username'])

  const logout = async () => {
    try {
      await store.dispatch('user/logout')
      if (recordRoute) {
        router.push(`/login?redirect=${route.fullPath}`)
      } else {
        router.push('/login')
      }
    } catch (error) {
      router.push(`/login?redirect=${route.fullPath}`)
    }
  }

  // 刷新
  const refresh = () => {
    router.replace('/refresh')
  }

  // 主页
  const onHome = () => {
    pushWithQuery('VideoAdmin')
  }
</script>
<style lang="scss" scoped>
  .vab-avatar {
    display: flex;
    .ant-dropdown-link {
      display: block;
      min-height: 64px;
      cursor: pointer;
    }
    .icon {
      display: block;
      margin-right: 20px;
      cursor: pointer;
    }
  }
</style>
