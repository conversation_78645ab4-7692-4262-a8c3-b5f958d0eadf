<template>
  <a-menu-item :key="routeChildren.path" @click.capture="handleLink">
    <span class="anticon">
      <vab-icon :icon="routeChildren.meta.icon" />
    </span>
    <span>{{ routeChildren.meta.title }}</span>
  </a-menu-item>
</template>

<script setup>
  import { useRoute, useRouter } from 'vue-router'

  import VabIcon from '@/layout/vab-icon'
  import { isExternal } from '@/utils/validate'

  const route = useRoute()
  const router = useRouter()
  const props = defineProps({
    item: Object,
    routeChildren: Object,
  })

  const handleLink = () => {
    const { fullPath: routePath, name } = props.routeChildren
    const target = props.routeChildren.meta.target
    if (target === '_blank') {
      if (isExternal(routePath)) window.open(routePath)
      else if (route.path !== routePath) window.open(routePath.href)
    } else {
      if (isExternal(routePath)) window.location.href = routePath
      else if (route.path !== routePath) router.push({ name })
    }
  }
</script>

<style lang="scss" scoped>
  .head-example {
    margin-left: 15px;
  }
</style>
