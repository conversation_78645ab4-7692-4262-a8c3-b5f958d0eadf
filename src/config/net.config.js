/**
 * @description 导出默认网路配置
 **/
module.exports = {
  //配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
  contentType: 'application/json;charset=UTF-8',
  //消息框消失时间
  messageDuration: 3000,
  //最长请求时间
  requestTimeout: 20000,
  //操作正常code，支持String、Array、int多种类型
  successCode: [200, 0],
  // 防抖时间
  debounceTime: 200,
  // 节流时间
  throttleTime: 2000,
  // 商务负责
  comRes: '商务负责',
  // 总部运营
  headTions: '总部运营',
  // 渠道运营
  stationTion: '渠道运营',
  // 百度地图IP定位
  ak: 'Db7Bl1vVX9MB7IrSB8ub5WI51PDuDotK',
  // 搜索框搜索size
  select_pageSize: 50,
  // 搜索框placeholder文字
  select_placeholder: '输入关键字搜索(默认前五十部)...',
  // 日期格式
  dateFormat: 'YYYY-MM-DD',
}
