<template>
  <!-- DOCX预览容器 -->
  <div class="docx-preview-container" v-loading="docxLoading">
    <!-- 使用 @vue-office/docx 进行预览 -->
    <VueOfficeDocx
      :src="fullDocxUrl"
      class="docx-viewer"
      @rendered="onDocxRendered"
      @error="onDocxError"
    />
  </div>

  <!-- 底部下载按钮 -->
  <div class="docx-download-bar">
    <el-button
      type="primary"
      color="rgb(22,119,225)"
      @click="downloadCurrentDocx"
      :disabled="!currentDocxUrl"
    >
      下载DOCX
    </el-button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import VueOfficeDocx from '@vue-office/docx'

/**
 * Props：和PDF预览组件保持一致的参数格式
 * @param {String} currentDocxUrl - DOCX文件地址（Blob URL或远程URL）
 * @param {String} currentDocxTitle - DOCX标题
 */
const props = defineProps({
  currentDocxUrl: {
    type: String,
    required: true,
  },
  currentDocxTitle: {
    type: String,
    default: 'DOCX预览'
  },
  spliceUrl: {
    type: String,
    default: "",
    required: false
  }
})

/**
 * 组件内部状态
 */
const docxLoading = ref(false) // 加载状态

// 计算完整的DOCX URL
const fullDocxUrl = computed(() => {
  return props.spliceUrl + props.currentDocxUrl
})

/**
 * DOCX渲染成功回调
 */
const onDocxRendered = () => {
  docxLoading.value = false
  console.log('DOCX渲染成功')
}

/**
 * DOCX渲染失败回调
 */
const onDocxError = (error) => {
  docxLoading.value = false
  console.error('DOCX渲染失败:', error)
  ElMessage.error('DOCX预览失败，请检查文件格式或重试')
}



/**
 * 下载DOCX文件
 */
const downloadCurrentDocx = () => {
  if (!props.currentDocxUrl) return
  const link = document.createElement('a')
  link.href = fullDocxUrl.value
  link.download = `${props.currentDocxTitle}.docx`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style scoped lang="scss">
.docx-preview-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.docx-viewer {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #f9fafb;
  border-radius: 4px;

  // 深度选择器，修改 @vue-office/docx 的样式
  :deep(.docx-wrapper) {
    background-color: transparent;
    padding: 20px;
  }

  :deep(.docx) {
    background-color: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 0 auto;
    max-width: 210mm;
  }
}

.docx-download-bar {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #fff;
  box-sizing: border-box;
}
</style>
