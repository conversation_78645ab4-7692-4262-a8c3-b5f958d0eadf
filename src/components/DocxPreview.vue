<template>
  <!-- DOCX预览容器 -->
  <div class="docx-preview-container" v-loading="docxLoading">
    <!-- 左侧缩略图侧边栏（和PDF预览结构一致） -->
    <div class="docx-sidebar">
      <div class="thumbnail-list">
        <div v-for="page in docxNumPages" :key="page" class="thumbnail-item" :class="{ active: currentPage === page }"
          @click="goToPage(page)">
          <canvas class="thumbnail-canvas" :ref="el => { if (el) thumbnailRefs[page] = el }"></canvas>
          <div class="page-number">第{{ page }}页</div>
          <!-- 缩略图加载状态 -->
          <div v-if="thumbnailLoading" class="thumbnail-loading">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧DOCX主视图（和PDF预览结构一致） -->
    <div class="docx-main">
      <!-- 页码导航与缩放控制（保留和PDF一致的交互） -->
      <!-- <div class="docx-controls">
          <el-button @click="prevPage" :disabled="currentPage <= 1">上一页</el-button>
          <span class="page-info">第 {{ currentPage }} 页 / 共 {{ docxNumPages }} 页</span>
          <el-button @click="nextPage" :disabled="currentPage >= docxNumPages">下一页</el-button>
          <el-button @click="zoomOut" :disabled="docxScale <= 0.7">缩小</el-button>
          <el-button @click="zoomIn" :disabled="docxScale >= 1.5">放大</el-button>
        </div> -->

      <!-- DOCX渲染容器（带滚动条） -->
      <div class="docx-viewer-wrapper">
        <div class="docx-viewer" ref="docxMainContainer">
          <!-- 分页渲染的DOCX内容 -->
          <div class="docx-page" v-for="page in docxPages" :key="page.index" :style="{
            transform: `scale(${docxScale})`,
            transformOrigin: 'top center',
            display: page.index === currentPage ? 'block' : 'none'
          }">
            <div v-html="page.content"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 底部下载按钮（和PDF预览一致） -->
  <!-- <div class="docx-download-bar">
      <el-button 
        type="primary" 
        color="rgb(22,119,225)" 
        @click="downloadCurrentDocx"
        :disabled="!currentDocxUrl"
      >
        下载DOCX
      </el-button>
    </div> -->
</template>

<script setup>
import { ref, watch, onUnmounted, nextTick, toRaw } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
// 1. 引入DOCX解析依赖
import { renderAsync } from 'docx-preview'
// 2. 引入生成缩略图依赖（将HTML转为canvas）
import html2canvas from 'html2canvas'

/**
 * Props：和PDF预览组件保持一致的参数格式
 * @param {String} currentDocxUrl - DOCX文件地址（Blob URL或远程URL）
 * @param {String} currentDocxTitle - DOCX标题
 */
const props = defineProps({
  currentDocxUrl: {
    type: String,
    required: true,
  },
  currentDocxTitle: {
    type: String,
    default: 'DOCX预览'
  },
  spliceUrl: {
    type: String,
    default: "",
    required: false
  }
})

/**
 * 组件内部状态（和PDF预览对齐）
 */
const docxLoading = ref(false) // 加载状态
const thumbnailLoading = ref(false) // 缩略图生成状态
const docxPages = ref([]) // 存储所有分页的HTML内容（{index: 页码, content: HTML字符串}）
const docxNumPages = ref(0) // 总页数
const currentPage = ref(1) // 当前显示页码
const docxScale = ref(1.0) // 缩放比例（默认1倍，范围0.7-1.5）
const thumbnailRefs = ref({}) // 缩略图画布引用
const docxMainContainer = ref(null) // 主视图容器引用
const tempDocxContainer = ref(null) // 临时容器（用于解析DOCX生成分页HTML，不渲染到DOM）

/**
 * 核心方法1：加载并解析DOCX文件
 * 步骤：1. 拉取DOCX文件 2. 解析为分页HTML 3. 存储分页数据 4. 渲染主视图和缩略图
 */
const loadDocx = async (url) => {
  if (!url) return
  docxLoading.value = true
  try {
    const response = await fetch(url)
    if (!response.ok) throw new Error(`DOCX加载失败，状态码: ${response.status}`)
    const docxBlob = await response.blob()

    // 创建临时容器
    if (!tempDocxContainer.value) {
      tempDocxContainer.value = document.createElement('div')
      tempDocxContainer.value.style.display = 'none'
      document.body.appendChild(tempDocxContainer.value)
    }

    // 解析DOCX
    await renderAsync(
      docxBlob,
      tempDocxContainer.value,
      null,
      {
        className: 'docx',
        inWrapper: true,
        renderHeaders: true,
        renderFooters: true,
        pageWidth: 210 * 3.78, // A4宽度转换为像素
        pageHeight: 297 * 3.78, // A4高度转换为像素
        pageMargin: 20 * 3.78 // A4边距转换为像素
      }
    )


    // 智能Word分页策略：基于内容高度和分页符
    let pageElements = [];

    // 获取docx-preview渲染后的主要容器
    const docxWrapper = tempDocxContainer.value.querySelector('.docx-wrapper');
    const docxContent = tempDocxContainer.value.querySelector('.docx') || docxWrapper || tempDocxContainer.value;


    // 检查文档的实际高度
    const docHeight = docxContent.scrollHeight;
    const pageHeight = 297 * 3.78; // A4高度

    // 直接使用智能分页算法 - docx-preview不会生成真正的分页

    // 等待DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 500));

    // 强制重新计算布局
    docxContent.style.display = 'none';
    docxContent.offsetHeight; // 触发重排
    docxContent.style.display = 'block';

    // 使用简单分页算法
    pageElements = await simplePagination(docxContent, pageHeight);

    // 确保每个页面都有内容
    pageElements = pageElements.filter(el => {
      if (!el) return false;
      const hasContent = el.textContent && el.textContent.trim().length > 0;
      const hasHTML = el.innerHTML && el.innerHTML.trim().length > 0;
      return hasContent || hasHTML;
    });

    if (pageElements.length === 0) {
      throw new Error("DOCX文件内容解析失败");
    }

    // 生成pages数组
    const pages = Array.from(pageElements).map((el, index) => {
      let content = el.outerHTML || el.innerHTML || '<div>内容加载失败</div>';

      // 如果内容为空或只有空白，尝试获取原始HTML
      if (!content || content.trim().length === 0 || content === '<div>内容加载失败</div>') {
        content = docxContent.innerHTML || '<div>内容加载失败</div>';
      }

      // 为article元素创建完整的页面结构
      if (content.includes('<article')) {
        content = `
          <div style="
            width: 100%; 
            min-height: 297mm; 
            background: #fff; 
            padding: 20mm; 
            box-sizing: border-box;
            display: block;
            overflow: visible;
          ">
            ${content}
          </div>
        `;
      } else {
        content = `
          <div style="
            width: 100%; 
            min-height: 297mm; 
            background: #fff; 
            padding: 20mm; 
            box-sizing: border-box;
            display: block;
            overflow: visible;
          ">
            ${content}
          </div>
        `;
      }

      return {
        index: index + 1,
        content: content
      };
    })



    docxPages.value = pages
    docxNumPages.value = pages.length

    await nextTick()

    // 强制重新渲染，解决高度为0的问题
    setTimeout(() => {
      if (docxMainContainer.value) {
        // 强制重新计算布局
        docxMainContainer.value.style.display = 'none';
        docxMainContainer.value.offsetHeight; // 触发重排
        docxMainContainer.value.style.display = 'block';

        // 检查页面容器
        const pageContainers = docxMainContainer.value.querySelectorAll('.docx-page');

        pageContainers.forEach((page, index) => {
          if (page.scrollHeight === 0) {
            page.style.height = 'auto';
            page.style.minHeight = '297mm';
            page.style.display = 'block';
            page.style.overflow = 'visible';
          }
        });
      }
    }, 100);

    renderCurrentPage()

    // 延迟生成缩略图，确保DOM完全渲染
    setTimeout(async () => {
      // 等待DOM完全渲染，确保所有canvas元素都已创建
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 300))

      await renderThumbnails()
    }, 800)

  } catch (error) {
    console.error('DOCX加载失败:', error)
    ElMessage.error('DOCX预览失败，请检查文件格式或重试')
    docxLoading.value = false
  }
}

/**
 * 核心方法2：渲染当前页码的主视图
 */
const renderCurrentPage = () => {
  if (!docxMainContainer.value || docxPages.value.length === 0) return
  // 切换页码时，主视图滚动到顶部
  docxMainContainer.value.scrollTop = 0
}

/**
 * 图片去重函数：保留第一张图片，去掉重复的
 */
const deduplicateImages = (allElements) => {
  const processedImages = new Set();

  return Array.from(allElements).filter((el) => {
    const textContent = el.textContent?.trim() || '';
    let imgSrc = null;

    // 检查元素本身是否是IMG标签
    if (el.tagName === 'IMG') {
      imgSrc = el.src;
    } else {
      // 检查元素内部是否包含img标签
      const images = el.querySelectorAll('img');
      if (images.length > 0 && textContent === '') {
        imgSrc = images[0]?.src;
      }
    }

    if (imgSrc) {
      if (processedImages.has(imgSrc)) {
        return false; // 跳过重复的图片
      } else {
        processedImages.add(imgSrc);
        return true; // 保留新图片
      }
    }

    return true; // 保留非图片元素
  });
};

/**
 * 简单分页：不依赖高度测量，直接按元素数量分页
 */
const simplePagination = async (contentElement, pageHeight) => {
  const pages = [];
  const allElements = contentElement.querySelectorAll('p, h1, h2, h3, h4, h5, h6, div, table, ul, ol, li, img, figure');

  if (allElements.length === 0) {
    return [contentElement];
  }

  // 使用通用去重函数
  const filteredElements = deduplicateImages(allElements);

  // 每页大约15个元素，减少重复
  const elementsPerPage = 15;
  const totalPages = Math.ceil(filteredElements.length / elementsPerPage);

  for (let i = 0; i < totalPages; i++) {
    const startIndex = i * elementsPerPage;
    const endIndex = Math.min(startIndex + elementsPerPage, filteredElements.length);
    const pageElements = filteredElements.slice(startIndex, endIndex);


    const page = createPageContainer();
    pageElements.forEach(el => {
      page.appendChild(el.cloneNode(true));
    });

    pages.push(page);
  }

  return pages;
}


/**
 * 创建页面容器
 */
const createPageContainer = () => {
  const pageDiv = document.createElement('div');
  pageDiv.className = 'page';
  pageDiv.style.width = '210mm';
  pageDiv.style.minHeight = '297mm';
  pageDiv.style.padding = '20mm';
  pageDiv.style.boxSizing = 'border-box';
  pageDiv.style.backgroundColor = '#fff';
  pageDiv.style.marginBottom = '20px';
  pageDiv.style.position = 'relative';
  pageDiv.style.overflow = 'visible';
  return pageDiv;
}

/**
 * 备用方法：生成简单的文本缩略图
 */
const generateTextThumbnail = (canvas, pageIndex) => {
  const ctx = canvas.getContext('2d')
  const width = 200
  const height = 280

  canvas.width = width
  canvas.height = height

  // 绘制背景
  ctx.fillStyle = '#f8f9fa'
  ctx.fillRect(0, 0, width, height)

  // 绘制边框
  ctx.strokeStyle = '#e5e7eb'
  ctx.lineWidth = 1
  ctx.strokeRect(0, 0, width, height)

  // 绘制页码
  ctx.fillStyle = '#374151'
  ctx.font = 'bold 16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`第${pageIndex}页`, width / 2, height / 2 - 20)

  // 绘制文档图标
  ctx.fillStyle = '#6b7280'
  ctx.font = '12px Arial'
  ctx.fillText('DOCX文档', width / 2, height / 2 + 10)

  // 设置样式
  canvas.style.display = 'block'
  canvas.style.visibility = 'visible'
  canvas.style.opacity = '1'
  canvas.style.width = '100%'
  canvas.style.height = 'auto'
  canvas.style.maxHeight = '120px'
  canvas.style.minHeight = '80px'
  canvas.style.border = '1px solid #e5e7eb'
  canvas.style.borderRadius = '2px'
  canvas.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)'
}

/**
 * 核心方法3：生成所有页码的缩略图（用html2canvas将HTML转为canvas）
 */
const renderThumbnails = async () => {
  if (docxPages.value.length === 0) return

  // 等待DOM完全渲染
  await nextTick()

  // 额外等待确保canvas元素已创建
  await new Promise(resolve => setTimeout(resolve, 200))

  thumbnailLoading.value = true

  // 检查html2canvas是否可用
  let useHtml2Canvas = true
  try {
    // 测试html2canvas是否可用
    const testDiv = document.createElement('div')
    testDiv.innerHTML = '<div>test</div>'
    testDiv.style.position = 'absolute'
    testDiv.style.left = '-9999px'
    document.body.appendChild(testDiv)

    // 尝试使用html2canvas
    await html2canvas(testDiv, { scale: 0.1 })
    document.body.removeChild(testDiv)
  } catch (error) {
    useHtml2Canvas = false
  }

  // 遍历所有页面，生成缩略图
  for (const page of docxPages.value) {
    try {
      // 检查canvas引用是否存在
      const canvas = thumbnailRefs.value[page.index]
      if (!canvas) {
        continue
      }

      // 如果html2canvas不可用，直接使用文本缩略图
      if (!useHtml2Canvas) {
        generateTextThumbnail(canvas, page.index)
        continue
      }

      // 创建临时DOM，用于生成缩略图
      const tempPageEl = document.createElement('div')
      tempPageEl.innerHTML = page.content

      // 设置样式，确保内容可见
      tempPageEl.style.width = '210mm' // A4宽度
      tempPageEl.style.height = '297mm' // A4高度
      tempPageEl.style.padding = '20mm' // A4边距
      tempPageEl.style.boxSizing = 'border-box'
      tempPageEl.style.backgroundColor = '#fff'
      tempPageEl.style.position = 'absolute'
      tempPageEl.style.left = '-9999px' // 移到屏幕外，但保持可见
      tempPageEl.style.top = '0'
      tempPageEl.style.display = 'block'
      tempPageEl.style.overflow = 'visible'
      tempPageEl.style.zIndex = '-1' // 确保在后台

      document.body.appendChild(tempPageEl)

      // 等待DOM渲染
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      try {
        const canvasObj = await html2canvas(tempPageEl, {
          scale: 0.15, // 降低缩放比例，提高性能
          useCORS: true, // 解决跨域图片问题
          logging: false, // 关闭日志
          backgroundColor: '#fff', // 白色背景
          allowTaint: true, // 允许跨域图片
          foreignObjectRendering: false, // 关闭foreignObject渲染，提高兼容性
          width: 210 * 3.78, // 210mm转换为像素（1mm ≈ 3.78px）
          height: 297 * 3.78, // 297mm转换为像素
          scrollX: 0,
          scrollY: 0,
          windowWidth: 210 * 3.78,
          windowHeight: 297 * 3.78
        })

        if (canvasObj.width === 0 || canvasObj.height === 0) {
          document.body.removeChild(tempPageEl)
          continue
        }

        // 将生成的canvas画到缩略图画布上
        const ctx = canvas.getContext('2d')

        // 设置canvas尺寸
        canvas.width = canvasObj.width
        canvas.height = canvasObj.height

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // 绘制图像
        ctx.drawImage(canvasObj, 0, 0)

        // 设置canvas样式
        canvas.style.display = 'block'
        canvas.style.visibility = 'visible'
        canvas.style.opacity = '1'
        canvas.style.width = '100%'
        canvas.style.height = 'auto'
        canvas.style.maxHeight = '120px'
        canvas.style.minHeight = '80px'
        canvas.style.border = '1px solid #e5e7eb'
        canvas.style.borderRadius = '2px'
        canvas.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)'

        // 触发重绘
        canvas.offsetHeight

      } catch (html2canvasError) {
        // 如果html2canvas失败，使用备用方法生成文本缩略图
        generateTextThumbnail(canvas, page.index)
      }

      // 移除临时DOM
      if (document.body.contains(tempPageEl)) {
        document.body.removeChild(tempPageEl)
      }

    } catch (error) {
      // 缩略图生成失败，静默处理
    }
  }

  thumbnailLoading.value = false
  // 缩略图生成完成后，确保主loading也取消
  docxLoading.value = false
}

/**
 * 交互方法：和PDF预览保持一致
 */
// 跳转至指定页码
const goToPage = (pageNum) => {
  if (pageNum < 1 || pageNum > docxNumPages.value) return
  currentPage.value = pageNum
  renderCurrentPage()
}
// 上一页
const prevPage = () => goToPage(currentPage.value - 1)
// 下一页
const nextPage = () => goToPage(currentPage.value + 1)
// 放大（步长0.1）
const zoomIn = () => {
  if (docxScale.value >= 1.5) return
  docxScale.value = Number((docxScale.value + 0.1).toFixed(1))
  renderCurrentPage()
}
// 缩小（步长0.1）
const zoomOut = () => {
  if (docxScale.value <= 0.7) return
  docxScale.value = Number((docxScale.value - 0.1).toFixed(1))
  renderCurrentPage()
}

/**
 * 下载DOCX文件（和PDF预览逻辑一致）
 */
const downloadCurrentDocx = () => {
  if (!props.currentDocxUrl) return
  const link = document.createElement('a')
  link.href = props.spliceUrl + props.currentDocxUrl
  link.download = `${props.currentDocxTitle}.docx` // 保持文件名一致
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 资源清理：避免内存泄漏（和PDF预览逻辑一致）
 */
const cleanupDocxResources = () => {
  try {
    // 1. 清空分页数据
    docxPages.value = []
    docxNumPages.value = 0
    currentPage.value = 1
    docxScale.value = 1.0
    thumbnailRefs.value = {}

    // 2. 移除临时容器
    if (tempDocxContainer.value && document.body.contains(tempDocxContainer.value)) {
      document.body.removeChild(tempDocxContainer.value)
      tempDocxContainer.value = null
    }

  } catch (error) {
    console.warn('DOCX资源清理失败:', error)
  }
}

/**
 * 监听Props变化：弹窗显示时加载DOCX，隐藏时清理资源（和PDF预览一致）
 */
// watch(
//   [() => props.visible, () => props.currentDocxUrl],
//   async ([newVisible, newUrl]) => {
//     if (newVisible && newUrl) {
//       await loadDocx(newUrl)
//     } else if (!newVisible) {
//       cleanupDocxResources()
//     }
//   },
//   { immediate: true }
// )
watch(
  [() => props.currentDocxUrl],
  async (newUrl) => {
    if (newUrl) {
      await loadDocx(newUrl)
    } else if (!newVisible) {
      cleanupDocxResources()
    }
  },
  { immediate: true }
)

/**
 * 组件卸载：强制清理资源（和PDF预览一致）
 */
// onUnmounted(() => {
//   cleanupDocxResources()
// })
</script>

<style scoped lang="scss">
// 完全复用PDF预览的样式结构，只修改前缀（pdf→docx），保证视觉一致
.docx-preview-container {
  display: flex;
  gap: 16px;
  padding: 10px 0;
  overflow: auto;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}

// 左侧缩略图侧边栏（和PDF样式完全一致）
.docx-sidebar {
  width: 160px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  border-right: 1px solid #e5e7eb;
  padding-right: 8px;
  box-sizing: border-box;

  .thumbnail-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 5px 0;
  }

  .thumbnail-item {
    cursor: pointer;
    border: 2px solid transparent;
    padding: 6px;
    border-radius: 4px;
    text-align: center;
    transition: all 0.2s;
    box-sizing: border-box;

    &.active {
      border-color: #409eff;
      background-color: #f0f7ff;
    }

    &:hover {
      background-color: #f9fafb;
    }

    .thumbnail-canvas {
      width: 100% !important;
      height: auto !important;
      max-height: 120px;
      min-height: 80px;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      display: block !important;
      margin: 0 auto;
      background: #fff;
      border: 1px solid #e5e7eb;
      visibility: visible !important;
      opacity: 1 !important;
    }

    .page-number {
      font-size: 12px;
      color: #6b7280;
      margin-top: 4px;
      white-space: nowrap;
    }

    .thumbnail-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #409eff;
      font-size: 16px;
    }
  }

  // 滚动条美化
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e5e7eb;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #d1d5db;
  }
}

// 右侧主视图（和PDF样式完全一致）
.docx-main {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 控制栏
  .docx-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #fff;
    margin-bottom: 8px;
    box-sizing: border-box;

    .page-info {
      font-size: 14px;
      color: #374151;
      font-weight: 500;
    }

    el-button {
      padding: 4px 12px;
      font-size: 13px;
    }
  }

  // 主视图容器（带滚动条）
  .docx-viewer-wrapper {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #f9fafb;
    border-radius: 4px;
    padding: 16px 0;
    display: flex;
    justify-content: center;
    box-sizing: border-box;

    // 滚动条美化
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #e5e7eb;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #d1d5db;
    }

    .docx-viewer {
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 0 16px;
      box-sizing: border-box;

      // DOCX页面样式（模拟A4纸）
      .docx-page {
        width: 100%;
        height: 100%;
        // width: 210mm; // A4标准宽度
        // min-height: 297mm; // A4标准高度
        background-color: #fff;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px; // 页面间距
        display: block;
        overflow: visible;
      }
    }
  }
}

// 底部下载栏（和PDF样式完全一致）
.docx-download-bar {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #fff;
  box-sizing: border-box;
}
</style>