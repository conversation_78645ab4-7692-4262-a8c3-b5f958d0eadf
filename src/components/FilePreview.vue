<template>
  <div class="file-preview-container">
    <!-- PDF 预览 -->
    <!-- <VueOfficePdf v-if="fileType === 'pdf'" :src="previewUrl" class="preview-box" /> -->
    <PdfPreview v-if="fileType === 'pdf'" :currentPdfUrl="previewUrl" :currentPdfTitle="fileName">
    </PdfPreview>

    <!-- Word 预览 -->
    <!-- <VueOfficeDocx v-else-if="fileType === 'docx'" :src="previewUrl" class="preview-box" /> -->
    <DocxPreview v-else-if="fileType === 'docx'" :current-docx-url="previewUrl" :current-docx-title="fileName" />

    <!-- Excel 预览 -->
    <VueOfficeExcel v-else-if="fileType === 'xlsx'" :src="previewUrl" class="preview-box" />

    <!-- 其他文件 -->
    <div v-else class="other-preview">
      <p>暂不支持预览该文件类型，请下载查看。</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import VueOfficePdf from "@vue-office/pdf";
import DocxPreview from './DocxPreview.vue';
import PdfPreview from "./PdfPreview.vue";

const props = defineProps({
  fileType: {
    type: String,
    required: true
  },  //文件类型
  fileName: {
    type: String,
    default: "download"
  },
  fileUrl: {
    type: String,
    required: true
  }, // 后端返回的文件地址
  spliceUrl: {
    type: String,
    default: ""
  }, // 域名前缀（如果需要拼接）
});

const previewUrl = computed(() => {
  return props.spliceUrl + props.fileUrl
})

// 点击下载
const downloadFile = () => {
  const link = document.createElement("a");
  link.href = previewUrl.value;
  link.download = props.fileName;
  link.click();
};

onMounted(() => {
});
</script>

<style scoped>
.file-preview-container {
  width: 100%;
  height: 100%;
}

.other-preview {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 80%;
  width: 80%;
}

.preview-box {
  width: 100%;
  height: 100%;
  overflow: auto;
  /* 保证内容超出时出现滚动条 */
  box-sizing: border-box;
}

:deep(.docx-wrapper) {
  background-color: transparent;
}
</style>
