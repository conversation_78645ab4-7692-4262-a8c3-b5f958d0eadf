<template>
  <a-select
    v-model:value="formState[formName]"
    :allow-clear="true"
    :default-active-first-option="false"
    :fieldNames="{
      label: 'name',
      value: 'id',
    }"
    :filter-option="false"
    :not-found-content="'没有搜索到...'"
    :options="options"
    :placeholder="select_placeholder"
    show-search
    style="width: 300px"
    @change="handleChange"
    @focus="handleFocus"
    @search="handleSearch"
  />
</template>

<script setup>
  import { toRefs } from '@vue/reactivity'

  import { select_placeholder } from '@/config'

  const emits = defineEmits(['focus', 'search', 'change'])
  const props = defineProps(['formState', 'options', 'formName'])
  const { formState, options, formName } = toRefs(props)

  const handleFocus = () => emits('focus')
  const handleSearch = (name) => emits('search', name)
  const handleChange = (name) => emits('change', name)
</script>

<style lang="scss" scoped>
  .default {
  }
</style>
