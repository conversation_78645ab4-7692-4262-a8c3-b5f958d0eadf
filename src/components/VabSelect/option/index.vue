<template>
  <a-select
    v-model:value="formState[formName]"
    allow-clear
    :disabled="disabled"
    :options="options"
    show-search
    :style="style || 'width: 160px'"
    @change="onChange"
  />
</template>

<script setup>
  import { toRefs } from '@vue/reactivity'
  const emits = defineEmits(['change'])
  const props = defineProps([
    'formState',
    'options',
    'formName',
    'disabled',
    'style',
  ])

  const { formState, options, formName, disabled, style } = toRefs(props)
  const onChange = (value) => {
    emits('change', value)
  }
</script>
<style lang="scss" scoped>
  .default {
  }
</style>
