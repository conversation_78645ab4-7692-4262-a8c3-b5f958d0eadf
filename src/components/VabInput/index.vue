<template>
  <a-input-search
    v-model:value="formState[formName]"
    allowClear
    :disabled="disabled"
    enter-button
    placeholder="输入资产名称快速查询"
    showCount
    @pressEnter="handlePressEnter"
    @search="handleSearch"
  />
</template>

<script setup>
  import { toRefs } from '@vue/reactivity'

  const emits = defineEmits(['search', 'pressEnter'])
  const props = defineProps(['formState', 'formName', 'disabled'])
  const { formState, formName, disabled } = toRefs(props)

  const handleSearch = (name) => {
    console.log(name)
    emits('search', name)
  }
  const handlePressEnter = () => {
    emits('pressEnter')
  }
</script>

<style lang="sass" scoped></style>
