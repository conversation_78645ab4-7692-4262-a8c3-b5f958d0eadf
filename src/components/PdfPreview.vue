<template>
  <div class="pdf-preview-container" v-loading="pdfLoading">
    <div class="pdf-sidebar">
      <div class="thumbnail-list">
        <div v-for="page in pdfNumPages" :key="page" class="thumbnail-item" :class="{ active: currentPage === page }"
          @click="goToPage(page)">
          <canvas class="thumbnail-canvas" :ref="el => { thumbnailRefs[page] = el }"></canvas>
          <div class="page-number">第{{ page }}页</div>
        </div>
      </div>
    </div>
    <div class="pdf-main">
      <!-- <div class="pdf-controls">
            <el-button @click="prevPage" :disabled="currentPage <= 1">上一页</el-button>
            <span class="page-info">第 {{ currentPage }} 页 / 共 {{ pdfNumPages }} 页</span>
            <el-button @click="nextPage" :disabled="currentPage >= pdfNumPages">下一页</el-button>
            <el-button @click="zoomOut" :disabled="pdfScale <= 0.5">缩小</el-button>
            <el-button @click="zoomIn" :disabled="pdfScale >= 2">放大</el-button>
            <el-button @click="downloadCurrentPdf">下载</el-button>
          </div> -->
      <div class="pdf-viewer">
        <canvas class="pdf-canvas" ref="pdfCanvas"></canvas>
      </div>

    </div>
  </div>
</template>

<script setup>
import { nextTick, onMounted, onUnmounted, ref, toRaw, watch } from 'vue';
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf.mjs'
import PdfWorker from 'pdfjs-dist/legacy/build/pdf.worker.mjs?worker'
pdfjsLib.GlobalWorkerOptions.workerPort = new PdfWorker()

const pdfLoading = ref(false)
const pdfDoc = ref(null)
const pdfNumPages = ref(0)
const currentPage = ref(1)
const pdfScale = ref(1.5)
const pdfCanvas = ref(null)
const thumbnailRefs = ref({})

const props = defineProps({
  currentPdfUrl: {
    type: String,
    required: true
  },
  currentPdfTitle: {
    type: String,
    required: false,
    default: "download"
  },
  selectedItem: {
    type: Object,
    required: false,
    default: () => { }
  },
  spliceUrl: {
    type: String,
    default: "",
    required: false
  }
})

// 添加PDF相关函数 // 加载 PDF（修正为 ESM api）
const loadPdf = async (url) => {
  // 清空旧缩略图 refs，避免页码错位
  thumbnailRefs.value = {}
  try {
    pdfLoading.value = true

    // // 将文件转换为ArrayBuffer
    let arrayBuffer = null;
    if (url) {
      let tmpUrl = props.spliceUrl + url

      const response = await fetch(tmpUrl)
      arrayBuffer = await response.arrayBuffer()
    }
    console.log("arrayBuffer============================")
    console.log(arrayBuffer)

    // 加载PDF文档
    // const loadingTask = pdfjsLib.getDocument({ url })
    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer })
    pdfDoc.value = await loadingTask.promise
    pdfNumPages.value = pdfDoc.value.numPages

    // 重置当前页面
    currentPage.value = 1
    await nextTick()

    // 渲染第一页
    await renderPage(currentPage.value)

    // 渲染所有缩略图
    await renderThumbnails()
  } catch (error) {
    console.error('PDF加载错误:', error)
    throw error
  } finally {
    pdfLoading.value = false
  }
}

const renderPage = async (num) => {
  if (!pdfDoc.value) return

  try {
    // 使用 toRaw 获取原始对象，避免Vue响应式代理的干扰
    const rawPdfDoc = toRaw(pdfDoc.value)
    const page = await rawPdfDoc.getPage(num)
    const viewport = page.getViewport({ scale: pdfScale.value })
    const canvas = pdfCanvas.value
    if (!canvas) return
    const context = canvas.getContext('2d')

    canvas.height = viewport.height
    canvas.width = viewport.width

    const renderContext = {
      canvasContext: context,
      viewport: viewport
    }

    await page.render(renderContext).promise
  } catch (error) {
    console.error('页面渲染错误:', error)
  }
}

const renderThumbnails = async () => {
  if (!pdfDoc.value) return

  // 等待DOM更新完成
  await nextTick()

  // 使用 toRaw 获取原始对象，避免Vue响应式代理的干扰
  const rawPdfDoc = toRaw(pdfDoc.value)

  for (let i = 1; i <= pdfNumPages.value; i++) {
    try {
      const page = await rawPdfDoc.getPage(i)
      const viewport = page.getViewport({ scale: 0.3 }) // 缩略图使用较小的缩放比例
      const canvas = thumbnailRefs.value[i]

      if (!canvas) continue

      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width

      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }

      await page.render(renderContext).promise
    } catch (error) {
      console.error(`缩略图 ${i} 渲染错误:`, error)
    }
  }
}

const goToPage = (num) => {
  if (num < 1 || num > pdfNumPages.value) return
  currentPage.value = num
  renderPage(num)
}

const prevPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1)
  }
}

const nextPage = () => {
  if (currentPage.value < pdfNumPages.value) {
    goToPage(currentPage.value + 1)
  }
}

const zoomIn = () => {
  if (pdfScale.value < 2) {
    pdfScale.value += 0.25
    renderPage(currentPage.value)
  }
}

const zoomOut = () => {
  if (pdfScale.value > 0.5) {
    pdfScale.value -= 0.25
    renderPage(currentPage.value)
  }
}

const downloadCurrentPdf = () => {
  if (props.currentPdfUrl) {
    const link = document.createElement('a')
    link.href = props.currentPdfUrl
    link.download = props.currentPdfTitle + '.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

const clearRes = () => {
  // 清理资源
  if (pdfDoc.value) {
    // 使用 toRaw 获取原始对象，避免Vue响应式代理的干扰
    try {
      const rawPdfDoc = toRaw(pdfDoc.value)
      if (rawPdfDoc && typeof rawPdfDoc.destroy === 'function') {
        rawPdfDoc.destroy()
      }
    } catch (error) {
      console.warn('PDF文档销毁时出现错误:', error)
    }
    pdfDoc.value = null
  }
  thumbnailRefs.value = {}
}

// 点击下载
const handleDownload = async (item) => {

  // console.log("点击下载项==================")
  // console.log(item)

  const res = await adminColumns.qbExamPapersDownload.get({
    examPaperId: item.id,
    show_page_id: 29
  })
  // console.log("下载试卷结果=====================")
  // console.log(res)
  // console.log(res.data.data.file_url)

  // if (props.currentPdfUrl) {
  if (res.data.data.file_url) {
    const link = document.createElement('a');
    link.href = res.data.data.file_url;
    link.download = res.data.data.file_url + '.pdf';
    link.click();
  }
}

watch(props.currentPdfUrl, async () => {
  pdfLoading.value = true
  await loadPdf(`${props.currentPdfUrl}`)
  pdfLoading.value = false
})

onMounted(async () => {
  pdfLoading.value = true
  // 加载PDF文档
  await loadPdf(`${props.currentPdfUrl}`)

  pdfLoading.value = false
})

onUnmounted(() => {
  clearRes()
})
</script>

<style scoped lang="scss">
.pdf-preview-container {
  display: flex;
  height: 100%;
  overflow: hidden;

  .pdf-sidebar {
    width: 150px;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    padding: 10px;

    .thumbnail-list {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .thumbnail-item {
        cursor: pointer;
        border: 2px solid transparent;
        padding: 5px;
        border-radius: 4px;
        text-align: center;

        &.active {
          border-color: #409eff;
          background-color: #f5f7fa;
        }

        &:hover {
          background-color: #f0f2f5;
        }

        .thumbnail-canvas {
          width: 100%;
          height: auto;
          max-height: 120px;
          margin-bottom: 5px;
        }

        .page-number {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .pdf-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .pdf-controls {
      padding: 10px;
      display: flex;
      align-items: center;
      gap: 10px;
      border-bottom: 1px solid #ddd;
      background-color: #f5f7fa;

      .page-info {
        margin: 0 15px;
        font-weight: bold;
      }
    }

    .pdf-viewer {
      flex: 1;
      overflow: auto;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 20px;
      height: 100%;

      .pdf-canvas {
        // height: 90%;
        border: 1px solid #ddd;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>