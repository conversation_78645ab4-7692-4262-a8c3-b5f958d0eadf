import {
  createRouter,
  createWebHistory,
  // createWebHashHistory,
} from 'vue-router'

import Layout from '@/layout'

export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/403.vue'),
    meta: {
      title: '403',
      icon: 'error-warning-line',
    },
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404.vue'),
    meta: {
      title: '404',
      icon: 'error-warning-line',
    },
    hidden: true,
  },
]
export const asyncRoutes = [
  {
    path: '/',
    name: 'redirectRoot',
    redirect: '/index',
    hidden: true,
  },
  {
    path: '/intelligentLessonPreparation',
    name: 'IntelligentLessonPreparation',
    component: Layout,
    meta: {
      title: '智能备课',
      icon: 'money-cny-box-fill',
    },
    // hidden: true,
    children: [
      {
        path: 'lessonPreparationMaterials',
        name: 'LessonPreparationMaterials',
        component: () => import('@/views/lessonPreparationMaterials/index.vue'),
        meta: {
          title: '备课资料',
          icon: 'money-cny-box-fill',
        },
      },
      {
        path: 'teachingMaterialManagement',
        name: 'TeachingMaterialManagement',
        component: () => import('@/views/teachingMaterialManagement/index.vue'),
        meta: {
          title: '教材管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'knowledgePointManagement',
        name: 'KnowledgePointManagement',
        component: () => import('@/views/knowledgePointManagement/index.vue'),
        meta: {
          title: '知识点管理',
          icon: 'layout-column-fill',
        },
      },
    ],
  },
  {
    path: '/analysis',
    name: 'Root',
    component: Layout,
    meta: {
      title: '分析页',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    // hidden: true,
    children: [
      {
        path: '',
        name: 'AnalysisAdmin',
        component: () => import('@/views/分析页/index.vue'),
        meta: {
          title: '分析页',
          icon: 'bar-chart-2-line',
          // affix: true,
        },
      },
    ],
  },
  {
    path: '/playLogs',
    name: 'PlayLogs',
    component: Layout,
    meta: {
      title: '播放数据',
      icon: 'play-mini-line',
      roles: ['administrator', 'admin.normal'],
    },
    // hidden: true,
    children: [
      {
        path: '',
        name: 'PlayLogsAdmin',
        component: () => import('@/views/播放数据/index.vue'),
        meta: {
          title: '播放数据',
          icon: 'play-mini-line',
        },
      },
    ],
  },
  {
    path: '/orderLogs',
    name: 'OrderLogs',
    component: Layout,
    meta: {
      title: '订购数据',
      icon: 'money-cny-box-fill',
      roles: ['administrator', 'admin.normal'],
    },
    // hidden: true,
    children: [
      {
        path: '',
        name: 'OrderLogsAdmin',
        component: () => import('@/views/订购数据/index.vue'),
        meta: {
          title: '订购数据',
          icon: 'money-cny-box-fill',
        },
      },
    ],
  },
  {
    path: '/orderProductLogs',
    name: 'OrderProductLogs',
    component: Layout,
    meta: {
      title: '订购产品包数据',
      icon: 'money-cny-box-fill',
      roles: ['administrator', 'admin.normal'],
    },
    // hidden: true,
    children: [
      {
        path: '',
        name: 'OrderProductLogsAdmin',
        component: () => import('@/views/订购产品包数据/index.vue'),
        meta: {
          title: '订购产品包数据',
          icon: 'money-cny-box-fill',
        },
      },
    ],
  },
  {
    path: '/pagesList',
    name: 'PagesList',
    component: Layout,
    meta: {
      title: '页面列表',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'PagesListAdmin',
        component: () => import('@/views/页面列表/index.vue'),
        meta: {
          title: '页面列表',
          icon: 'list-check',
        },
      },
    ],
  },
  {
    path: '/k12',
    name: 'K12',
    component: Layout,

    meta: {
      title: 'k12',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: '',
        name: 'K12PrimarySchool',
        component: () => import('@/views/k12/小学/index.vue'),
        meta: {
          title: '小学',
          icon: 'home-2-line',
        },
      },
      {
        path: 'k12JuniorHighSchool',
        name: 'K12JuniorHighSchool',
        component: () => import('@/views/k12/初中/index.vue'),
        meta: {
          title: '初中',
          icon: 'home-2-line',
        },
      },
      {
        path: 'k12HighSchool',
        name: 'K12HighSchool',
        component: () => import('@/views/k12/高中/index.vue'),
        meta: {
          title: '高中',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/questionBank',
    name: 'QuestionBank',
    component: Layout,
    meta: {
      title: '题库组卷',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: '',
        name: 'QuestionBankAdmin',
        component: () => import('@/views/题库组卷/index.vue'),
        meta: {
          title: '题库组卷',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/division',
    name: 'Division',
    component: Layout,
    meta: {
      title: '名师讲堂',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: '',
        name: 'DivisionAdmin',
        component: () => import('@/views/名师讲堂/index.vue'),
        meta: {
          title: '名师讲堂',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/newOriental',
    name: 'NewOriental',
    component: Layout,
    meta: {
      title: '新东方',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: 'primarySchool',
        name: 'NewOrientalPrimarySchool',
        component: () => import('@/views/新东方/小学/index.vue'),
        meta: {
          title: '小学',
          icon: 'home-2-line',
        },
      },
      {
        path: 'juniorHighSchool',
        name: 'NewOrientalJuniorHighSchool',
        component: () => import('@/views/新东方/初中/index.vue'),
        meta: {
          title: '初中',
          icon: 'home-2-line',
        },
      },
      {
        path: 'highSchool',
        name: 'NewOrientalHighSchool',
        component: () => import('@/views/新东方/高中/index.vue'),
        meta: {
          title: '高中',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/futureSchool',
    name: 'FutureSchool',
    component: Layout,
    meta: {
      title: '未来学校',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: 'primarySchool',
        name: 'FutureSchoolPrimarySchool',
        component: () => import('@/views/未来学校/小学/index.vue'),
        meta: {
          title: '小学',
          icon: 'home-2-line',
        },
      },
      {
        path: 'juniorHighSchool',
        name: 'FutureSchoolJuniorHighSchool',
        component: () => import('@/views/未来学校/初中/index.vue'),
        meta: {
          title: '初中',
          icon: 'home-2-line',
        },
      },
      {
        path: 'highSchool',
        name: 'FutureSchoolHighSchool',
        component: () => import('@/views/未来学校/高中/index.vue'),
        meta: {
          title: '高中',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/primarySchoolfusionPackage',
    name: 'PrimarySchoolFusionPackage',
    component: Layout,
    meta: {
      title: '融合包-小学',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: 'primarySchoolFirstLevel',
        name: 'PrimarySchoolFirstLevel',
        component: () => import('@/views/融合包/小学/一级/index.vue'),
        meta: {
          title: '一级',
          icon: 'home-2-line',
        },
      },
      {
        path: 'primarySchoolSecondLevel',
        name: 'PrimarySchoolSecondLevel',
        component: () => import('@/views/融合包/小学/二级/index.vue'),
        meta: {
          title: '二级',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/juniorHighSchoolfusionPackage',
    name: 'JuniorHighSchoolFusionPackage',
    component: Layout,
    meta: {
      title: '融合包-初中',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: 'juniorHighSchoolFirstLevel',
        name: 'JuniorHighSchoolFirstLevel',
        component: () => import('@/views/融合包/初中/一级/index.vue'),
        meta: {
          title: '一级',
          icon: 'home-2-line',
        },
      },
      {
        path: 'juniorHighSchoolSecondLevel',
        name: 'JuniorHighSchoolSecondLevel',
        component: () => import('@/views/融合包/初中/二级/index.vue'),
        meta: {
          title: '二级',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/highSchoolfusionPackage',
    name: 'HighSchoolFusionPackage',
    component: Layout,
    meta: {
      title: '融合包-高中',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: 'highSchoolFirstLevel',
        name: 'HighSchoolFirstLevel',
        component: () => import('@/views/融合包/高中/一级/index.vue'),
        meta: {
          title: '一级',
          icon: 'home-2-line',
        },
      },
      {
        path: 'highSchoolSecondLevel',
        name: 'HighSchoolSecondLevel',
        component: () => import('@/views/融合包/高中/二级/index.vue'),
        meta: {
          title: '二级',
          icon: 'home-2-line',
        },
      },
    ],
  },

  {
    path: '/index',
    name: 'Video',
    component: Layout,
    meta: {
      title: '视频课程管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.cp', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'VideoAdmin',
        component: () => import('@/views/视频课程管理/index.vue'),
        meta: {
          title: '视频课程管理',
          icon: 'play-circle-line',
        },
      },
    ],
  },
  {
    path: '/testQuestions',
    name: 'TestQuestions',
    component: Layout,
    meta: {
      title: '题库管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },
    // hidden: true,
    children: [
      {
        path: 'qbSubjectAdmin',
        name: 'QbSubjectAdmin',
        component: () => import('@/views/试题管理/学科管理/index.vue'),
        meta: {
          title: '学科管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbGradesAdmin',
        name: 'QbGradesAdmin',
        component: () => import('@/views/试题管理/学段管理/index.vue'),
        meta: {
          title: '学段管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbSourceCodes',
        name: 'QbSourceCodesAdmin',
        component: () => import('@/views/试题管理/资源code/index.vue'),
        meta: {
          title: '资源code管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbSource',
        name: 'QbSourceAdmin',
        component: () => import('@/views/试题管理/资源/index.vue'),
        meta: {
          title: '资源管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbTeachingMaterialAdmin',
        name: 'QbTeachingMaterialAdmin',
        component: () => import('@/views/试题管理/教材管理/index.vue'),
        meta: {
          title: '教材管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbKnowledgePointsAdmin',
        name: 'QbKnowledgePointsAdmin',
        component: () => import('@/views/试题管理/知识点管理/index.vue'),
        meta: {
          title: '知识点管理',
          icon: 'layout-column-fill',
        },
        props: (route) => ({
          query: route.query,
        }),
      },
      {
        path: 'qbTestQuestionsAdmin',
        name: 'QbTestQuestionsAdmin',
        component: () => import('@/views/试题管理/试题管理/index.vue'),
        meta: {
          title: '试题管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbFormATestPaperAdmin',
        name: 'QbFormATestPaperAdmin',
        component: () => import('@/views/试题管理/组卷模板管理/index.vue'),
        meta: {
          title: '组卷模板管理',
          icon: 'layout-column-fill',
        },
      },
    ],
  },
  {
    path: '/testPaper',
    name: 'TestPaper',
    component: Layout,
    meta: {
      title: '试卷管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },
    // hidden: true,
    children: [
      {
        path: 'qbSearchesAdmin',
        name: 'QbSearchesAdmin',
        component: () => import('@/views/试卷管理/搜索资源/index.vue'),
        meta: {
          title: '搜索资源管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbColumns',
        name: 'QbColumns',
        component: () => import('@/views/试卷管理/栏目管理/index.vue'),
        meta: {
          title: '栏目管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbExamPapers',
        name: 'QbExamPapers',
        component: () => import('@/views/试卷管理/试卷管理/index.vue'),
        meta: {
          title: '试卷管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'qbGrades',
        name: 'QbGrades',
        component: () => import('@/views/试卷管理/成绩等级管理/index.vue'),
        meta: {
          title: '成绩等级管理',
          icon: 'layout-column-fill',
        },
      }
    ],
  },
  {
    path: '/dictationAndReading',
    name: 'DictationAndReading',
    component: Layout,
    meta: {
      title: '听写跟读',
      icon: 'home-4-line',
    },
    // hidden: true,
    children: [
      {
        path: 'literacyGrades',
        name: 'LiteracyGradesAdmin',
        component: () => import('@/views/听写跟读/学段管理/index.vue'),
        meta: {
          title: '学段管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'literacySubject',
        name: 'LiteracySubjectAdmin',
        component: () => import('@/views/听写跟读/学科管理/index.vue'),
        meta: {
          title: '学科管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'literacyBooks',
        name: 'LiteracyBooksAdmin',
        component: () => import('@/views/听写跟读/书籍管理/index.vue'),
        meta: {
          title: '书籍管理',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'bookChineseDictation',
        name: 'BookChineseDictationAdmin',
        component: () => import('@/views/听写跟读/书籍语文听写/index.vue'),
        meta: {
          title: '书籍语文听写',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'bookEnglishDictation',
        name: 'BookEnglishDictationAdmin',
        component: () => import('@/views/听写跟读/书籍英语听写/index.vue'),
        meta: {
          title: '书籍英语听写',
          icon: 'layout-column-fill',
        },
      },
      {
        path: 'bookEnglishFollowReading',
        name: 'BookEnglishFollowReadingAdmin',
        component: () => import('@/views/听写跟读/书籍英语跟读/index.vue'),
        meta: {
          title: '书籍英语跟读',
          icon: 'layout-column-fill',
        },
      },
    ],
  },
  {
    path: '/fileUpload',
    name: 'FileUpload',
    component: Layout,
    meta: {
      title: '视频上传',
      icon: 'home-4-line',
    },
    hidden: true,
    children: [
      {
        path: '',
        name: 'FileUploadAdmin',
        component: () => import('@/views/视频上传/index.vue'),
        meta: {
          title: '视频上传',
          icon: 'home-2-line',
        },
      },
    ],
  },
  {
    path: '/lanmu',
    name: 'Lanmu',
    component: Layout,
    meta: {
      title: '栏目管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    // hidden: true,
    children: [
      {
        path: '',
        name: 'LanmuAdmin',
        component: () => import('@/views/栏目管理/index.vue'),
        meta: {
          title: '栏目管理',
          icon: 'layout-column-fill',
        },
      },
    ],
  },
  {
    path: '/album',
    name: 'Album',
    component: Layout,
    meta: {
      title: '专辑管理',
      icon: 'image-2-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: 'bigAlbum',
        name: 'BigAlbumAdmin',
        component: () => import('@/views/专辑管理/大专辑/index.vue'),
        meta: {
          title: '专辑包管理',
          icon: 'image-fill',
        },
      },
      {
        path: 'smallAlbum',
        name: 'SmallAlbumAdmin',
        component: () => import('@/views/专辑管理/小专辑/index.vue'),
        meta: {
          title: '视频专辑管理',
          icon: 'image-line',
        },
      },
    ],
  },

  {
    path: '/content',
    name: 'Content',
    component: Layout,
    meta: {
      title: '内容管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'ContentManage',
        component: () => import('@/views/内容管理/index.vue'),
        meta: {
          title: '内容管理',
          icon: 'play-list-add-line',
        },
      },
    ],
  },
  {
    path: '/grades',
    name: 'Grades',
    component: Layout,
    meta: {
      title: '学级管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'GradesAdmin',
        component: () => import('@/views/学级管理/index.vue'),
        meta: {
          title: '学级管理',
          icon: 'shopping-bag-line',
        },
      },
    ],
  },
  {
    path: '/subject',
    name: 'Subject',
    component: Layout,
    meta: {
      title: '学科管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'SubjectAdmin',
        component: () => import('@/views/学科管理/index.vue'),
        meta: {
          title: '学科管理',
          icon: 'book-read-line',
        },
      },
    ],
  },
  {
    path: '/cp',
    name: 'CP',
    component: Layout,
    meta: {
      title: '供应商管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'CpList',
        component: () => import('@/views/供应商管理/index.vue'),
        meta: {
          title: '供应商管理',
          icon: 'map-pin-user-fill',
        },
      },
    ],
  },
  {
    path: '/products',
    name: 'Products',
    component: Layout,
    meta: {
      title: '产品包管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },
    children: [
      {
        path: '',
        name: 'productsAdmin',
        component: () => import('@/views/产品包管理/index.vue'),
        meta: {
          title: '产品包管理',
          icon: 'user-line',
        },
      },
    ],
  },
  {
    path: '/user',
    name: 'User',
    component: Layout,
    meta: {
      title: '管理员管理',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },

    children: [
      {
        path: '',
        name: 'UserAdmin',
        component: () => import('@/views/用户管理/index.vue'),
        meta: {
          title: '管理员管理',
          icon: 'user-line',
        },
      },
    ],
  },
  {
    path: '/client',
    name: 'Client',
    component: Layout,
    meta: {
      title: '客户端用户',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },
    children: [
      {
        path: '',
        name: 'clientAdmin',
        component: () => import('@/views/客户端用户管理/index.vue'),
        meta: {
          title: '客户端用户',
          icon: 'user-line',
        },
      },
    ],
  },
  {
    path: '/operationRecords',
    name: 'OperationRecords',
    component: Layout,
    meta: {
      title: '操作日志',
      icon: 'home-4-line',
      roles: ['administrator', 'admin.normal'],
    },
    children: [
      {
        path: '',
        name: 'OperationRecordsAdmin',
        component: () => import('@/views/操作日志/index.vue'),
        meta: {
          title: '操作日志',
          icon: 'user-line',
        },
      },
    ],
  },
  {
    path: '/vab',
    name: 'Vab',
    component: Layout,
    redirect: '/vab/table',
    meta: {
      title: '组件',
      icon: 'apps-line',
    },
    hidden: true,
    children: [
      {
        path: 'table',
        name: 'Table',
        component: () => import('@/views/vab/table'),
        meta: {
          title: '表格',
          icon: 'table-2',
        },
      },
      {
        path: 'icon',
        name: 'Icon',
        component: () => import('@/views/vab/icon'),
        meta: {
          title: '图标',
          icon: 'remixicon-line',
        },
      },
    ],
  },
  {
    path: '/error',
    name: 'Error',
    component: Layout,
    redirect: '/error/403',
    hidden: true,
    meta: {
      title: '错误页',
      icon: 'error-warning-line',
    },
    children: [
      {
        path: '403',
        name: 'Error403',
        component: () => import('@/views/403.vue'),
        meta: {
          title: '403',
          icon: 'error-warning-line',
        },
      },
      {
        path: '404',
        name: 'Error404',
        component: () => import('@/views/404.vue'),
        meta: {
          title: '404',
          icon: 'error-warning-line',
        },
      },
    ],
  },
  {
    path: '/refresh',
    name: 'Refresh',
    component: () => import('@/views/refresh.vue'),
    meta: {
      title: '刷新页面',
      icon: 'donut-chart-line',
    },
    hidden: true,
  },
  {
    path: '/:all*',
    name: 'All',
    redirect: '/404',
    meta: {
      title: '404',
      icon: 'error-warning-line',
    },
    hidden: true,
  },
]
const router = createRouter({
  // history: createWebHashHistory(),
  history: createWebHistory(),
  routes: constantRoutes,
})

export default router
