@use "./normalize.less";

html {
  body {

    * {
      box-sizing: border-box;
      font-family: PingFang SC, Arial, Microsoft YaHei, sans-serif;
      font-size: 14px;
    }

    /* ant-input-search搜索框 */
    .ant-input-search {
      max-width: 250px;
    }

    /* ant-pagination分页 */
    .ant-pagination {
      margin-top: @vab-margin;
      text-align: center;

      &.ant-table-pagination {
        float: none !important;
        margin-top: @vab-margin;
      }

    }


  }
}
