{
  "compilerOptions": {
    "target": "es5",
    "module": "esnext",
    "strict": true,
    "jsx": "preserve",
    "importHelpers": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "downlevelIteration": true,
    "skipLibCheck": true,
    "esModuleInterop": true, // 允许export=导出，由import from 导入
    "allowJs": true, // 允许编译器编译JS，JSX文件
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "useDefineForClassFields": true,
    // 对修饰器的实验支持功能在将来的版本中可能更改。在 "tsconfig" 或 "jsconfig" 中设置 "experimentalDecorators" 选项以删除此警告。
    "experimentalDecorators": true,
    "sourceMap": true,
    "baseUrl": "./",
    "outDir": "./dist",
    "types": ["webpack-env"],
    "paths": {
      "~/*": ["*"],
      "@/*": ["src/*"],
      "#/*": ["types/*"]
    },
    "lib": ["esnext", "dom", "dom.iterable", "scripthost"]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.vue",
    "types/*.d.ts",
    "types/**/*.d.ts",
    "src/router/*.ts"
  ],
  "exclude": ["node_modules", "dist/**/*"]
}
