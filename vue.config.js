/**
 * <AUTHOR> @description vue.config.js全局配置
 */
const path = require('path')
const {
  /* baseURL, */
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  transpileDependencies,
  title,
  abbreviation,
  devPort,
  providePlugin,
  build7z,
} = require('./src/config')
const TerserPlugin = require('terser-webpack-plugin')
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin') //引入gzip压缩插件
const { version, author } = require('./package.json')
const Webpack = require('webpack')
const WebpackBar = require('webpackbar')
const FileManagerPlugin = require('filemanager-webpack-plugin')
const dayjs = require('dayjs')
const date = dayjs().format('YYYY_M_D')
const time = dayjs().format('YYYY-M-D HH:mm:ss')
process.env.VUE_APP_TITLE = title || 'vue-admin-beautiful'
process.env.VUE_APP_AUTHOR = author || 'FJF'
process.env.VUE_APP_UPDATE_TIME = time
process.env.VUE_APP_VERSION = version
process.env.VUE_APP_DATA_URL_PXWH = ''


const resolve = (dir) => {
  return path.join(__dirname, dir)
}
const mockServer = () => {
  if (process.env.NODE_ENV === 'development') {
    return require('./mock/mockServer.js')
  }
  return ''
}
module.exports = {
  publicPath,
  assetsDir,
  outputDir,
  lintOnSave,
  transpileDependencies,
  devServer: {
    hot: true,
    port: devPort,
    open: true,
    noInfo: false,
    overlay: {
      warnings: true,
      errors: true,
    },
    // 注释掉的地方是前端配置代理访问后端的示例
    // proxy: {
    //   ['/location/ip']: {
    //     target: `http://api.map.baidu.com`,
    //     ws: true,
    //     changeOrigin: true,
    //     // pathRewrite: {
    //     //   ['^/' + '/location/ip']: '',
    //     // },
    //   },
    // },
    proxy: {
      "/api": {
        target: "http://**************:28110", // 测试环境
        changeOrigin: true,
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, "/api")
      },
    },
    // after: mockServer(),
  },
  configureWebpack() {
    return {
      optimization: {
        splitChunks: {
          chunks: 'all',
        },
        // 是否要启用压缩，默认情况下，生产环境会自动开启
        minimize: process.env.NODE_ENV === 'development' ? false : true,
        minimizer: [
          // 压缩时使用的插件
          // 压缩js文件
          new TerserPlugin({
            parallel: true, // 开启多线程压缩
          }),
          // 压缩css文件
          new OptimizeCSSAssetsPlugin(),
        ],
      },
      resolve: {
        alias: {
          '~': resolve('.'),
          // '@': resolve(__dirname, 'src'),
          '@': resolve('src'),
          '*': resolve(''),
          '#': resolve('types'),
        },
        fallback: {
          "fs": false,
          "path": false,
          "crypto": false
        }
      },
      plugins: [
        new Webpack.ProvidePlugin(providePlugin),
        new WebpackBar({
          name: 'video_education_frontend_m',
        }),
        new Webpack.ProvidePlugin({
          $: 'jquery',
          jQuery: 'jquery',
          'windows.jQuery': 'jquery',
        }),
        new CompressionPlugin({
          //此插件不能使用太高的版本，否则报错：TypeError: Cannot read property 'tapPromise' of undefined
          // filename: "[path][base].gz", // 这种方式是默认的，多个文件压缩就有多个.gz文件，建议使用下方的写法
          filename: '[path][base].gz', //  '[path].gz[query]'使得多个.gz文件合并成一个文件，这种方式压缩后的文件少，建议使用
          algorithm: 'gzip', // 官方默认压缩算法也是gzip
          test: /\.js$|\.css$|\.html$|\.ttf$|\.eot$|\.woff$|\.otf$/, // 使用正则给匹配到的文件做压缩，这里是给html、css、js以及字体（.ttf和.woff和.eot）做压缩
          threshold: 10240, //以字节为单位压缩超过此大小的文件，使用默认值10240吧
          minRatio: 0.8, // 最小压缩比率，官方默认0.8
          //是否删除原有静态资源文件，即只保留压缩后的.gz文件，建议这个置为false，还保留源文件。以防：
          // 假如出现访问.gz文件访问不到的时候，还可以访问源文件双重保障
          deleteOriginalAssets: false,
        }),
      ],
    }
  },
  chainWebpack(config) {
    config.resolve.symlinks(true)
    config.module.rule('svg').exclude.add(resolve('src/icon/remixIcon')).end()
    config.plugin('html').tap((args) => {
      args[0].title = title
      return args
    })
    config.module
      .rule('remixIcon')
      .test(/\.svg$/)
      .include.add(resolve('src/icon/remixIcon'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({ symbolId: 'remix-icon-[name]' })
      .end()

    config.when(process.env.NODE_ENV === 'development', (config) => {
      config.devtool('source-map')
    })

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config.performance.set('hints', false)
      config.devtool('none')
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'vue-admin-beautiful-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial',
          },
        },
      })
      config
        .plugin('banner')
        .use(Webpack.BannerPlugin, [`${time}`])
        .end()
    })

    if (build7z) {
      config.when(process.env.NODE_ENV === 'production', (config) => {
        config
          .plugin('fileManager')
          .use(FileManagerPlugin, [
            {
              onEnd: {
                delete: [`./${outputDir}/video`, `./${outputDir}/data`],
                archive: [
                  {
                    source: `./${outputDir}`,
                    destination: `./${outputDir}/${abbreviation}_${outputDir}_${date}.7z`,
                  },
                ],
              },
            },
          ])
          .end()
      })
    }
  },
  runtimeCompiler: true,
  productionSourceMap: false,
  css: {
    requireModuleExtension: true,
    sourceMap: true,
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
          modifyVars: {
            'vab-color-blue': '#1890ff',
            'vab-margin': '20px',
            'vab-padding': '20px',
            'vab-header-height': '65px',
          },
        },
      },
      // 默认情况下 `sass` 选项会同时对 `sass` 和 `scss` 语法同时生效
      // 因为 `scss` 语法在内部也是由 sass-loader 处理的
      // 但是在配置 `prependData` 选项的时候
      // `scss` 语法会要求语句结尾必须有分号，`sass` 则要求必须没有分号
      // 在这种情况下，我们可以使用 `scss` 选项，对 `scss` 语法进行单独配置
      // 注意：在 sass-loader v8 中，这个选项名是 "prependData"
      scss: {
        prependData: `@use "@/styles/main.scss";`,
      },
    },
  },
  pwa: {
    workboxOptions: {
      // https://developer.chrome.com/docs/workbox/modules/workbox-webpack-plugin/
      skipWaiting: true,
      clientsClaim: true,
      importWorkboxFrom: 'local',
      importsDirectory: 'js',
      navigateFallback: '/',
      navigateFallbackBlacklist: [/\/api\//],
    },
  },
}
