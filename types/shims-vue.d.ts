import { AxiosRequestConfig } from 'axios'

/* eslint-disable */
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module '*.json' {
  const value: any
  export default value
}

// 处理  类型“AxiosResponse<any, any>”
declare module 'axios' {
  interface AxiosResponse<T = any> {
    code: number
    message: string
  }
  export function create(config?: AxiosRequestConfig): AxiosInstance
}
