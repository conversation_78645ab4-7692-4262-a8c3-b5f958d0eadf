import type { RouteRecordRaw } from 'vue-router'

/**
 * 路由记录
 */
declare interface VabRouteRecord
  // 在RouteRecordRaw去除name meta children
  extends Omit<RouteRecordRaw, 'name' | 'meta' | 'children'> {
  name: string
  meta?: VabRouteMeta
  fullPath?: string
  children?: VabRouteRecord[]
  hidden?: boolean
  path?: string
  redirect?: string
  query?: {}
  params?: {}
  matched?: string[]
}
declare interface VabRouteMeta {
  title?: string
  icon?: string
  hidden?: boolean
  affix?: boolean
  roles?: string[]
}
